import { CommonModule } from '@angular/common';
import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';

@Component({
  selector: 'app-details-platform-variables',
  standalone: true,
  imports: [
  CommonModule,
  TranslateModule,
  MatIconModule,
  MatDialogModule
  ],
  templateUrl: './details-platform-variables.component.html',
  styleUrl: './details-platform-variables.component.css'
})
export class DetailsPlatformVariablesComponent {

  constructor(
    public dialogRef: MatDialogRef<DetailsPlatformVariablesComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) {}

  onClose(): void {
    this.dialogRef.close();
  }
}
