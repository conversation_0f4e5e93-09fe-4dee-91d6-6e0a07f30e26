.wrapper {
  margin: 10px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  height: 100vh;
  max-height: 90vh;
  overflow: hidden;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.form-container {
  flex: 1;
  margin-right: 20px; /* Adjust as needed for spacing */
}

.map-container {
  display: flex;
  align-items: flex-start; /* Align the map to the top */
  flex-direction: column;

}

.border-points-container {
  max-height: 400px; /* Adjust this to fit your needs */
  overflow-y: auto; /* Makes the container scrollable */
  scroll-behavior: smooth; /* Smooth scroll */
}

.highlighted-marker {
  background-color: #e0f7fa; /* Light cyan background */
  border-left: 4px solid var(--primary-color); /* Darker teal left border */
  transition: background-color 0.3s ease; /* Smooth transition */
}

.scrollable-container{
  max-height: 500px; /* Set your desired height */
  overflow-y: auto;  /* Enable vertical scrolling */
  padding: 10px; /* Optional: Adds some padding */
  border-radius: 4px; /* Optional: Rounded corners */
  width: 70%;
}

.data-button-div{
  display: flex;
  margin-bottom: -5%;
}

.data-div{
display: flex;
margin-right: -7%;
}

.delete-button{
  border: 0;
  background-color: transparent; 
  height: 50px;
  color: red;
}

.separator {
  border-bottom: 1px solid #ccc; /* Adds a bottom border for separation */
  padding: 16px 0; /* Adds vertical padding */
  margin-bottom: 16px; /* Adds space between the rows */
}

.close-icon{
  cursor: pointer;
  font-size: 35px;
  border: none;
  background-color: transparent;
}

.save-button{
  width: 135px;
  height: 40px;
  gap: 0px;
  border-radius: 5px;
  opacity: 0px;
  background-color: #383838 !important;
  color: white !important;
  box-shadow: 0px 1px 3px 0px #0000004D;
  box-shadow: 0px 4px 8px 3px #00000026;
  margin-left: 4%;
}

.details-report-content-row{
  display: flex;
  width: 100%; /* Ensure it takes full width */
  justify-content: space-between; /* Space between elements */
}

.information{
  width: 100%;
  height: 25px;
  font-size: 15px;
  font-weight: 700;
  line-height: 24px;
  color: var(--Dark-color---Smart-Lighting, #222222);
}

.icon{
  vertical-align: -5px;
  width: 16.5px;
  padding-right: 30px;
  color: #FBBC05;
}

.p{
  width: 12rem;
  height: 24px;
  font-size: 16px;
  font-weight: 400;
  line-height: 24px;
  color: #000000;
  margin-left: 29px;
}

/* Optional: You can adjust the colors and sizes as needed */
.details-report-content-row {
  /* Optional styles to enhance visual appeal */
  background-color: #f9f9f9; /* Light background for better visibility */
  border-radius: 4px; /* Rounded corners */
  transition: background-color 0.3s; /* Smooth background transition on hover */
}

.details-report-content-row:hover {
  background-color: #eaeaea; /* Darker background on hover */
}



.map {
  width: 100%;
  height: 100%;
  box-shadow: 0px 4px 4px 0px #00000040;
}

.search{
display: flex;
align-items: center;
margin-bottom: 20px;
}

google-map div.map-container{
height: 722px;
}

.search-input{
height: 40px;
width: 305px;
border-radius: 2px;
border: 1px ; /* Added border for clarity */
}

.search-button {
  background-color: #383838;
  color: white;
  width: 80px;
  height: 44px; /* Match input height */
  margin-top: 3px;
  margin-left: 10px;
  border: none; /* Remove border */
  border-radius: 4px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  cursor: pointer;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between; /* Align items to the start */
  height: 63px;
  width: 100%; /* Use full width */
}

.header button {
  width: 100px;
  height: 40px;
  border-radius: 5px;
  margin-left: 10px;
  margin-right: 10px;
  box-shadow: 0px 1px 3px 0px #0000004D;
  box-shadow: 0px 4px 8px 3px #00000026;

}

.h1 {
  size: 20px;
  color: var(--primary-color);
  font-weight: bold;
}

.filter-container {
  align-items: center;
}

.wide-dropdown {
  width: 220px;
  margin-right: 10px;
}

.mat-form-field {
  width: 220px;
  margin-right: 10px;
}



.date,
.long-fields,
.priority {
width: calc(33% - 10px); /* Three fields per row with spacing between */
}

.date {
width: 300px;
border-radius: 2px;
border: 1px;
margin-right: 10px;
}

.mat-form-fields {
width: 300px;
border: 1px;
margin-right: 10px;
}

.priority {
  width: 167px;
  border-radius: 2px;
  border: 1px;
  margin-right: 10px;
}

.left-big-border {
  height: 33px;
}

.buttons{
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}

.save-button{
  width: 135px;
  height: 40px;
  gap: 0px;
  border-radius: 5px;
  opacity: 0px;
  background-color: #383838 !important;
  color: white !important;
  box-shadow: 0px 1px 3px 0px #0000004D;
  box-shadow: 0px 4px 8px 3px #00000026;
  margin-right: 10px;
}


.map-fields{
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}

.map-form-fields{
width: 15rem;
border: 1px;
margin-right: 10px;
}

#map {
  height: 70vh;
  width: 100%;
  box-sizing: border-box;
}


.mat-mdc-text-field-wrapper {
  height: 0px;
  flex: auto;
  will-change: auto;
}

.main-content {
  display: flex;
  padding-top: 23px;
  padding-left: 40px;
  width: 100%; /* Use full width */
  justify-content: space-between;
  overflow: hidden; /* Prevent main content overflow */
}

.map-container {
  display: flex;
  width: 100%;
}

.map {
  display: flex;
  width: 100%;
}


/* Adjust text field wrapper height */
:host ::ng-deep .mat-mdc-text-field-wrapper {
  height: 44px;
}

:host ::ng-deep .mdc-text-field--outlined .mat-mdc-form-field-infix,
:host ::ng-deep .mdc-text-field--no-label .mat-mdc-form-field-infix {
  padding-top: 12px;
}

/* Responsive adjustments */
@media screen and (max-width: 768px) {
  .wrapper {
    overflow-y: auto;
    margin: 10px 5px; /* Adjusted margin for better spacing */
  }

  .header {
    width: 100%;
    padding: 10px;
  }

  .h1 {
    margin-left: 0px;
    font-size: 18px; /* Slightly smaller font size for smaller screens */
    margin-top: 10px; /* Space between button and heading */
  }

  .main-content {
    flex-direction: column;
    padding: 10px; /* Added padding for better spacing */
    width: 100%;
  }

  .filter-container {
    flex-direction: column;
    align-items: stretch;
    width: 200px;
    margin-bottom: 10px; /* Added margin to separate sections */
  }

  .wide-dropdown,
.mat-form-field,
.date,
.long-fields,
.description,
.search,
.priority {
width: 100%;
margin-right: 0;
margin-bottom: 10px; /* Added margin to separate elements */
}

}