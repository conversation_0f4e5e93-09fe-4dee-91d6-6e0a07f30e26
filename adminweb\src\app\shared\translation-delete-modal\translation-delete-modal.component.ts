import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';

@Component({
  selector: 'app-translation-delete-modal',
  standalone: true,
  imports: [
    TranslateModule,
    MatIconModule,
    MatDialogModule
  ],
  templateUrl: './translation-delete-modal.component.html',
  styleUrl: './translation-delete-modal.component.css'
})
export class TranslationDeleteModalComponent {
  constructor(
    public dialogRef: MatDialogRef<TranslationDeleteModalComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) { }

  onNoClick(): void {
    this.dialogRef.close();
  }

  onYesClick(): void {
    this.dialogRef.close(true); // Потвърждение за изтриване
  }
}
