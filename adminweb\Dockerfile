# Use Node.js as the base image
FROM node:18 AS build

# Set the working directory
WORKDIR /app

# Copy package.json and package-lock.json
COPY package*.json ./

# Install dependencies
RUN npm install --legacy-peer-deps

# Install Angular CLI globally
RUN npm install -g @angular/cli

# Copy the rest of the application source code
COPY . .

# Accept build argument for Angular environment
ARG ANGULAR_ENVIRONMENT=staging

# Build the Angular application with the specified environment
RUN ng build --configuration=${ANGULAR_ENVIRONMENT}

# Use Caddy as the static file server
FROM caddy:2.7.4-alpine

# Copy Angular build files from the previous stage
COPY --from=build /app/dist/adminweb/browser /srv

# Expose port 4200 for Traefik to route traffic
EXPOSE 4200

COPY Caddyfile /etc/caddy/Caddyfile

# Run Caddy with a custom configuration
CMD ["caddy", "run", "--config", "/etc/caddy/Caddyfile"]
