import { ComponentFixture, TestBed } from '@angular/core/testing';

import { DeletePlatformVariablesComponent } from './delete-platform-variables.component';

describe('DeletePlatformVariablesComponent', () => {
  let component: DeletePlatformVariablesComponent;
  let fixture: ComponentFixture<DeletePlatformVariablesComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [DeletePlatformVariablesComponent]
    })
    .compileComponents();

    fixture = TestBed.createComponent(DeletePlatformVariablesComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
