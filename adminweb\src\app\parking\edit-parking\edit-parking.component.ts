import { CommonModule } from '@angular/common';
import { ChangeDete<PERSON><PERSON><PERSON>, <PERSON>mponent, <PERSON>ement<PERSON>ef, OnDestroy, OnInit, QueryList, ViewChild, ViewChildren, ViewContainerRef } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatDialogModule } from '@angular/material/dialog';
import { MatDividerModule } from '@angular/material/divider';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { GoogleMapsModule } from '@angular/google-maps';
import { ActivatedRoute, Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { GoogleMapsService } from '../../core/services/google.maps.service';
import { MapGetModel } from '../../shared/interfaces/map/map-get.model';
import { environment } from '../../../environments/enviroment';
import { ParkingService } from '../../core/services/parking.service';
import { ParkngSimpleBorderPoint } from '../../shared/interfaces/parking/parking-simple-border-point.model';
import { EditParkingModel } from '../../shared/interfaces/parking/parking-edit.model';
import { MessageService } from '../../core/services/message.service';
import { MatFormFieldModule } from '@angular/material/form-field';
import { FormsModule } from '@angular/forms';
import { MatInputModule } from '@angular/material/input';

@Component({
  selector: 'app-edit-parking',
  standalone: true,
  imports: [
    TranslateModule,
    MatIconModule,
    MatDialogModule,
    GoogleMapsModule,
    CommonModule,
    MatDividerModule,
    MatButtonModule,
    MatFormFieldModule,
    FormsModule,
    MatInputModule
  ],
  templateUrl: './edit-parking.component.html',
  styleUrl: './edit-parking.component.css'
})
export class EditParkingComponent implements OnInit, OnDestroy {
  @ViewChild('mapContainer') mapContainer!: ElementRef;
  @ViewChild('markerContainer', { read: ViewContainerRef, static: true }) markerContainer!: ViewContainerRef;
  @ViewChildren('borderPointRow') borderPointRows!: QueryList<ElementRef>;

  constructor(
    private googleMapsService: GoogleMapsService,
    private route: ActivatedRoute,
    private router: Router,
    private parkingService: ParkingService,
    private changeDetectorRef: ChangeDetectorRef,
    private messageService: MessageService
  ) {}

  protected data: { name: string, radius: number | string | null, borderPoints: any[] } = { name: '', radius: null, borderPoints: [] };
  protected mapHeight: number = 0;
  protected mapWidth: number = 0;
  protected center!: google.maps.LatLngLiteral;
  protected zoom = 13;
  private map: google.maps.Map | undefined;
  private dayMapId = environment.dayMapId;
  //private aggregatedGroupsData: AggregatedGroupModel[] = [];
  private marker: google.maps.marker.AdvancedMarkerElement | undefined;
  private currentMarker: any = null;
  protected clickedCoordinates: { latitude: number, longitude: number, index: number}[] = [];
  private currentMarkers: google.maps.marker.AdvancedMarkerElement[] = [];
  private polyline: google.maps.Polyline | undefined;
  private markerPositions: { latitude: number; longitude: number }[] = [];
  protected parkingId!: number;
  protected highlightedIndex: number | null = null;
  protected parkingRadius!: number;
  protected firstMarkerIcon = {
    url: 'assets/images/end-point-marker.svg',
  };
  protected lastMarkerIcon = {
    url: 'assets/images/start-point-marker.svg',
  };
  private isFirstTime = true;

  private parkingDataSubscription!: Subscription;

 async ngOnInit() {
  this.route.queryParamMap.subscribe((queryParams) => {
    const id = queryParams.get('id');
    console.log(id);
    this.parkingId = Number(id);
    this.parkingRadius = Number(queryParams.get('radius'));

    const latitude = queryParams.get('latitude');
    const longitude = queryParams.get('longitude');

    // Get border points from query parameters
    const borderPointsParam = queryParams.get('borderPoints');
    console.log('borderPointsParam:', borderPointsParam);

    // Handle case where borderPoints is empty but we have latitude and longitude
    if (borderPointsParam === '[]' && latitude && longitude) {
      console.log('Creating single border point from lat/lng coordinates');
      
      // Create a single border point from the latitude and longitude
      const singleBorderPoint = {
        index: 0,
        latitude: Number(latitude),
        longitude: Number(longitude),
        parkingLotId: this.parkingId // Include parking lot ID if needed
      };

      // Set up data with the single point
      this.data = {
        name: queryParams.get('name') || '',
        radius: queryParams.get('radius'),
        borderPoints: [singleBorderPoint]
      };

      // Set center from the single point
      this.center = {
        lat: Number(latitude),
        lng: Number(longitude)
      };

      // Map to clicked coordinates
      this.clickedCoordinates = [{
        latitude: Number(latitude),
        longitude: Number(longitude),
        index: 0
      }];

      console.log('Single border point created:', singleBorderPoint);
      console.log('Clicked coordinates:', this.clickedCoordinates);

      // Initialize Google Maps and exit early
      this.initializeGoogleMaps().then(() => {
        //this.getMapData();
      });
      return; // Exit early since we've handled this case
    }

    if (borderPointsParam && borderPointsParam !== '[]') {
      try {
        // Parse border points from query params
        const borderPoints = JSON.parse(borderPointsParam);
        console.log('Parsed borderPoints:', borderPoints);
        
        if (Array.isArray(borderPoints) && borderPoints.length > 0) {
          // Use border points from query params
          this.data = {
            name: queryParams.get('name') || '',
            radius: queryParams.get('radius'),
            borderPoints: borderPoints
          };
          
          // Set center from first border point
          this.center = {
            lat: borderPoints[0].latitude,
            lng: borderPoints[0].longitude
          };
          
          // Map border points to clicked coordinates
          this.clickedCoordinates = borderPoints.map(point => ({
            latitude: point.latitude,
            longitude: point.longitude,
            index: point.index
          }));
          
          console.log('Border points loaded from query params:', borderPoints);
          console.log('Clicked coordinates:', this.clickedCoordinates);
          
        } else {
          console.warn('Border points array is empty or invalid');
          this.handleMissingBorderPoints(queryParams);
        }
        
      } catch (error) {
        console.error('Error parsing border points from query params:', error);
        this.handleMissingBorderPoints(queryParams);
      }
      
    } else {
      console.warn('No border points found in query parameters');
      this.handleMissingBorderPoints(queryParams);
    }
    
    // Initialize Google Maps after setting up the data
    this.initializeGoogleMaps().then(() => {
      //this.getMapData();
    });
  });
}

// ========================================
// ALTERNATIVE: More Comprehensive Approach
// ========================================

// If you want to handle multiple scenarios more elegantly:
async ngOnInitAlternative() {
  this.route.queryParamMap.subscribe((queryParams) => {
    const id = queryParams.get('id');
    this.parkingId = Number(id);
    this.parkingRadius = Number(queryParams.get('radius'));

    const latitude = queryParams.get('latitude');
    const longitude = queryParams.get('longitude');
    const borderPointsParam = queryParams.get('borderPoints');

    console.log('Query params:', { id, latitude, longitude, borderPointsParam });

    let borderPoints: any[] = [];

    // Try to get border points from different sources
    if (borderPointsParam && borderPointsParam !== '[]') {
      try {
        borderPoints = JSON.parse(borderPointsParam);
        console.log('Border points from query params:', borderPoints);
      } catch (error) {
        console.error('Error parsing border points:', error);
        borderPoints = [];
      }
    }

    // If no border points but we have lat/lng, create a single point
    if (borderPoints.length === 0 && latitude && longitude) {
      borderPoints = [{
        index: 0,
        latitude: Number(latitude),
        longitude: Number(longitude),
        parkingLotId: this.parkingId
      }];
      console.log('Created single border point from lat/lng:', borderPoints[0]);
    }

    // Set up the data based on what we have
    if (borderPoints.length > 0) {
      this.setupParkingData(borderPoints, queryParams);
    } else {
      console.warn('No valid border points or coordinates found');
      this.handleMissingBorderPoints(queryParams);
    }
    
    // Initialize Google Maps
    this.initializeGoogleMaps().then(() => {
      //this.getMapData();
    });
  });
}

// Helper method to set up parking data
private setupParkingData(borderPoints: any[], queryParams: any): void {
  this.data = {
    name: queryParams.get('name'),
    radius: queryParams.get('radius'),
    borderPoints: borderPoints
  };

  // Set center from first border point
  this.center = {
    lat: borderPoints[0].latitude,
    lng: borderPoints[0].longitude
  };

  // Map border points to clicked coordinates
  this.clickedCoordinates = borderPoints.map((point, index) => ({
    latitude: point.latitude,
    longitude: point.longitude,
    index: point.index !== undefined ? point.index : index // Fallback to array index
  }));

  console.log('Parking data setup complete:');
  console.log('- Border points:', borderPoints);
  console.log('- Center:', this.center);
  console.log('- Clicked coordinates:', this.clickedCoordinates);
}

// ========================================
// UPDATE YOUR editElement TO HANDLE SINGLE POINTS
// ========================================

// Update your editElement method to handle both cases:
editElement(element: any): void {
  console.log('Editing element:', element);
  
  let borderPointsParam: string;
  
  // Handle case where element might not have borderPoints or has empty borderPoints
  if (!element.borderPoints || element.borderPoints.length === 0) {
    borderPointsParam = '[]'; // Send empty array
  } else {
    borderPointsParam = JSON.stringify(element.borderPoints);
  }
  
  // Navigate to parking edit route
  this.router.navigate(['/parking/parking-edit', element.id], {
    queryParams: { 
      id: element.id,
      borderPoints: borderPointsParam,
      radius: element.radius || null,
      latitude: element.latitude || null,
      longitude: element.longitude || null
    }
  });
  
  console.log('Navigation params:', {
    id: element.id,
    borderPoints: borderPointsParam,
    latitude: element.latitude,
    longitude: element.longitude
  });
}

private handleMissingBorderPoints(queryParams: any): void {
  // Set default center from latitude/longitude params if available
  const lat = queryParams.get('latitude');
  const lng = queryParams.get('longitude');
  
  if (lat && lng) {
    this.center = { 
      lat: Number(lat), 
      lng: Number(lng) 
    };
    console.log('Using fallback center coordinates:', this.center);
  } else {
    // Default center (you can set this to your city center or a default location)
    this.center = {
      lat: 42.482798,  // Default to Yambol coordinates
      lng: 26.503206
    };
    console.log('Using default center coordinates:', this.center);
  }
  
  // Initialize empty data
  this.data = {
    name: '',
    radius: queryParams.get('radius') || null,
    borderPoints: []
  };
  
  this.clickedCoordinates = [];
}

  private updatePolylinePath() {
    if (this.polyline) {
      const path = this.clickedCoordinates.map(coord => new google.maps.LatLng(coord.latitude, coord.longitude));
      this.polyline.setPath(path);
    }
  }

  private scrollToHighlighted() {
    
    if (this.highlightedIndex !== null) {
      const element = this.borderPointRows.toArray()[this.highlightedIndex].nativeElement;
      element.scrollIntoView({ behavior: 'smooth', block: 'nearest', inline: 'start' });
      this.changeDetectorRef.detectChanges();
    }
      
  }

  addBorderPoints() {
    const midpoint = this.googleMapsService.calculateMidpoint(
      this.clickedCoordinates.map(coord => ({ lat: coord.latitude, lng: coord.longitude }))
  );

  
  console.log(midpoint)
  
    const borderPoints: ParkngSimpleBorderPoint[] = this.clickedCoordinates.map(coord => ({
            index: coord.index,
            latitude: coord.latitude,
            longitude: coord.longitude
          }));
    
          const editParkingModel: EditParkingModel = {
            name: this.data.name,
            id: this.parkingId,
            borderPoints: borderPoints
          };
    
        this.parkingService.updateParking(editParkingModel).subscribe({
          next: () => {
            this.messageService.showMessage(["CreateSuccessfully"], 'success');
            this.router.navigate(['/parking']);
          },
          error: (err) => {
            console.error('Error adding border points:', err);
          }
        });
  }

  deleteBorderPoint(index: number) {
    console.log(this.clickedCoordinates)
    const removedPoint = this.clickedCoordinates.splice(index, 1)[0];
    console.log(this.clickedCoordinates)

    const markerIndex = this.markerPositions.findIndex(position =>
      position.latitude === removedPoint.latitude && position.longitude === removedPoint.longitude
    );

    if (markerIndex > -1) {
      this.currentMarkers[markerIndex].map = null;  
      this.currentMarkers.splice(markerIndex, 1);
      this.markerPositions.splice(markerIndex, 1);
    }

    // Update indexes of remaining points and markers
    this.clickedCoordinates.forEach((coord, i) => {
      coord.index = i; // Realign indexes
    });


    if (this.highlightedIndex === index) {
      this.highlightedIndex = null;
    } else if (this.highlightedIndex !== null && this.highlightedIndex > index) {
      this.highlightedIndex--;
    }

    this.changeDetectorRef.detectChanges();

    this.updatePolylinePath();
  }

  async initializeGoogleMaps() {
    try {
      await this.googleMapsService.loadLibraries();

      this.center = {
        lat: 42.482798, 
        lng: 26.503206 

      };

      this.map = await this.googleMapsService.initializeMap(
        this.mapContainer.nativeElement,
        this.center,
        this.zoom,
        this.dayMapId,
        'day'
      );

      this.polyline = new google.maps.Polyline({
        map: this.map,
        path: [],
        strokeColor: '#800080',
        strokeOpacity: 1.0,
        strokeWeight: 2,
      });

      this.clickedCoordinates.forEach((points: { latitude: number; longitude: number }, index: number) => {
        let pointsGoogle: google.maps.LatLngLiteral = { lat: points.latitude, lng: points.longitude };

        let iconUrl: string | undefined = undefined;
        if (index === 0) {
          iconUrl = this.firstMarkerIcon.url;
        } else if (index === this.clickedCoordinates.length - 1 && index !== 0) {
          iconUrl = this.lastMarkerIcon.url;
        }

        const newMarker = this.googleMapsService.addAdvancedMarker(
          pointsGoogle,
          index,
          undefined,
          undefined,
          iconUrl,
          undefined,
          true
        );

        newMarker.addListener('click', () => {
          this.onMarkerClick(points.latitude, points.longitude);
        });

        this.currentMarkers.unshift(newMarker); // Using push here
        this.markerPositions.unshift({ latitude: points.latitude, longitude: points.longitude });
      });

      this.updatePolylinePath();

      this.map.addListener('click', (event: google.maps.MapMouseEvent) => {
        if (!event.latLng) return; // Ensure the click event has coordinates
      
        const clickedPosition: google.maps.LatLngLiteral = {
          lat: event.latLng.lat(),
          lng: event.latLng.lng(),
        };
      
        if (this.currentMarkers.length > 0) {
          if (this.isFirstTime) {
            // First time: Remove the last marker
            const lastMarker = this.currentMarkers[this.currentMarkers.length - 1];
            this.removeMarker(lastMarker);
            this.isFirstTime = false;
          } else {
            // Subsequent times: Remove the first marker
            const firstMarker = this.currentMarkers[0];
            this.removeMarker(firstMarker);
          }
        }
      
        // Create new marker at clicked position
        this.currentMarker = this.createMarker(clickedPosition, this.firstMarkerIcon.url);
      
        // Store marker data
        this.currentMarkers.unshift(this.currentMarker);
        this.markerPositions.unshift({ latitude: clickedPosition.lat, longitude: clickedPosition.lng });
      
        this.clickedCoordinates.unshift({
          latitude: clickedPosition.lat,
          longitude: clickedPosition.lng,
          index: 0
        });
      
        // Update indexes
        this.clickedCoordinates.forEach((coord, i) => {
          coord.index = i;
        });
  
        this.changeDetectorRef.detectChanges();
      
        // Add click listener to marker
        this.currentMarker.addListener('click', () => {
          this.onMarkerClick(clickedPosition.lat, clickedPosition.lng);
        });
      
        // Update polyline if applicable
        this.updatePolylinePath();
      });
      
    } catch (error) {
      console.error('Error loading Google Maps:', error);
    }
  }

  onMarkerClick(latitude: number, longitude: number) {
    const index = this.clickedCoordinates.findIndex(point =>
        point.latitude === latitude && point.longitude === longitude
    );

    if (index !== -1) {
        this.highlightedIndex = index;
        this.scrollToHighlighted();
    }
  }

  async ngAfterViewInit(): Promise<void> {
    this.googleMapsService.setViewContainerRef(this.markerContainer);
  }

  protected onBack() {
    this.router.navigate(['/parking']);
  } 
  
  
  private createMarker(position: google.maps.LatLngLiteral, iconUrl: string): google.maps.marker.AdvancedMarkerElement {

    const newMarker = this.googleMapsService.addAdvancedMarker(
      position,
      2,
      undefined,
      undefined,
      iconUrl, 
      undefined,
      true
    );
  
    return newMarker;
  }
    

  private removeMarker(marker: google.maps.marker.AdvancedMarkerElement): void {
    const position = marker.position as google.maps.LatLngLiteral; 

    marker.map = null; 

    const markerIndex = this.currentMarkers.indexOf(marker);
    if (markerIndex > -1) {
      this.currentMarkers.splice(markerIndex, 1);  
      this.markerPositions.splice(markerIndex, 1); 
    }

    const newMarker = this.googleMapsService.addAdvancedMarker(
      position,
      2,
    );
  
    this.currentMarkers.splice(markerIndex, 0, newMarker);

    // Insert the position at the same index in markerPositions array
    this.markerPositions.splice(markerIndex, 0, { latitude: position.lat, longitude: position.lng });

    newMarker.addListener('click', () => {
    this.onMarkerClick(position.lat, position.lng);
  });
    
  }

  ngOnDestroy(): void {
    if (this.parkingDataSubscription) {
      this.parkingDataSubscription.unsubscribe();
    }
  }
}
