<div class="contentPanel" fxLayout="column" xLayoutAlign="flex-start">
    <div fxLayout="row"
         fxFlex="0 1 100%"
         fxFlex.lt-lg="0 1 100%"
         fxFlex.lt-md="0 1 100%"
         class="panelHead">
          <span class="h1">{{ 'PhonePreview' | translate }}</span>
          <button mat-button class="close-icon" [mat-dialog-close]="false">
            <mat-icon>close</mat-icon>
          </button>
    </div>
  </div>

  <body>
    <div class="main">
      <div class="display">

        <!-- eventStatusImage Placeholder -->
      <div class="statusImage" *ngIf="data.eventStatus"> 
        <span class="statusText">{{data.eventStatusString}}</span>
        <span><img src={{data.eventStatus}} alt="Status Image" class="statusImageSrc"></span>
      </div>

        <!-- dateImage Placeholder -->
      <div class="dateImage" *ngIf="data.date"> 
        <span class="dateText">{{data.date}}</span>
        <span><img src={{data.datePath}} alt="Date Image" class="dateImageSrc"></span>
      </div>

        <!-- contentImage Placeholder -->
      <div class="contentImage"> 
        <span><img src={{data.headerImage}} alt="Header Image" class="headerImageSrc"></span>
        <span><img src={{data.contentImage}} alt="Content Image" class="contentImageSrc"></span>
        <mat-icon class="star-icon">star</mat-icon>
      </div>

        <!-- Heading Placeholder -->
      <div class="heading">
        <span>{{ data.heading }}</span>
      </div>

      <mat-divider class="divider" [ngStyle]="{'margin-bottom': (data.heading?.length >= 36) ? '28px' : '40px'}"></mat-divider>

      <!-- content Placeholder -->
      <div class="content" [ngStyle]="{
        'margin-top': (data.eventStatus) ? '90%' : '75%',
        'height' : (data.eventStatus) ? '33%' : '39%'
        }">
        <span>{{ data.content }}</span>
      </div>

        <!-- Add the image here -->
     <img src={{image}} alt="Phone Screen Image" class="phone-screen-img">
      </div>
      <div class="camera">
        <div class="focus"></div>
      </div>
      <div class="power-button"></div>
      <div class="volume-button"></div>
    </div>
  </body>