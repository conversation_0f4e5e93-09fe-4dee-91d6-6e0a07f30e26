import {
  MAT_BUTTON_CONFIG,
  MAT_FAB_DEFAULT_OPTIONS,
  MAT_FAB_DEFAULT_OPTIONS_FACTORY,
  Mat<PERSON>nchor,
  MatButton,
  MatButtonModule,
  MatFabAnchor,
  MatFabButton,
  MatIconAnchor,
  MatIconButton,
  Mat<PERSON>iniFabAnchor,
  MatMiniFabButton
} from "./chunk-TX5LMAVA.js";
import "./chunk-LY2EHQ3C.js";
import "./chunk-OQHNG3N5.js";
import "./chunk-EKDOZG4N.js";
import "./chunk-MIJT6FLR.js";
import "./chunk-O4XABTPG.js";
import "./chunk-5OPE3T2R.js";
import "./chunk-4N4GOYJH.js";
import "./chunk-FHTVLBLO.js";
import "./chunk-WDMUDEB6.js";
export {
  MAT_BUTTON_CONFIG,
  MAT_FAB_DEFAULT_OPTIONS,
  MAT_FAB_DEFAULT_OPTIONS_<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Mat<PERSON>utton,
  MatButtonModule,
  Mat<PERSON>ab<PERSON>nch<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>utt<PERSON>,
  MatIconAnchor,
  Mat<PERSON>conButton,
  MatMiniFabAnchor,
  MatMiniFabButton
};
//# sourceMappingURL=@angular_material_button.js.map
