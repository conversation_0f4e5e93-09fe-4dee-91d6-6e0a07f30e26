import { AfterViewInit, Component, ElementRef, Inject, On<PERSON><PERSON>roy, OnInit, ViewChild, ViewContainerRef } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { CommonModule } from '@angular/common';
import { GoogleMapsModule } from '@angular/google-maps';
import { GoogleMapsService } from '../../core/services/google.maps.service';
import { environment } from '../../../environments/enviroment';

@Component({
  selector: 'app-details-parking',
  standalone: true,
  imports: [
    TranslateModule,
    MatIconModule,
    MatDialogModule,
    CommonModule,
    GoogleMapsModule,
  ],
  templateUrl: './details-parking.component.html',
  styleUrl: './details-parking.component.css'
})
export class DetailsParkingComponent implements OnInit, AfterViewInit, On<PERSON><PERSON>roy {

  @ViewChild('mapContainer') mapContainer!: ElementRef;
  @ViewChild('markerContainer', { read: ViewContainerRef, static: true }) markerContainer!: ViewContainerRef;

  constructor(
    public dialogRef: MatDialogRef<DetailsParkingComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any, 
    private googleMapsService: GoogleMapsService
  ) {}

  protected mapHeight: number = 300; // Set a default height
  protected mapWidth: number = 100;
  protected center: google.maps.LatLngLiteral = {
    lat: Number(this.data.latitude), 
    lng: Number(this.data.longitude)
  };
  protected zoom = 15;
  private map: google.maps.Map | undefined;
  private dayMapId = environment.dayMapId;
  private polygon: google.maps.Polygon | undefined;
  private marker: google.maps.marker.AdvancedMarkerElement | undefined;

  async ngOnInit() {
    console.log('Parking data:', this.data);
    console.log('Center coordinates:', this.center);
    console.log('Border points:', this.data.borderPoints);
    
    // Calculate appropriate center if we have border points
    if (this.data.borderPoints && this.data.borderPoints.length > 0) {
      this.center = this.calculateCenterFromBorderPoints();
      console.log('Calculated center from border points:', this.center);
    }
    
    await this.initializeGoogleMaps();
  }

  private calculateCenterFromBorderPoints(): google.maps.LatLngLiteral {
    if (!this.data.borderPoints || this.data.borderPoints.length === 0) {
      return this.center;
    }

    // Calculate the center point of all border points
    const latSum = this.data.borderPoints.reduce((sum: number, point: any) => sum + point.latitude, 0);
    const lngSum = this.data.borderPoints.reduce((sum: number, point: any) => sum + point.longitude, 0);
    
    return {
      lat: latSum / this.data.borderPoints.length,
      lng: lngSum / this.data.borderPoints.length
    };
  }

  private async initializeGoogleMaps() {
    try {
      // Load necessary libraries
      await this.googleMapsService.loadLibraries();

      // Initialize the map
      this.map = await this.googleMapsService.initializeMap(
        this.mapContainer.nativeElement,
        this.center,
        this.zoom,
        this.dayMapId,
        'day'
      );

      // Add visualization based on data type
      this.addParkingVisualization();

    } catch (error) {
      console.error('Error loading Google Maps:', error);
    }
  }

  private addParkingVisualization(): void {
    if (!this.map) return;

    console.log(this.data);

    // Check if we have multiple border points for polygon visualization
    if (this.data.borderPoints && this.data.borderPoints.length >= 3) {
      console.log('Creating filled polygon visualization for multiple border points');
      this.createPolygonVisualization();
    } 
    // If we have 2 border points, create a line
    else if (this.data.borderPoints && this.data.borderPoints.length === 2) {
      console.log('Creating line visualization for 2 border points');
      this.createLineVisualization();
    }
    // Fallback to main lat/lng coordinates
    else if (this.data.borderPoints.length <= 0 && this.data.latitude && this.data.longitude) {
      console.log('Creating marker for main coordinates');
      this.createSinglePointMarker(this.data.latitude, this.data.longitude);
    }
  }

  private createPolygonVisualization(): void {
    if (!this.map || !this.data.borderPoints) return;

    // Create polygon path from border points (automatically closes the shape)
    const polygonPath = this.data.borderPoints.map((point: any) => ({
      lat: point.latitude,
      lng: point.longitude
    }));

    // Create blue filled polygon for parking zone
    this.polygon = new google.maps.Polygon({
      paths: polygonPath,
      strokeColor: '#1976D2', // Blue border color for parking zone
      strokeOpacity: 1.0,
      strokeWeight: 2,
      fillColor: '#1976D2', // Blue fill color for parking zone
      fillOpacity: 0.3, // Semi-transparent fill
      map: this.map
    });

    // Adjust map bounds to show all border points
    this.fitMapToBounds();

    console.log(`Created filled polygon with ${this.data.borderPoints.length} points (automatically closed)`);
  }

  private createLineVisualization(): void {
    if (!this.map || !this.data.borderPoints) return;

    // Create line path from 2 border points
    const linePath = this.data.borderPoints.map((point: any) => ({
      lat: point.latitude,
      lng: point.longitude
    }));

    // Create blue line for 2-point parking area
    const polyline = new google.maps.Polyline({
      path: linePath,
      geodesic: true,
      strokeColor: '#1976D2', // Blue color for parking zone
      strokeOpacity: 1.0,
      strokeWeight: 3,
      map: this.map
    });

    // Adjust map bounds to show all border points
    this.fitMapToBounds();

    console.log(`Created line with ${this.data.borderPoints.length} points`);
  }

  private createSinglePointMarker(latitude: number, longitude: number): void {
    if (!this.map) return;

    const markerPosition = { lat: latitude, lng: longitude };
    
    this.marker = this.googleMapsService.addAdvancedMarker(
      markerPosition,
      0,
      undefined,
      undefined,
      'assets/images/end-point-marker.svg', // You can use a custom parking icon
      undefined,
      true
    );

    console.log('Created single point marker at:', markerPosition);
  }

  private createMainCoordinateMarker(): void {
    if (!this.map) return;

    this.marker = this.googleMapsService.addAdvancedMarker(
      this.center,
      0,
      undefined,
      undefined,
      'assets/images/parking-marker.svg', // You can use a custom parking icon
      undefined,
      true
    );

    console.log('Created main coordinate marker at:', this.center);
  }

  private fitMapToBounds(): void {
    if (!this.map || !this.data.borderPoints || this.data.borderPoints.length < 2) return;

    const bounds = new google.maps.LatLngBounds();
    
    // Add all border points to bounds
    this.data.borderPoints.forEach((point: any) => {
      bounds.extend({ lat: point.latitude, lng: point.longitude });
    });

    // Fit map to show all points with some padding
    this.map.fitBounds(bounds, {
      top: 50,
      right: 50,
      bottom: 50,
      left: 50
    });
  }

  async ngAfterViewInit(): Promise<void> {
    this.googleMapsService.setViewContainerRef(this.markerContainer);
  }

  onClose(): void {
    this.dialogRef.close();
  }

  ngOnDestroy(): void {
    // Clean up map elements
    if (this.polygon) {
      this.polygon.setMap(null);
    }
    
    if (this.marker) {
      this.marker.map = null;
    }
  }
}