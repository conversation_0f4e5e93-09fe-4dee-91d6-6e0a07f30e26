import { Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { UsersService } from '../../core/services/user.service';
import { MessageService } from '../../core/services/message.service';
import { AuthService } from '../../core/services/auth.service';
import { PlatformVariablesService } from '../../core/services/platform-variables.service';

@Component({
  selector: 'app-delete-platform-variables',
  standalone: true,
  imports: [
    TranslateModule,
    MatIconModule,
    MatDialogModule
  ],
  templateUrl: './delete-platform-variables.component.html',
  styleUrl: './delete-platform-variables.component.css'
})
export class DeletePlatformVariablesComponent {

  protected disabledSave = false;

  constructor(
    public dialogRef: MatDialogRef<DeletePlatformVariablesComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private platformVariablesService: PlatformVariablesService,
    private messageService: MessageService
  ) { }

  ngOnInit(): void {
  }

  onNoClick(): void {
    this.dialogRef.close();
  }

  onYesClick(): void {
    this.platformVariablesService.deletePlatformVariable(this.data.id).subscribe(
      {
        next: (response) => {
          this.messageService.showMessage(["DeleteSuccessfully"], 'success');
          this.dialogRef.close(true);
        },
        error: (error: any) => {
          console.error('Error deleting user:', error);
          // Handle error as per your application's requirement
        }
      }
    );
  }
}
