import { CommonModule, DatePipe } from '@angular/common';
import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatOptionModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { MatDividerModule } from '@angular/material/divider';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';  // Import MatInputModule
import { MatSelectModule } from '@angular/material/select';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { EditorComponent } from '../../../shared/editor/editor.component';
import { MessageService } from '../../../core/services/message.service';
import { TranslationModalComponent } from '../../../shared/translation-modal/translation-modal.component';
import { TranslationDeleteModalComponent } from '../../../shared/translation-delete-modal/translation-delete-modal.component';
import { EventsService } from '../../../core/services/events.service';
import { EventsGetByIdModel } from '../../../shared/interfaces/events/events-get-by-id.model';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';

@Component({
  selector: 'app-edit-sport-event',
  standalone: true,
  imports: [
   CommonModule, 
   TranslateModule,
   MatIconModule,
   MatDialogModule,
   ReactiveFormsModule,
   MatFormFieldModule,
   MatInputModule,
   MatButtonModule,
   MatDatepickerModule,
   MatSelectModule,
   MatOptionModule,
   MatDividerModule,
   MatCardModule,
   EditorComponent,
   FormsModule,
   MatSlideToggleModule
  ],
  templateUrl: './edit-sport-event.component.html',
  styleUrl: './edit-sport-event.component.css'
})
export class EditSportEventComponent {
  @ViewChild('fileInput') fileInput: ElementRef | undefined;


  editSportEventForm!: FormGroup;
  protected content = '';
  protected imageSrcs: { 
    id?: number;
    fileName: string; 
    contentType: string; 
    extension: string; 
    content: string; 
    isCover: boolean;
  }[] = [];
  private sportEventId!: number;
  protected sportEvent: any;
  protected selectedThumbnailIndex: number | null = null; // Track selected thumbnail
  protected  translatedLanguages: { languageShort: string, languageFullName: string, heading: string, content: string }[] = [];
  protected showImages: boolean = true;

  constructor(private translate: TranslateService, private fb: FormBuilder, private router: Router, private route: ActivatedRoute, public dialog: MatDialog, private messageService: MessageService,
    private eventsService: EventsService, private datePipe: DatePipe
  ) {
    this.editSportEventForm = this.fb.group({
      id: [''],
      heading: ['', [Validators.maxLength(60)]],
      date: [''],
      time: ['', [this.timeValidator]], // Add time control with validator
      templateContent: [''],
      thumbnailIndex: [null], // Add control for thumbnail index
    });
  }

  ngOnInit(): void {

    this.route.paramMap.subscribe((params) => {
      const id = params.get('id');
      if (id) {
        this.sportEventId = Number(id);
        this.loadEventsData(Number(id));
      }
    });

    this.route.queryParams.subscribe((queryParams) => {
      // Assuming you passed the landmark data like heading, content, published in queryParams
      this.sportEvent = {
        heading: queryParams['heading'],
        content: queryParams['content'],
        published: queryParams['published']
      };

      if (this.sportEvent) {

        const htmlContent = `<p>${this.sportEvent.content}</p>`;

        this.content = htmlContent;

        this.editSportEventForm.patchValue({
          id: this.sportEventId,
          heading: this.sportEvent.heading,
          templateContent: htmlContent
        });
      }

    });

  }

    
      protected get headingControl(): FormControl<string> {
        return this.editSportEventForm.get(
          'heading'
        ) as FormControl<string>;
      }
  
      protected get templateContentControl(): FormControl<string> {
        return this.editSportEventForm.get(
          'templateContent'
        ) as FormControl<string>;
      }

  // Custom validator for time format (HH:MM)
  timeValidator(control: any) {
    const value = control.value;
    if (!value) return null; // Allow empty value
    
    const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;
    if (!timeRegex.test(value)) {
      return { invalidTime: true };
    }
    
    return null;
  }

  onTimeInput(event: Event) {
    const input = event.target as HTMLInputElement;
    let value = input.value.replace(/[^\d]/g, ''); // Remove non-digits
    
    // Auto-format as user types
    if (value.length >= 3) {
      value = value.substring(0, 2) + ':' + value.substring(2, 4);
    }
    
    // Validate hours (00-23)
    if (value.length >= 2) {
      const hours = parseInt(value.substring(0, 2));
      if (hours > 23) {
        value = '23' + value.substring(2);
      }
    }
    
    // Validate minutes (00-59)
    if (value.length >= 5) {
      const minutes = parseInt(value.substring(3, 5));
      if (minutes > 59) {
        value = value.substring(0, 3) + '59';
      }
    }
    
    // Limit to 5 characters (HH:MM)
    if (value.length > 5) {
      value = value.substring(0, 5);
    }
    
    // Update the form control
    this.editSportEventForm.get('time')?.setValue(value);
    input.value = value;
  }

  onTimeKeydown(event: KeyboardEvent) {
    const input = event.target as HTMLInputElement;
    const allowedKeys = ['Backspace', 'Delete', 'Tab', 'Escape', 'Enter', 'ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown'];
    
    // Allow navigation and editing keys
    if (allowedKeys.includes(event.key)) {
      return;
    }
    
    // Allow only numeric input
    if (!/^\d$/.test(event.key)) {
      event.preventDefault();
      return;
    }
    
    // Handle cursor position for better UX
    const cursorPosition = input.selectionStart || 0;
    const currentValue = input.value;
    
    // Skip colon position
    if (cursorPosition === 2 && currentValue.length >= 2) {
      input.setSelectionRange(3, 3);
    }
  }

  onTimeClick(event: Event) {
    const input = event.target as HTMLInputElement;
    const cursorPosition = input.selectionStart || 0;
    
    // If clicked on colon, move cursor to next position
    if (cursorPosition === 2 && input.value.length >= 3) {
      setTimeout(() => {
        input.setSelectionRange(3, 3);
      }, 0);
    }
  }

  private loadEventsData(id: number): void {
  this.eventsService.getEventById(id).subscribe({
    next: (data: EventsGetByIdModel) => {
      // Extract time using the helper method (recommended approach)
      const timeData = this.extractTimeFromDate(data.startDate);
      
      // Extract date separately
      let eventDate: Date | null = null;
      if (data.startDate) {
        eventDate = new Date(data.startDate);
      }

      this.editSportEventForm.patchValue({
        id: data.id,
        heading: data.name,
        templateContent: data.description,
        date: eventDate, // Set the date for the date picker
        time: timeData?.timeString || '', // Set the time (HH:MM format) for your time input
      });

      if(data.description) {
        this.content = data.description;
      }

      if(data.images) {
        this.imageSrcs = data.images.map(img => ({
          id: img.id,
          fileName: img.key,
          contentType: 'image/png',
          extension: img.key.split('.').pop() || '',
          content: img.preSignedUrl,
          isCover: img.isCover
        }));
        
        const coverIndex = this.imageSrcs.findIndex(img => img.isCover);
        this.selectedThumbnailIndex = coverIndex !== -1 ? coverIndex : null;
        
        this.editSportEventForm.patchValue({
          thumbnailIndex: this.selectedThumbnailIndex
        });
      }

      if (data.translations && data.translations.length > 0) {
        this.translatedLanguages = data.translations.map(translation => ({
          languageShort: translation.languageCode || 'en',
          languageFullName: translation.languageName || 'English',
          heading: translation.name || '',
          content: translation.description || ''
        }));
      }
    },
    error: (error) => {
      console.error('Error fetching interest point details:', error);
    }
  });
}

// Helper method for reusable time extraction from ISO string
private extractTimeFromDate(dateString: string): { hour: number, minute: number, timeString: string, time12h: string } | null {
  if (!dateString) return null;
  
  const date = new Date(dateString); // ISO string like "2025-06-18T17:00:00Z"
  
  // Check if the date is valid
  if (isNaN(date.getTime())) {
    console.error('Invalid date string:', dateString);
    return null;
  }
  
  const hour = date.getHours(); // 17
  const minute = date.getMinutes(); // 0
  const timeString = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`; // "17:00"
  
  // 12-hour format
  const hour12 = hour > 12 ? hour - 12 : (hour === 0 ? 12 : hour);
  const ampm = hour >= 12 ? 'PM' : 'AM';
  const time12h = `${hour12}:${minute.toString().padStart(2, '0')} ${ampm}`; // "5:00 PM"
  
  return { hour, minute, timeString, time12h };
}


  get form() {
    return this.editSportEventForm.controls;
  }

  onSubmit() {
   // Update isCover property for all images before submitting
    this.updateCoverImage();
    
    this.editSportEventForm.patchValue({
      templateContent: this.content
    });
  
    const templateContent = this.cleanContent(this.editSportEventForm.value.templateContent);
  
    if (this.editSportEventForm.valid && templateContent.length > 0) {
      // Patch form data with thumbnail selection
      this.editSportEventForm.patchValue({ thumbnailIndex: this.selectedThumbnailIndex });
  
      // Initialize storage for files and existing images
      let coverFile: File | null = null;
      let coverImageId: number | null = null;
      const files: File[] = [];
      const existingImageIds: number[] = [];
  
      // Process images: differentiate between new and existing images
      this.imageSrcs.forEach((src, index) => {
        if (src.id) {
          // Existing image: check if it's the cover
          if (index === this.selectedThumbnailIndex) {
            coverImageId = src.id;
          } else {
            existingImageIds.push(src.id);
          }
        } else {
          // New image: convert base64 to file
          const byteArray = this.convertBase64ToByteArray(src.content);
          const blob = new Blob([byteArray], { type: src.contentType });
          const file = new File([blob], src.fileName, { type: src.contentType });
  
          if (index === this.selectedThumbnailIndex) {
            coverFile = file;
          } else {
            files.push(file);
          }
        }
      });
  
      // Prepare translation data
      const translations = this.translatedLanguages.map(translation => ({
        name: translation.heading,
        description: translation.content,
        languageName: translation.languageFullName,
        languageCode: translation.languageShort,
      }));
  
      // Prepare form data
      const formData = new FormData();
      formData.append('id', this.sportEventId.toString());
      formData.append('Name', this.editSportEventForm.value.heading);
      formData.append('Description', this.editSportEventForm.value.templateContent);
      formData.append('Category', 'Sports');

          // Combine date and time into StartDate field
    if (this.editSportEventForm.value.date && this.editSportEventForm.value.time) {
      const combinedDateTime = this.combineDateAndTime(
        this.editSportEventForm.value.date, 
        this.editSportEventForm.value.time
      );
      formData.append('StartDate', combinedDateTime.toISOString());
    } else if (this.editSportEventForm.value.date) {
      // If only date is provided, use date with default time (00:00:00)
      const dateValue = this.editSportEventForm.value.date;
      const dateOnly = dateValue instanceof Date ? dateValue : new Date(dateValue);
      formData.append('StartDate', dateOnly.toISOString());
    }
  
      // Append existing image IDs
      existingImageIds.forEach((id, index) => {
        formData.append(`ExistingImageIds[${index}]`, id.toString());
      });
  
      // Append translation data
      translations.forEach((translation, index) => {
        formData.append(`Translations[${index}].name`, translation.name);
        formData.append(`Translations[${index}].description`, translation.description);
        formData.append(`Translations[${index}].languageCode`, translation.languageCode);
        formData.append(`Translations[${index}].languageName`, translation.languageName);
      });
  
      // Append new images (excluding the cover)
      files.forEach((file, index) => {
        formData.append(`Files[${index}]`, file, file.name);
      });
  
      // Append cover image (either as an ID or a new file)
      if (coverImageId !== null) {
        formData.append('ExistingCoverId', String(coverImageId));
      } else if (coverFile !== null) {
        formData.append('Cover', coverFile);
      }
      
      // Submit to backend
      this.eventsService.editEvent(formData).subscribe({
        next: (response) => {
          console.log('Event updated successfully', response);
          this.messageService.showMessage(["UpdateSuccessfully"], 'success');
          this.router.navigate(['events/sport-events']);
        },
        error: (error) => {
          console.error('Error updating interest point', error);
          this.messageService.showMessage(["ErrorSubmittingForm"], 'error');
        }
      });
    } else {
      this.messageService.showMessage(["PleaseCompleteTheForm"], 'error');
    }
  }

  // Helper method to combine date and time
  private combineDateAndTime(date: Date, time: string): Date {
    const [hours, minutes] = time.split(':').map(num => parseInt(num, 10));
    const combinedDate = new Date(date);
    combinedDate.setHours(hours, minutes, 0, 0);
    return combinedDate;
  }

  convertBase64ToByteArray(base64: string): Uint8Array {
    const binaryString = atob(base64.split(',')[1]); // Decode base64 string
    const byteArray = new Uint8Array(binaryString.length);
    for (let i = 0; i < binaryString.length; i++) {
      byteArray[i] = binaryString.charCodeAt(i);
    }
    return byteArray;
  }

  // Update the selectThumbnail method to handle isCover property
  selectThumbnail(index: number) {
    this.selectedThumbnailIndex = index;
    this.editSportEventForm.patchValue({
      thumbnailIndex: this.selectedThumbnailIndex,
    });
    
    // Update isCover property for all images
    this.updateCoverImage();
  }
  
  // New method to update isCover property for all images
  private updateCoverImage() {
    if (this.imageSrcs.length > 0 && this.selectedThumbnailIndex !== null) {
      // Reset isCover for all images
      this.imageSrcs.forEach((img, i) => {
        img.isCover = i === this.selectedThumbnailIndex;
      });
    }
  }

  onChangeContent() {
    const templateContentControl = this.editSportEventForm.get('templateContent');
    if (templateContentControl) {
      templateContentControl.setValue(this.content);
    }
  }

 openFileDialog() {
     if (this.fileInput) {
       this.fileInput.nativeElement.click();
     }
   }
 
   onFileSelected(event: Event) {
     const fileInput = event.target as HTMLInputElement;
     if (fileInput.files && fileInput.files.length > 0) {
       Array.from(fileInput.files).forEach((file: File) => {
         const reader = new FileReader();
   
         // Read the file as a data URL to get the content in base64
         reader.onload = (e: any) => {
           // Create a file object with dynamically populated properties
           const fileDetails = {
             fileName: file.name,              // Get the file name
             contentType: file.type,           // Get the content type (MIME type)
             extension: file.name.split('.').pop() || '', // Extract the file extension
             content: e.target.result,         // Base64 encoded content of the file
             isCover: false                    // Initialize isCover to false
           };
   
           // Push the file details to the array
           this.imageSrcs.push(fileDetails);
   
           // If this is the first image, automatically set it as the thumbnail and cover
           if (this.imageSrcs.length === 1) {
             this.selectedThumbnailIndex = 0;
             this.imageSrcs[0].isCover = true;
           }
           
           // Update the form control
           this.editSportEventForm.patchValue({
             thumbnailIndex: this.selectedThumbnailIndex
           });
         };
   
         // Read the file as base64
         reader.readAsDataURL(file);
       });
     }
   }
   
   removePhoto(index: number) {
     const removedWasCover = this.imageSrcs[index].isCover;
     this.imageSrcs.splice(index, 1); // Remove image at the given index

     if (this.fileInput) {
      this.fileInput.nativeElement.value = '';
    }
     
     // Update thumbnail selection if needed
     if (this.selectedThumbnailIndex === index) {
       // If we removed the selected thumbnail, select the first image if available
       this.selectedThumbnailIndex = this.imageSrcs.length > 0 ? 0 : null;
       
       // If the removed image was the cover and we have other images, set the new selection as cover
       if (removedWasCover && this.imageSrcs.length > 0 && this.selectedThumbnailIndex !== null) {
         this.imageSrcs[this.selectedThumbnailIndex].isCover = true;
       }
     } else if (this.selectedThumbnailIndex !== null && index < this.selectedThumbnailIndex) {
       // If we removed an image before the selected one, update the index
       this.selectedThumbnailIndex--;
     }
     
     // Update the form control
     this.editSportEventForm.patchValue({
       thumbnailIndex: this.selectedThumbnailIndex
     });
   }
 
   openTranslationModal(language?: string) {
      
    let heading;
    let content;

    if(language === undefined) {
     heading = this.editSportEventForm.value.heading?.trim();
     content = this.editSportEventForm.value.templateContent?.trim(); // Read from the form
    } else if(language !== undefined) {
      const selectedTranslation = this.translatedLanguages.find(
        translation => translation.languageShort === language
      );

      heading = selectedTranslation?.heading
      content = selectedTranslation?.content
    }

     content = this.cleanContent(content);
 
     if (!heading || !content) {
       this.messageService.showMessage(['HeadingAndContentCannotBeEmpty'], 'error')
       return; // Exit function if both are empty
     }
     
     const dialogRef = this.dialog.open(TranslationModalComponent, {
       width: '693px',
       height: '95vh',
       data: {
         heading: heading,
         content: content,
         selectedLanguage: language
       }
     });
 
     dialogRef.afterClosed().subscribe(result => {
       if (result) {
         // Check if the translation already exists for the selected language
         const existingTranslationIndex = this.translatedLanguages.findIndex(
           translation => translation.languageShort === result.languageShort
         );
 
         if (existingTranslationIndex !== -1) {
           // If it exists, update the existing translation
           this.translatedLanguages[existingTranslationIndex] = {
             languageShort: result.languageShort,
             languageFullName: result.languageFullName,
             heading: result.heading,
             content: result.content
           };
         } else {
           // If it doesn't exist, add a new translation
           this.translatedLanguages.push({
             languageShort: result.languageShort,
             languageFullName: result.languageFullName,
             heading: result.heading,
             content: result.content
           });
         }
       }
     });
   }
 
   removeTranslation(language: string) {
     const dialogRef = this.dialog.open(TranslationDeleteModalComponent, {
       width: '530px'
     });
 
     dialogRef.afterClosed().subscribe(result => {
       if (result) {
         // Proceed with deletion if the user confirmed
         this.translatedLanguages = this.translatedLanguages.filter(
           translation => translation.languageShort !== language
         );
       }
     });
   }
 
   cleanContent(content: string): string {
     if (!content) return ''; // Ensure it's not undefined/null
   
     // Remove empty paragraphs, line breaks, and spaces
     content = content.replace(/<(p|br|div|span)>\s*<\/\1>/g, '');
   
     return content.trim(); // Trim remaining spaces
   }

   toggleImages() {
    this.showImages = !this.showImages;
   }

  onBack() {
    this.router.navigate(['events/sport-events']);
  }
}
