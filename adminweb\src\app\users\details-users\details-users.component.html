<div class="contentPanel" fxLayout="column" xLayoutAlign="flex-start">
    <div fxLayout="row"
         fxFlex="0 1 100%"
         fxFlex.lt-lg="0 1 100%"
         fxFlex.lt-md="0 1 100%"
         class="panelHead">
          <span class="h1">{{ 'Details' | translate }}</span>
          <button mat-button class="close-icon" [mat-dialog-close]="false">
            <mat-icon>close</mat-icon>
          </button>
    </div>
  </div>
  
  <div mat-dialog-content>
    <!-- Profile Picture -->
    <div class="profile-container">
        <img [src]="profilePicture" 
             alt="Mock Profile Picture" 
             class="profile-picture">
      </div>
  
    <!-- User Details -->
    <div class="details-report-content-row">
      <strong>{{ 'ID' | translate }}</strong>
      <p>{{data.id}}</p>
    </div>
    <div class="details-report-content-row">
      <strong>{{ 'firstName' | translate }}</strong>
      <p>{{data.firstName}}</p>
    </div>
    <div class="details-report-content-row">
      <strong>{{ 'lastName' | translate }}</strong>
      <p>{{data.lastName}}</p>
    </div>
    <div class="details-report-content-row">
      <strong>{{ 'role' | translate }}</strong>
      <p>{{data.role | translate}}</p>
    </div>
  
    <!-- Car Plates -->
    <div class="details-report-content-row">
      <strong>{{ 'vehiclePlates' | translate }}</strong>
      <ul class="vehicle-plates">
        <li *ngFor="let plate of data.vehiclePlates">{{ plate }}</li>
      </ul>
    </div>
  </div>
  