import { Injectable } from '@angular/core';
import {
  HttpInterceptor,
  HttpRequest,
  HttpHandler,
  HttpEvent,
  HttpErrorResponse,
  HttpHeaders,
  HttpResponse,
} from '@angular/common/http';
import { BehaviorSubject, Observable, throwError } from 'rxjs';
import { tap } from 'rxjs/operators';
import { AuthService } from './auth.service';
import { JwtHelperService } from '@auth0/angular-jwt';
import { environment } from '../../../environments/enviroment';
import { MessageService } from './message.service';

@Injectable()
export class AuthInterceptor implements HttpInterceptor {
  private isRefreshing = false;
  private refreshTokenSubject: BehaviorSubject<string | null> =
    new BehaviorSubject<string | null>(null);

  private jwtHelper!: JwtHelperService; // Lazy initialize JwtHelperService

  constructor(
    private authService: AuthService,
    private messageService: MessageService
  ) {} // Inject MessageService

  private getJwtHelper(): JwtHelperService {
    // Initialize JwtHelperService when first needed
    if (!this.jwtHelper) {
      this.jwtHelper = new JwtHelperService();
    }
    return this.jwtHelper;
  }

  intercept(
    req: HttpRequest<any>,
    next: HttpHandler
  ): Observable<HttpEvent<any>> {
    const accessToken = localStorage.getItem('token');

    if (
      req.url.startsWith(environment.apiUrl) &&
      accessToken &&
      !this.getJwtHelper().isTokenExpired(accessToken)
    ) {
      const headers = new HttpHeaders().set(
        'Authorization',
        `Bearer ${accessToken}`
      );
      const authReq = req.clone({ headers });

      return next.handle(authReq).pipe(
        tap({
          next: (event: any) => {
            if (event instanceof HttpResponse) {
              // Do nothing on success
            }
          },
          error: (error: HttpErrorResponse) => {
            let messages: string[] = [];
            var respError = error as HttpErrorResponse;

            if (
              respError &&
              (respError.status === 401 || respError.status === 403)
            ) {
              messages.push('Unauthorized access. Please login again.');
              this.authService.logout();
            } else if (respError && respError.status === 400) {
              const validationErrors = respError.error;
              
              let errors;
              try {
                // Attempt to parse the validation errors
                errors = JSON.parse(validationErrors).errors;
              } catch (e) {
                errors = validationErrors.errors; // or you could set it to validationErrors if you want to process it as a string
              }

              Object.keys(errors).forEach((prop) => {
                messages.push(`${prop}: ${errors[prop].join(', ')}`);
              });
            } else if (respError && respError.status === 404) {
              const validationErrorKey = respError.error.targetProperty;
              const validationErrors = respError.error.detail;

              messages.push(`${validationErrorKey}: ${validationErrors}`);
            } else if (respError && respError.status === 422) {
              const validationErrors = respError.error;

              let errors;
              try {
                // Attempt to parse the validation errors
                errors = JSON.parse(validationErrors).errors;
              } catch (e) {
                // or you could set it to validationErrors if you want to process it as a string
                errors = validationErrors;
              }

              Object.keys(errors).forEach((prop) => {
                // If the key (prop) is a number, store it in the messages array
                if (!isNaN(Number(prop))) {
                  messages.push(`Key: ${prop}, Value: ${errors[prop]}`);
                }
              });
            } else {
              messages.push('Server error occurred. Please try again later.');
            }

            
            // Use MessageService to show the error messages
            this.messageService.showMessage(messages, 'error');
          },
        })
      );
    } else {
      return next.handle(req).pipe(
        tap({
          next: (event: any) => {
            if (event instanceof HttpResponse) {
              // Do nothing on success
            }
          },
          error: (error: HttpErrorResponse) => {
            let messages: string[] = [];
            var respError = error as HttpErrorResponse;

            if (respError && respError.status === 400) {
              
              const validationErrors = respError.error;
              let errors;

              try {
                // Attempt to parse the validation errors
                errors = JSON.parse(validationErrors).errors;
              } catch (e) {
                // Handle case where validationErrors is not a valid JSON
                errors = validationErrors.errors; // Process as string
              }

              Object.keys(errors).forEach((prop) => {
                messages.push(`${prop}: ${errors[prop].join(', ')}`);
              });
            } else if (respError && (respError.status === 401 || respError.status === 403)) {
                messages.push('Session expired. Please login again.');
                this.authService.logout();
            } else {
              messages.push('Server error occurred. Please try again later.');
            }

            // Use MessageService to show the error messages
            this.messageService.showMessage(messages, 'error');
          },
        })
      );
    }
  }
}

