.contentPanel{
    padding: 2px;
    width: 100%;
    border-top: 8px solid var(--primary-color);
    justify-content: space-between;
    border-bottom: 1px solid #ccc;
  }
  
  .panelHead{
    margin: 20px;
    display: flex;
    align-items: center;
  }
  
  .h1{
    text-align: center;
    color: var(--color-main);
    width: 100%;
    border-bottom: 1px solid var(--color-gray-border) !important;
    margin: 0;
    font-family: 'Roboto', sans-serif;
    font-size: 20px;
    font-weight: 700;
    line-height: 16px;
    margin-left: 33px;
  }
  
  .close-icon{
      cursor: pointer;
      font-size: 35px;
      border: none;
      background-color: transparent;
  }
  
  .details-report-content-row{
    display: flex;
    justify-content: space-between;
  }

  /* Profile Picture */
  .profile-container {
    display: flex;
    justify-content: center;
    margin-bottom: 15px;
  }
  
  .profile-picture {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid #ccc;
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease-in-out;
  }
  
  /* Vehicle Plates List */
  .vehicle-plates {
    list-style-type: none;
    padding: 0;
  }
  
  .vehicle-plates li {
    background: var(--primary-light);
    padding: 5px 10px;
    margin: 3px 0;
    border-radius: 4px;
  }