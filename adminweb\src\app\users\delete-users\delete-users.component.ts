import { Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { UsersService } from '../../core/services/user.service';
import { MessageService } from '../../core/services/message.service';
import { AuthService } from '../../core/services/auth.service';

@Component({
  selector: 'app-delete-users',
  standalone: true,
  imports: [
    TranslateModule,
    MatIconModule,
    MatDialogModule
  ],
  templateUrl: './delete-users.component.html',
  styleUrl: './delete-users.component.css'
})
export class DeleteUsersComponent implements OnInit {

  protected disabledSave = false;

  constructor(
    public dialogRef: MatDialogRef<DeleteUsersComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private usersService: UsersService,
    private authService: AuthService,
    private messageService: MessageService
  ) { }

  ngOnInit(): void {
    const userRole = this.authService.getRole()

    if (this.data.role === 'AdminPlatform' && userRole !== 'AdminPlatform') {
    this.disabledSave = true;
    }
  }

  onNoClick(): void {
    this.dialogRef.close();
  }

  onYesClick(): void {
    this.usersService.deleteUser(this.data.id).subscribe(
      {
        next: (response) => {
          this.messageService.showMessage(["DeleteSuccessfully"], 'success');
          this.dialogRef.close(true);
        },
        error: (error: any) => {
          console.error('Error deleting user:', error);
          // Handle error as per your application's requirement
        }
      }
    );
  }
}
