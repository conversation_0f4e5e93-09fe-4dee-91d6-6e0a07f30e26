import { CommonModule, DatePipe } from '@angular/common';
import { Component, DestroyRef, inject, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatDividerModule } from '@angular/material/divider';
import { MatSelectModule } from '@angular/material/select';
import { MatCardModule } from '@angular/material/card';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatInputModule } from '@angular/material/input';
import { MatMenuModule } from '@angular/material/menu';
import { MatSortModule, Sort } from '@angular/material/sort';
import { Router, RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { MatPseudoCheckboxModule } from '@angular/material/core';
import { MatDialog } from '@angular/material/dialog';
import { MatTooltip } from '@angular/material/tooltip';
import { GridColumnModel } from '../../shared/interfaces/settings/grid-settings.model';
import { DetailsSportEventComponent } from './details-sport-event/details-sport-event.component';
import { DeleteSportEventComponent } from './delete-sport-event/delete-sport-event.component';
import { PublishStatusSportEventComponent } from './publish-status-sport-event/publish-status-sport-event.component';
import { PhoneEventsPreviewComponent } from '../../shared/phone-events-preview/phone-events-preview.component';
import { EventsItemEntity } from '../../shared/interfaces/events/events-item.model';
import { PaginationSortModel } from '../../shared/interfaces/paginator/pagination-sort.model';
import { GridSetting } from '../../shared/constants/grid-settings';
import { EventsService } from '../../core/services/events.service';
import { tap } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

@Component({
  selector: 'app-sport-events',
  standalone: true,
  imports: [
    CommonModule,
    MatDividerModule,
    MatSelectModule,
    MatIconModule,
    MatButtonModule,
    MatCardModule,
    MatTableModule,
    MatPseudoCheckboxModule,
    ReactiveFormsModule,
    TranslateModule,
    MatInputModule,
    MatDatepickerModule,
    FormsModule,
    MatPaginatorModule,
    MatPaginator,
    MatTableModule,
    MatMenuModule,
    MatSortModule,
    RouterModule,
    MatTooltip
  ],
  templateUrl: './sport-events.component.html',
  styleUrl: './sport-events.component.css'
})
export class SportEventsComponent {
  @ViewChild(MatPaginator) paginator!: MatPaginator | null;
  private destroyRef = inject(DestroyRef);

  sportEventsFilterForm!: FormGroup;
  
  private clickTimeout: any; // To manage single-click timeout
  private clickDelay = 300; // Time in milliseconds to distinguish single from double click
  private isDoubleClick = false; // Flag to track if double-click occurred
  protected today = new Date();
  protected firstDayOfMonth = new Date(this.today.getFullYear(), this.today.getMonth(), 1);
  protected lastDayOfMonth = new Date(this.today.getFullYear(), this.today.getMonth() + 1, 0);
  protected totalCount = 0;
  protected gridColumns: GridColumnModel[] = [];
  protected gridColors: [] = []


 displayedColumns: string[] = [
     'id',
     'name',
     'startDate',
     'createdBy',
     'createdDate',
     'lastModifiedBy',
     'lastModifiedDate',
     'actions',
   ];
 
   protected dataSource: MatTableDataSource<EventsItemEntity> =
       new MatTableDataSource<EventsItemEntity>([]);

  protected paginationSort: PaginationSortModel = {
        pageNumber: GridSetting.defaultPageNumber,
        pageSize: GridSetting.defaultPageSize,
        sortColumn: GridSetting.defaultSortColumn,
        sortDirection: GridSetting.defaultSortDirection,
    };

 ngOnInit() {
  this.getEventsData();
 }

  constructor(
    private fb: FormBuilder,
    public dialog: MatDialog,
    private datePipe: DatePipe,
    private router: Router,
    private eventsService: EventsService
    ) {
    this.sportEventsFilterForm = this.fb.group({
      fromDate: [this.firstDayOfMonth],
      toDate:   [this.lastDayOfMonth],
      search: ['']
    });
  }

  private getEventsData(paginationSort = this.paginationSort): void {
    
        let search = this.sportEventsFilterForm.value.search;
        let fromDate = this.sportEventsFilterForm.value.fromDate;
        let toDate = this.sportEventsFilterForm.value.toDate;

        let formattedFromDate = fromDate ? this.datePipe.transform(fromDate, 'yyyy-MM-dd') : null;
        let formattedToDate = toDate ? this.datePipe.transform(toDate, 'yyyy-MM-dd') : null;
       
        let category = 'Sports';
        let { pageNumber, pageSize, sortColumn, sortDirection } = paginationSort;
        this.eventsService
          .getEvents(
            search,
            formattedFromDate,
            formattedToDate,
            category,
            sortColumn,
            sortDirection,
            pageNumber,
            pageSize
          )
          .pipe(
            tap((data) => {
              this.dataSource = new MatTableDataSource<EventsItemEntity>(data.items);
              this.totalCount = data.totalCount;
            }),
            takeUntilDestroyed(this.destroyRef)
          )
          .subscribe();
  }

  protected onPageChange(event: any) {
        const pageIndex = event.pageIndex + 1; // Paginator index starts from 0, while your API starts from 1
        const pageSize = event.pageSize;
        this.paginationSort = {
          ...this.paginationSort,
          pageSize: pageSize,
          pageNumber: pageIndex,
        };
    
        this.getEventsData();
      }
    
      protected onSortChange(sortState: Sort) {
        this.paginationSort = {
          ...this.paginationSort,
          sortColumn: sortState.active,
          sortDirection: sortState.direction,
        };
        this.getEventsData();
      }
    
      private resetPaginatorSort() {
        if (this.paginator) {
          this.paginator.firstPage();
        }
        this.paginationSort = {
          pageNumber: GridSetting.defaultPageNumber,
          pageSize: GridSetting.defaultPageSize,
          sortColumn: GridSetting.defaultSortColumn,
          sortDirection: GridSetting.defaultSortDirection,
        };
      }

  openAddLandmarkDialog(): void {
    // Navigate to repairAdd route
    this.router.navigate(['/events/sport-events/sport-events-add']);
  }


  editElement(element: any): void {
    // Navigate to repairEdit route
    this.router.navigate(['/events/sport-events/sport-events-edit', element.id], {
    });
  }

  deleteElement(element: any): void {
    const dialogRef = this.dialog.open(DeleteSportEventComponent, {
      width: '530px',
      data: { id: element.id, content: element.content }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.getEventsData();
      }
    });
  }

  viewDetails(element: any): void {
  this.eventsService.getEventById(element.id).subscribe({
    next: (data) => {
      const mergedElement = { ...element, ...data };

      const dialogRef = this.dialog.open(DetailsSportEventComponent, {
        height: '600px',
        data: {data: mergedElement}
      });

      dialogRef.afterClosed().subscribe(() => {
        console.log('Dialog closed');
      });
    },
    error: (error) => {
      console.error('Error fetching details:', error);
    },
    complete: () => {
      console.log('Request completed.');
    }
  });
}

 openPreview(element: any): void {
    this.eventsService.getEventById(element.id).subscribe({
      next: (data) => {
        let imgPath;
        if(data.images.length > 0) {
          imgPath = data.images[0].preSignedUrl;  
        } else if(data.images.length <= 0) {
          imgPath = '../../../assets/images/default-background-image.png'
        }
 
        let cleanedDescription = this.cleanContent(data.description)

        console.log(cleanedDescription, data.description) 

        const headerPath = '../../../assets/images/event-header.png'
        const dialogRef = this.dialog.open(PhoneEventsPreviewComponent, {
          width: '480px',
          data: {heading: data.name, content: cleanedDescription, contentImage: imgPath, headerImage: headerPath}
        });
    
        dialogRef.afterClosed().subscribe(result => {
    
        });
      },
      error: (error) => {
        console.error('Error fetching details:', error);
      },
      complete: () => {
        console.log('Request completed.');
      }
    })
  }

  cleanContent(content: string): string {
    if (!content) return ''; // Ensure it's not undefined/null
  
    return content
      .replace(/<\/?p>/g, '') // Remove opening and closing <p> tags
      .replace(/<br\s*\/?>/g, ' ') // Replace <br> tags with space
      .replace(/\s+/g, ' ') // Replace multiple spaces with a single space
      .trim(); // Trim leading/trailing spaces
  }
  
  updatePublishStatus(element: any): void {
    const dialogRef = this.dialog.open(PublishStatusSportEventComponent, {
      width: '530px',
      data: { id: element.id, content: element.content, published: element.published }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        // Изпълнение на изтриването
        // Тук можете да добавите реална логика за изтриване
      }
    });
  }

  onRowClick(event: MouseEvent, row: any): void {
    // Check if the event target is a button or inside a specific column
    const target = event.target as HTMLElement;
  
    // Prevent row click if the target is a button, icon, or menu
    if (
      target.closest('button') ||
      target.closest('mat-menu') ||
      target.closest('.actions-header')
    ) {
      return;
    }
  
    // Reset double-click flag
    this.isDoubleClick = false;

    // Set a timeout for single-click action
    this.clickTimeout = setTimeout(() => {
      if (!this.isDoubleClick) {
        this.openPreview(row); // Trigger single-click action
      }
    }, this.clickDelay);
  }
  
  onRowDoubleClick(event: MouseEvent, row: any): void {
    // Check if the event target is a button or inside a specific column
    const target = event.target as HTMLElement;
  
    // Prevent row double-click if the target is a button, icon, or menu
    if (
      target.closest('button') ||
      target.closest('mat-menu') ||
      target.closest('.actions-header')
    ) {
      return;
    }
  
    // Set double-click flag to true
    this.isDoubleClick = true;

    // Clear the single-click timeout to prevent its execution
    clearTimeout(this.clickTimeout);

    // Trigger double-click action
    this.editElement(row);
  }


  onSearch() {
    this.resetPaginatorSort();
    this.getEventsData();
  }

  changeSticky(element: string) {
    return this.gridColumns.find(column => column.columnName === element)?.fixed;
  }
}
