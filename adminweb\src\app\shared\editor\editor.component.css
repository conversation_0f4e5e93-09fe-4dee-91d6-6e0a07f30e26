@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@400&display=swap');

.mat-error {
    margin-left: 3px;
    margin-bottom: 10px;
    margin-top: 7px;
    font-size: 15px;
    line-height: 1.125;
    font-family: <PERSON><PERSON>, 'Helvetica Neue', sans-serif;
    letter-spacing: normal;
    color: #f44336
}

.NgxEditor__Wrapper {
    max-width: 670px;  /* Limit the maximum width */
    width: 100%;       /* Ensure it takes up the full available space within the container */
    margin: 0 auto;    /* Center the editor container */
  }

ngx-editor {
    max-width: 100%;    /* Ensures it doesn't overflow the container */
    width: 100%;        /* Takes up full width of its parent */
    max-width: 600px;   /* Adjust this value as needed */
  }