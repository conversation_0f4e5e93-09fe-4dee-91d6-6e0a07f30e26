import { CommonModule } from '@angular/common';
import { Component, ElementRef, Inject, ViewChild } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';


@Component({
  selector: 'app-reports-details',
  standalone: true,
  imports: [
    TranslateModule,
    MatIconModule,
    MatDialogModule,
    CommonModule
  ],
  templateUrl: './reports-details.component.html',
  styleUrl: './reports-details.component.css'
})
export class ReportsDetailsComponent {
@ViewChild('slider') slider!: ElementRef;
    
      currentSlide = 0;
    
      constructor(
        public dialogRef: MatDialogRef<ReportsDetailsComponent>,
        @Inject(MAT_DIALOG_DATA) public data: any) {}
    
    
      // Slider navigation methods
      nextSlide(): void {
        if (this.currentSlide < this.data.data.images.length - 1) {
          this.currentSlide++;
        } else {
          this.currentSlide = 0; // Loop back to the first slide
        }
        this.updateSliderPosition();
      }
    
      prevSlide(): void {
        if (this.currentSlide > 0) {
          this.currentSlide--;
        } else {
          this.currentSlide = this.data.data.images.length - 1; // Loop to the last slide
        }
        this.updateSliderPosition();
      }
    
      goToSlide(index: number): void {
        this.currentSlide = index;
        this.updateSliderPosition();
      }
    
      updateSliderPosition(): void {
        if (this.slider && this.slider.nativeElement) {
          const translateX = -this.currentSlide * 100;
          this.slider.nativeElement.style.transform = `translateX(${translateX}%)`;
        }
      }
    
      onClose(): void {
        this.dialogRef.close();
      }
}
