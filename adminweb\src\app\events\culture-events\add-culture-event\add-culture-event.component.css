.wrapper {
    margin: 10px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    height: 90vh;
    max-height: 90vh;
    overflow: hidden;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    background-color: #FFFFFF;
  }
  
  .form-container {
    margin-right: 20px; /* Adjust as needed for spacing */
  }
  
  .crud-buttons{
    display: flex;
    margin-left: 28%;
  }

  /* Date and Time Container */
  .date-time-container {
    display: flex;
    gap: 15px;
    align-items: flex-start;
    margin-bottom: 20px;
  }

  .date{
   width: 300px !important;
  }

  .time{
    width: 200px !important;
  }

  /* Time field specific styling */
  .time input {
    font-family: 'Roboto Mono', monospace;
    font-size: 16px;
    letter-spacing: 1px;
    text-align: center;
    cursor: text;
  }

  .time input::placeholder {
    color: transparent; /* Hide placeholder when label is visible */
    opacity: 0;
  }

  /* Show placeholder only when field is focused and has no value */
  .time.mat-focused input::placeholder,
  .time input:focus::placeholder {
    color: #999;
    opacity: 0.7;
  }

  /* Ensure label positioning is correct */
  .time .mat-mdc-form-field-label {
    top: 50%;
    transform: translateY(-50%);
  }

  /* When focused or has value, move label up */
  .time.mat-focused .mat-mdc-form-field-label,
  .time.mat-form-field-should-float .mat-mdc-form-field-label {
    transform: translateY(-1.28125em) scale(0.75);
  }

  .time .mat-icon {
    color: #666;
    cursor: pointer;
  }

  /* Invalid time styling */
  .time.ng-invalid.ng-touched input {
    border-color: #f44336;
  }

  .time.ng-invalid.ng-touched .mat-form-field-outline-thick {
    color: #f44336;
  }
  

  .translated-languages {
    margin-left: 10px;
    max-width: 100%;
    margin-top: 20px;
  }
  
  .language-buttons {
    max-height: 200px;
    overflow-y: auto;
  }
  
  .language-button {
    margin-left: 3px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-basis: 115px; /* Control width per button */
    max-width: 100%;  /* Prevent buttons from growing larger than the container */
    margin-top: 10px;
    box-sizing: border-box; /* Ensures padding doesn't mess with the width */
  }

  
  
  .language-button button {
    margin-left: 10px; /* Add some space between the language name and the button */
  }
  
  .language-button mat-icon {
    font-size: 21px; /* Optional: Adjust icon size if needed */
  }

  .uploaded-images {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    max-height: 500px;
    overflow-y: auto;
  }
  
  .image-wrapper {
    position: relative;
    display: inline-block;
    margin-top: 9px;
  }
  
  .image-box {
    position: relative;
    display: flex;
    align-items: center;
  }
  
  .uploaded-image {
    width: 100px;
    height: 100px;
    object-fit: cover;
    border-radius: 5px;
  }
  
  .add-button-container {
    margin-left: 10px;
    margin-top: 25px;
    display: flex;
    justify-content: flex-start;
  }
  
  .add-button {
    width: 225px;
    height: 40px;
    border-radius: 5px;
    box-shadow: 0px 1px 3px 0px #0000004D;
    box-shadow: 0px 4px 8px 3px #00000026;
    background: #FFFFFF;
    margin-right: 10px;
  }

  .translate-button {
    width: 225px;
    height: 40px;
    border-radius: 5px;
    box-shadow: 0px 1px 3px 0px #0000004D;
    box-shadow: 0px 4px 8px 3px #00000026;
    background: #383838;
    color: white;
    margin-right: 10px;
  }
  
  
  .image-container {
    position: relative;
    display: inline-block;
    width: 80%;
    height: 80%;
    margin-top: 10px;
    max-width: 600px;
  }

  /* Add styling for the selected thumbnail */
  .selected-thumbnail {
    position: relative;
  }
  
  /* Add the "Cover" text indicator with more vibrant color */
  .selected-thumbnail::after {
    content: attr(data-label);
    position: absolute;
    bottom: 15px;
    left: 15px;
    background-color: var(--primary-color); /* Bright blue background */
    color: white;
    padding: 3px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
    z-index: 2;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3); /* Subtle shadow for better visibility */
  }
  
  /* Keep your existing shadow styling for the selected thumbnail */
  .selected-thumbnail .uploaded-image {
    box-shadow: 0 0 8px 3px rgba(21, 23, 27, 0.6);
    transition: all 0.2sease-in-out;
    border-radius: 8px;
  }
  
  .uploaded-image {
    width: 50%; /* Adjust width as needed */
    height: 50%; /* Adjust height as needed */
    object-fit: cover;
  }
  
  .remove-icon {
    position: absolute;
    top: -10px;
    background-color: rgba(255, 255, 255, 0.7); /* Slight white background for better visibility */
    border-radius: 50%;
    margin-left: 50%;
  }
  
  .example-card {
    display: flex;
    margin-top: 30px;
    margin-bottom: 30px;
  }
  
  .card-content-title {
    margin-bottom: 10px;
  }
  
  
  .header {
  padding-right: 200px;
  }
  
  .save-button{
    width: 135px;
    height: 40px;
    gap: 0px;
    border-radius: 5px;
    opacity: 0px;
    background-color: #383838 !important;
    color: white !important;
    box-shadow: 0px 1px 3px 0px #0000004D;
    box-shadow: 0px 4px 8px 3px #00000026;
    margin-right: 10px;
  }
  
  .cancel-button{
    width: 100px;
    height: 40px;
    gap: 0px;
    border-radius: 5px;
    opacity: 0px;
    background-color: #FFFFFF !important;
    box-shadow: 0px 1px 3px 0px #0000004D;
    box-shadow: 0px 4px 8px 3px #00000026;
  }
  
  .group-label {
    font-weight: bold;       /* Make the label bold */
    color: black;         /* Set a primary color */
    margin-top: 10px;       /* Add some space above */
    margin-bottom: 5px;     /* Add some space below */
    font-size: 1.1em;       /* Increase font size for visibility */
  }

  .date{
    display: flex;
    align-items: center;
    justify-content: flex-start;
    height: 63px;
    padding: 20px 15px 20px 10px;
  }

  .time{
    display: flex;
    align-items: center;
    justify-content: flex-start;
    height: 63px;
    padding: 20px 15px 20px 10px;
  }
  
  .heading{
    display: flex;
    align-items: center;
    justify-content: flex-start; /* Align items to the start */
    height: 63px;
    width: 711px; /* Use full width */
    padding: 20px 15px 20px 10px;
  }
  
  .header {
    display: flex;
    align-items: center;
    justify-content: space-between; /* Align items to the start */
    height: 63px;
    width: 100%; /* Use full width */
  }
  
  .header button {
    width: 100px;
    height: 40px;
    border-radius: 5px;
    margin-left: 10px;
    margin-right: 10px;
    box-shadow: 0px 1px 3px 0px #0000004D;
    box-shadow: 0px 4px 8px 3px #00000026;
  
  }
  
  .h1 {
    size: 20px;
    color: var(--primary-color);
    font-weight: bold;
  }
  
  .filter-container {
    align-items: center;
  }
  
  .wide-dropdown {
    width: 220px;
    margin-right: 10px;
  }
  
  .mat-form-field {
    width: 100%; /* Make the fields take full width */
    margin-right: 0; /* Remove horizontal margin */
  }
  
  
  
  .date,
  .time,
  .long-fields,
  .priority {
  width: calc(33% - 10px); /* Three fields per row with spacing between */
  }
  
  
  .long-fields {
  width: 211px;
  border: 1px;
  margin-right: 10px;
  }
  
  .priority {
    width: 167px;
    border-radius: 2px;
    border: 1px;
    margin-right: 10px;
  }
  
  .left-big-border {
    height: 33px;
  }
  
  .mat-mdc-text-field-wrapper {
    height: 0px;
    flex: auto;
    will-change: auto;
  }
  
  .main-content {
    display: flex;
    width: 100%; /* Use full width */
    justify-content: center;
  }
  
  .info-line {
    display: flex;
  }
  
  .value-text {
    font-weight: bold;
  }
  
  .demo-chart {
    height: 100%;
  }
  
  /* Table styling adjustments */
  .mat-mdc-header-cell {
    border-bottom: none;
  }
  
  .mat-mdc-cell {
    border-bottom: none;
  }
  
  .mdc-data-table__cell,
  .mdc-data-table__header-cell {
    padding: 0px;
  }
  
  .mat-mdc-header-cell.border-top-bottom {
    border-bottom: 1px solid #CCCCCC;
    border-top: 1px solid #CCCCCC;
  }
  
  /* Row height adjustments */
  mat-row,
  mat-footer-row {
    min-height: 28px;
  }
  
  .mat-mdc-table mat-header-row.mat-mdc-header-row,
  .mat-mdc-table mat-row.mat-mdc-row,
  .mat-mdc-table mat-footer-row.mat-mdc-footer-cell {
    height: 28px;
  }
  
  /* Button styling */
  .button-more {
    width: 135px;
    text-align: center;
    background-color: #383838;
    color: white;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }
  
  .center-cell {
    display: flex;
    justify-content: center;
  }
  
  .end-cell {
    display: flex;
    justify-content: flex-end;
  }
  
  /* Adjust text field wrapper height */
  :host ::ng-deep .mat-mdc-text-field-wrapper {
    height: 44px;
  }
  
  :host ::ng-deep .mdc-text-field--outlined .mat-mdc-form-field-infix,
  :host ::ng-deep .mdc-text-field--no-label .mat-mdc-form-field-infix {
    padding-top: 12px;
  }
  
  /* Responsive adjustments */
  @media screen and (max-width: 768px) {
    .wrapper {
      overflow-y: auto;
      margin: 10px 5px; /* Adjusted margin for better spacing */
    }
  
    .header {
      width: 100%;
      padding: 10px;
    }
  
    .h1 {
      margin-left: 0px;
      font-size: 18px; /* Slightly smaller font size for smaller screens */
      margin-top: 10px; /* Space between button and heading */
    }
  
    .main-content {
      flex-direction: column;
      padding: 10px; /* Added padding for better spacing */
      width: 100%;
    }
  
    .filter-container {
      display: flex;
      flex-direction: column; /* Stack elements vertically */
      align-items: flex-start; /* Align elements to the start */
      gap: 15px; /* Add spacing between elements */
    }

    .date-time-container {
      flex-direction: column;
      gap: 10px;
    }
  
    .wide-dropdown,
  .mat-form-field,
  .date,
  .time,
  .long-fields,
  .description,
  .search,
  .priority {
  width: 100%;
  margin-right: 0;
  margin-bottom: 10px; /* Added margin to separate elements */
  }
  
  }