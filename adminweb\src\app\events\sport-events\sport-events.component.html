<form [formGroup]="sportEventsFilterForm">
    <div class="wrapper">
      <div class="header">
          <div class="filter-container">
  
          <!-- search -->
          <mat-form-field appearance="outline" class="mat-form-field">
              <mat-label>{{'SearchFilter' | translate}}</mat-label>
              <input matInput formControlName="search">
          </mat-form-field>
            
            <!-- fromDate -->
            <mat-form-field appearance="outline" class="mat-form-field">
                <mat-label>{{'FromDate' | translate}}</mat-label>
                <input matInput formControlName="fromDate" [matDatepicker]="picker">
                <mat-datepicker-toggle matIconSuffix [for]="picker"></mat-datepicker-toggle>
                <mat-datepicker #picker></mat-datepicker>
            </mat-form-field>
  
            <!-- toDate -->
            <mat-form-field appearance="outline" class="mat-form-field">
                <mat-label>{{'ToDate' | translate}}</mat-label>
                <input matInput formControlName="toDate" [matDatepicker]="toPicker">
                <mat-datepicker-toggle matSuffix [for]="toPicker"></mat-datepicker-toggle>
                <mat-datepicker #toPicker></mat-datepicker>
            </mat-form-field>
  
              <button mat-icon-button type="submit" class="search-button" (click)="onSearch()">
                  <mat-icon>search</mat-icon>
              </button>
          </div>
      </div>
      <mat-divider></mat-divider>
  
      <div class="main-content">
          <div class="title">
              <span class="left-big-border"></span>
              <h1 class="h1">{{ 'sportEvents' | translate }}</h1>
          </div>
  
          <div class="add-button-container">
              <button mat-raised-button tabindex="1" class="add-button" (click)="openAddLandmarkDialog()">
                  <mat-icon>add</mat-icon>
                  <span>{{ 'AddNewSportEvent' | translate }}</span>
              </button>
          </div>
      </div>
  
      <div class="card-column">
        <mat-card class="custom-card table-container">
            <div class="mat-elevation-z8 custom-table">
              <table mat-table [dataSource]="dataSource" matSort (matSortChange)="onSortChange($event)">
  
               <!-- ID Column -->
                <ng-container matColumnDef="id">
                  <th mat-header-cell *matHeaderCellDef mat-sort-header class="custom-header"> ID </th>
                  <td mat-cell *matCellDef="let element" class="custom-cell"> {{element.id}} </td>
                </ng-container>

                <!-- Name Column -->
                <ng-container matColumnDef="name">
                  <th mat-header-cell *matHeaderCellDef mat-sort-header class="custom-header"> {{'name' | translate}} </th>
                  <td mat-cell *matCellDef="let element" class="custom-cell truncate" matTooltip="{{element.name}}"> {{element.name}} </td>
                </ng-container>

                <!-- Start Date Column -->
                <ng-container matColumnDef="startDate">
                  <th mat-header-cell *matHeaderCellDef mat-sort-header class="custom-header"> {{'date' | translate}} </th>
                  <td mat-cell *matCellDef="let element" class="custom-cell"> {{element.startDate | date}} </td>
                </ng-container>

                <!-- Created By Column -->
                <ng-container matColumnDef="createdBy">
                  <th mat-header-cell *matHeaderCellDef mat-sort-header class="custom-header"> {{'createdBy' | translate}} </th>
                  <td mat-cell *matCellDef="let element" class="custom-cell"> {{element.createdBy}} </td>
                </ng-container>

                <!-- Created Date Column -->
                <ng-container matColumnDef="createdDate">
                  <th mat-header-cell *matHeaderCellDef mat-sort-header class="custom-header"> {{'createdDate' | translate}} </th>
                  <td mat-cell *matCellDef="let element" class="custom-cell"> {{element.createdDate | date}} </td>
                </ng-container>

                <!-- Last Modified By Column -->
                <ng-container matColumnDef="lastModifiedBy">
                  <th mat-header-cell *matHeaderCellDef mat-sort-header class="custom-header"> {{'lastModifiedBy' | translate}} </th>
                  <td mat-cell *matCellDef="let element" class="custom-cell"> {{element.lastModifiedBy}} </td>
                </ng-container>

                <!-- Last Modified Date Column -->
                <ng-container matColumnDef="lastModifiedDate">
                  <th mat-header-cell *matHeaderCellDef mat-sort-header class="custom-header"> {{'lastModifiedDate' | translate}} </th>
                  <td mat-cell *matCellDef="let element" class="custom-cell"> {{element.lastModifiedDate | date}} </td>
                </ng-container>
              
                <!-- Actions Column -->
                <ng-container matColumnDef="actions">
                  <th mat-header-cell *matHeaderCellDef class="custom-header actions-header"> {{'actions' | translate}} </th>
                  <td mat-cell *matCellDef="let element" class="custom-cell">
                    <button mat-icon-button [matMenuTriggerFor]="menu">
                      <mat-icon>more_horizontal</mat-icon>
                    </button>
                    <mat-menu #menu="matMenu">
                      <div class="menu-actions">
                        <button mat-menu-item (click)="openPreview(element)" class="preview-button">
                          <mat-icon>phone_iphone</mat-icon>
                        </button>
                        <button mat-menu-item (click)="viewDetails(element)" class="details-button">
                          <mat-icon>info</mat-icon>
                        </button>
                        <button mat-menu-item (click)="editElement(element)" class="edit-button">
                          <mat-icon>edit</mat-icon>
                        </button>
                        <button mat-menu-item (click)="deleteElement(element)" class="delete-button">
                          <mat-icon>delete</mat-icon>
                        </button>
                      </div>
                    </mat-menu>
                  </td>
                </ng-container>
              
                <tr mat-header-row *matHeaderRowDef="displayedColumns; sticky: true" class="custom-row"></tr>
                <tr mat-row *matRowDef="let row; columns: displayedColumns;" class="custom-row" (click)="onRowClick($event, row)" 
                (dblclick)="onRowDoubleClick($event, row)"></tr>
              
              </table>
  
  
            </div>
        </mat-card>
        <div class="mat-paginator-sticky table-footer">
          <div class="buttons table-footer-left">
           
           </div>
           <div>
            <mat-paginator 
              [length]="totalCount"
              [pageSize]="paginationSort.pageSize"
              [pageSizeOptions]="[25, 50, 100]"
              showFirstLastButtons
              (page)="onPageChange($event)"
              aria-label="Select page of periodic elements">
            </mat-paginator>
           </div>
        </div>
      </div>
  
    </div>
  </form>