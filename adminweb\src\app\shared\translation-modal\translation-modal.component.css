/* Modal container */
.contentPanel {
    padding: 2px;
    width: 100%;
    border-top: 8px solid var(--primary-color);
    justify-content: space-between;
    border-bottom: 1px solid #ccc;
    background-color: #fff;
  }
  
  /* Header with title and close button */
  .panelHead {
    margin: 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  
  /* Modal Title */
  .h1 {
    text-align: center;
    color: var(--color-main);
    width: 100%;
    border-bottom: 1px solid var(--color-gray-border) !important;
    margin: 0;
    font-family: 'Roboto', sans-serif;
    font-size: 20px;
    font-weight: 700;
    line-height: 16px;
  }

  .data {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  }

  .translate-button {
    border-radius: 5px;
    box-shadow: 0px 1px 3px 0px #0000004D;
    box-shadow: 0px 4px 8px 3px #00000026;
    color: black;
    background-color: white;
  }
  
  /* Close button */
  .close-icon {
    cursor: pointer;
    font-size: 35px;
    border: none;
    background-color: transparent;
    color: var(--color-main);
    transition: color 0.3s ease;
  }
  
  .close-icon:hover {
    color: var(--primary-color);
  }
  
  /* Form container */
  .details-report-content-row {
    display: flex;
    flex-direction: column;
    gap: 15px;
    padding: 20px;
  }
  
  /* Language dropdown */
  .language-select {
    margin: 10px 0;
  }
  
  /* Input Fields */
  .heading {
    margin-top: 15px;
    margin-left: 3px;
  }
  
  .content {
    margin-left: 50px;
  }

  .heading h3,
.content h3 {
  font-size: 1rem; /* Bigger font size */
  font-weight: bold; /* Make it bold */
  color: #333; /* Darker text */
  margin-bottom: 8px; /* Add some spacing */
  text-transform: uppercase; /* Make text uppercase */
  letter-spacing: 1px; /* Slight letter spacing */
  padding-bottom: 4px;
}

.heading p,
.content p {
  font-size: 0.9rem; /* Slightly bigger text */
  color: #555; /* Softer text color */
  margin-top: 4px;
}
  
  /* Buttons */
  .button-container {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
  }
  
  .mat-mdc-button {
    font-weight: bold;
  }

  .save{
    background-color: var(--secondary-color);
    color: white;
    margin-right: 5px;
  }
  
  .cancel {
    background-color: white;
    color: var(--secondary-color);
  }

  .buttons {
    display: flex;
    justify-content: center;
  }

  .editable-text {
    font-size: 0.9rem; /* Slightly bigger text */
  color: #555; /* Softer text color */
  margin-top: 4px;
  }
  
  .editable-text:empty:before {
    content: attr(placeholder);
    color: #aaa;
  }
 
  /* Optional: Customize the mat-form-field appearance */
.translated-heading-field {
  width: 298%; /* Ensure the input takes up full width */
  margin-bottom: 16px; /* Add space between elements */
}

.translate-button:disabled {
  background-color: #cccccc;
  color: #888888;
  cursor: not-allowed;
}

.save:disabled {
  background-color: #cccccc;
  color: #888888;
  cursor: not-allowed;
}

:host ::ng-deep .mat-mdc-text-field-wrapper {
  height: 44px;
}

  
  
  