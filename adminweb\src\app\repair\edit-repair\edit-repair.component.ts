import { CommonModule } from '@angular/common';
import { ChangeDetector<PERSON><PERSON>, Component, <PERSON>ement<PERSON>ef, On<PERSON><PERSON>roy, OnInit, QueryList, ViewChild, ViewChildren, ViewContainerRef } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { MatDividerModule } from '@angular/material/divider';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { GoogleMapsModule } from '@angular/google-maps';
import { ActivatedRoute, Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { GoogleMapsService } from '../../core/services/google.maps.service';
import { MapGetModel } from '../../shared/interfaces/map/map-get.model';
import { environment } from '../../../environments/enviroment';
import { MatFormFieldModule } from '@angular/material/form-field';
import { Form<PERSON><PERSON>er, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatInputModule } from '@angular/material/input';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule, MatOptionModule } from '@angular/material/core';
import { MatSelectModule } from '@angular/material/select';
import { MatCardModule } from '@angular/material/card';
import { EditorComponent } from '../../shared/editor/editor.component';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MessageService } from '../../core/services/message.service';
import { RepairService } from '../../core/services/repairs.service';
import { Validators } from 'ngx-editor';
import { TranslationModalComponent } from '../../shared/translation-modal/translation-modal.component';
import { TranslationDeleteModalComponent } from '../../shared/translation-delete-modal/translation-delete-modal.component';
import { RepairGetModelGetByIdModel } from '../../shared/interfaces/repairs/repair-get-by-id.model';
import { ParkngSimpleBorderPoint } from '../../shared/interfaces/parking/parking-simple-border-point.model';

@Component({
  selector: 'app-edit-repair',
  standalone: true,
  imports: [
  TranslateModule,
    MatIconModule,
    MatDialogModule,
    GoogleMapsModule,
    CommonModule,
    MatDividerModule,
    MatButtonModule,
    MatFormFieldModule,
    ReactiveFormsModule,
    MatInputModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatOptionModule,
    MatSelectModule,
    MatCardModule,
    EditorComponent,
    FormsModule,
    MatSlideToggleModule
  ],
  templateUrl: './edit-repair.component.html',
  styleUrl: './edit-repair.component.css'
})
export class EditRepairComponent {
  @ViewChild('mapContainer') mapContainer!: ElementRef;
  @ViewChild('markerContainer', { read: ViewContainerRef, static: true }) markerContainer!: ViewContainerRef;
  @ViewChildren('borderPointRow') borderPointRows!: QueryList<ElementRef>;
  @ViewChild('fileInput') fileInput: ElementRef | undefined;

  repairEditFilterForm!: FormGroup;
   protected content = '';
  protected imageSrcs: { 
    id?: number;
     fileName: string; 
     contentType: string; 
     extension: string; 
     content: string; 
     isCover: boolean; 
  }[] = [];
  protected translatedLanguages: { languageShort: string, languageFullName: string, heading: string, content: string }[] = [];
  protected selectedThumbnailIndex: number | null = null;
   protected showImages: boolean = true;
   protected repair: any;

  protected repairTypesMock = [
    { id: 0, name: 'Пътен ремонт' }
  ];

  constructor(
    private googleMapsService: GoogleMapsService,
    private route: ActivatedRoute,
    private router: Router,
    private changeDetectorRef: ChangeDetectorRef,
    private fb: FormBuilder,
    public dialog: MatDialog, 
    private messageService: MessageService,
    private repairService: RepairService
  ) {
    this.repairEditFilterForm = this.fb.group({
      id: [''],
      fromDate: [null],
      toDate: [null],
      heading: ['', [Validators.maxLength(60)]],
      templateContent: [''],
      thumbnailIndex: [null]
     });
  }

  protected data: { radius: number | string | null, borderPoints: any[] } = { radius: null, borderPoints: [] };
  protected mapHeight: number = 0;
  protected mapWidth: number = 0;
  protected center!: google.maps.LatLngLiteral;
  protected zoom = 13;
  private map: google.maps.Map | undefined;
  private dayMapId = environment.dayMapId;
  //private aggregatedGroupsData: AggregatedGroupModel[] = [];
  private marker: google.maps.marker.AdvancedMarkerElement | undefined;
  private currentMarker: any = null;
  protected clickedCoordinates: { latitude: number, longitude: number, index: number}[] = [];
  private currentMarkers: google.maps.marker.AdvancedMarkerElement[] = [];
  private polyline: google.maps.Polyline | undefined;
  private markerPositions: { latitude: number; longitude: number }[] = [];
  protected repairId!: number;
  protected highlightedIndex: number | null = null;
  protected firstMarkerIcon = {
    url: 'assets/images/end-point-marker.svg',
  };
  protected lastMarkerIcon = {
    url: 'assets/images/start-point-marker.svg',
  };
  private isFirstTime = true;

  private repairDataSubscription!: Subscription;

 async ngOnInit() {
  this.initializeGoogleMaps();

  this.route.paramMap.subscribe((params) => {
    const id = params.get('id');
    if (id) {
      this.repairId = Number(id);
      // Load repair data - this will handle area points from API
      this.loadRepairData(Number(id));
    }
  });

  // Only handle basic repair info from query params (heading, content, etc.)
  this.route.queryParams.subscribe((queryParams) => {
    this.repair = {
      heading: queryParams['heading'],
      content: queryParams['content'],
    };

    if (this.repair && this.repair.heading) {
      const htmlContent = `<p>${this.repair.content}</p>`;
      this.content = htmlContent;

      this.repairEditFilterForm.patchValue({
        id: this.repairId,
        heading: this.repair.heading,
        templateContent: htmlContent
      });
    }
  });
}

  get form() {
    return this.repairEditFilterForm.controls;
  }

 private loadRepairData(id: number): void {
  this.repairService.getRepairById(id).subscribe({
    next: (data: RepairGetModelGetByIdModel) => {
      console.log('Repair data loaded:', data);
      
      // Handle basic form data
      let startDate: Date | null = null;
      let endDate: Date | null = null;
      
      if (data.startDate) {
        startDate = new Date(data.startDate);
      }
      
      if (data.endDate) {
        endDate = new Date(data.endDate);
      }

      this.repairEditFilterForm.patchValue({
        id: data.id,
        heading: data.name,
        templateContent: data.description,
        fromDate: startDate,
        toDate: endDate
      });

      if (data.description) {
        this.content = data.description;
      }

      // Handle images
      if (data.images) {
        this.imageSrcs = data.images.map(img => ({
          id: img.id,
          fileName: img.key,
          contentType: 'image/png',
          extension: img.key.split('.').pop() || '',
          content: img.preSignedUrl,
          isCover: img.isCover
        }));
        
        const coverIndex = this.imageSrcs.findIndex(img => img.isCover);
        this.selectedThumbnailIndex = coverIndex !== -1 ? coverIndex : null;
        
        this.repairEditFilterForm.patchValue({
          thumbnailIndex: this.selectedThumbnailIndex
        });
      }

      // Handle translations
      if (data.translations && data.translations.length > 0) {
        this.translatedLanguages = data.translations.map(translation => ({
          languageShort: translation.languageCode || 'en',
          languageFullName: translation.languageName || 'English',
          heading: translation.name || '',
          content: translation.description || ''
        }));
      }

      // Handle area points from API (EXACT SAME LOGIC AS PARKING BORDER POINTS)
      if (data.areaPoints && data.areaPoints.length > 0) {
        console.log('Area points found in API data:', data.areaPoints);
        
        // Map area points to clicked coordinates (same as parking)
        this.clickedCoordinates = data.areaPoints.map(point => ({
          latitude: point.latitude,
          longitude: point.longitude,
          index: point.index
        }));

        // Set center from first area point (same as parking)
        this.center = {
          lat: data.areaPoints[0].latitude,
          lng: data.areaPoints[0].longitude
        };

        console.log('Area points loaded from API:', this.clickedCoordinates);
        console.log('Map center set to:', this.center);
        
        // Reinitialize map with area points
        this.reinitializeMapWithAreaPoints();
        
      } else if (data.latitude && data.longitude) {
        // Handle single coordinates (same as parking single point logic)
        console.log('Creating single area point from main repair coordinates');
        
        const singleAreaPoint = {
          index: 0,
          latitude: data.latitude,
          longitude: data.longitude
        };

        this.clickedCoordinates = [singleAreaPoint];
        
        this.center = {
          lat: data.latitude,
          lng: data.longitude
        };

        console.log('Single area point created from API data:', singleAreaPoint);
        
        // Reinitialize map with single point
        this.reinitializeMapWithAreaPoints();
        
      } else {
        console.log('No area points or coordinates found in API data, using default center');
        this.handleMissingAreaPoints();
      }
    },
    error: (error) => {
      console.error('Error fetching repair details:', error);
      this.handleMissingAreaPoints();
    }
  });
}
  
private handleMissingAreaPoints(): void {
  this.center = {
    lat: 42.482798,  // Default to Yambol coordinates
    lng: 26.503206
  };
  
  console.log('Using default center coordinates:', this.center);
  this.clickedCoordinates = [];
}

 private async reinitializeMapWithAreaPoints(): Promise<void> {
  try {
    // Clear existing markers and polyline
    this.clearMapElements();
    
    // Wait to ensure the map is ready
    await new Promise(resolve => setTimeout(resolve, 100));
    
    // Set the map center and zoom
    if (this.map && this.center) {
      this.map.setCenter(this.center);
      this.map.setZoom(this.zoom);
    }
    
    // Add markers for each area point (exact same as parking)
    this.clickedCoordinates.forEach((points: { latitude: number; longitude: number }, index: number) => {
      let pointsGoogle: google.maps.LatLngLiteral = { lat: points.latitude, lng: points.longitude };

      let iconUrl: string | undefined = undefined;
      if (index === 0) {
        iconUrl = this.firstMarkerIcon.url;
      } else if (index === this.clickedCoordinates.length - 1 && index !== 0) {
        iconUrl = this.lastMarkerIcon.url;
      }

      const newMarker = this.googleMapsService.addAdvancedMarker(
        pointsGoogle,
        index,
        undefined,
        undefined,
        iconUrl,
        undefined,
        true
      );

      newMarker.addListener('click', () => {
        this.onMarkerClick(points.latitude, points.longitude);
      });

      this.currentMarkers.push(newMarker);
      this.markerPositions.push({ latitude: points.latitude, longitude: points.longitude });
    });

    // Update polyline path
    this.updatePolylinePath();
    
    console.log('Map reinitialized with area points');
    
  } catch (error) {
    console.error('Error reinitializing map with area points:', error);
  }
}

private clearMapElements(): void {
  this.currentMarkers.forEach(marker => {
    if (marker.map) {
      marker.map = null;
    }
  });
  this.currentMarkers = [];
  this.markerPositions = [];
  
  if (this.polyline) {
    this.polyline.setPath([]);
  }
}

 
   private updatePolylinePath() {
     if (this.polyline) {
       const path = this.clickedCoordinates.map(coord => new google.maps.LatLng(coord.latitude, coord.longitude));
       this.polyline.setPath(path);
     }
   }
 
   private scrollToHighlighted() {
     
     if (this.highlightedIndex !== null) {
       const element = this.borderPointRows.toArray()[this.highlightedIndex].nativeElement;
       element.scrollIntoView({ behavior: 'smooth', block: 'nearest', inline: 'start' });
       this.changeDetectorRef.detectChanges();
     }
       
   }

 
editRepair() {
  this.updateCoverImage();

  // Update form with current content
  this.repairEditFilterForm.patchValue({
    templateContent: this.content
  });

  const templateContent = this.cleanContent(this.repairEditFilterForm.value.templateContent);

  if (this.repairEditFilterForm.valid && templateContent.length > 0 && this.clickedCoordinates.length > 0) {
    
    // Patch form data with thumbnail selection
    this.repairEditFilterForm.patchValue({ thumbnailIndex: this.selectedThumbnailIndex });

    // Initialize storage for files and existing images
    let coverFile: File | null = null;
    let coverImageId: number | null = null;
    const files: File[] = [];
    const existingImageIds: number[] = [];

    // Process images: differentiate between new and existing images
    this.imageSrcs.forEach((src, index) => {
      if (src.id) {
        // Existing image: check if it's the cover
        if (index === this.selectedThumbnailIndex) {
          coverImageId = src.id;
        } else {
          existingImageIds.push(src.id);
        }
      } else {
        // New image: convert base64 to file
        const byteArray = this.convertBase64ToByteArray(src.content);
        const blob = new Blob([byteArray], { type: src.contentType });
        const file = new File([blob], src.fileName, { type: src.contentType });

        if (index === this.selectedThumbnailIndex) {
          coverFile = file;
        } else {
          files.push(file);
        }
      }
    });

    // Prepare translation data
    const translations = this.translatedLanguages.map(translation => ({
      name: translation.heading,
      description: translation.content,
      languageName: translation.languageFullName,
      languageCode: translation.languageShort,
    }));

    // Prepare area points
    const areaPoints = this.clickedCoordinates.map((coord, index) => ({
      index: index,
      latitude: coord.latitude,
      longitude: coord.longitude,
    }));

    // Prepare form data
    const formData = new FormData();
    formData.append('id', this.repairId.toString());
    formData.append('Name', this.repairEditFilterForm.value.heading);
    formData.append('Description', templateContent);

    // Handle dates
    if (this.repairEditFilterForm.value.fromDate) {
      const fromDate = this.repairEditFilterForm.value.fromDate;
      const startDate = fromDate instanceof Date ? fromDate : new Date(fromDate);
      formData.append('StartDate', startDate.toISOString());
    }

    if (this.repairEditFilterForm.value.toDate) {
      const toDate = this.repairEditFilterForm.value.toDate;
      const endDate = toDate instanceof Date ? toDate : new Date(toDate);
      formData.append('EndDate', endDate.toISOString());
    }

    // Append existing image IDs
    existingImageIds.forEach((id, index) => {
      formData.append(`ExistingImageIds[${index}]`, id.toString());
    });

    // Append translation data
    translations.forEach((translation, index) => {
      formData.append(`Translations[${index}].name`, translation.name);
      formData.append(`Translations[${index}].description`, translation.description);
      formData.append(`Translations[${index}].languageCode`, translation.languageCode);
      formData.append(`Translations[${index}].languageName`, translation.languageName);
      formData.append(`Translations[${index}].repairId`, this.repairId.toString());
    });

    // Append new images (excluding the cover)
    files.forEach((file, index) => {
      formData.append(`Files[${index}]`, file, file.name);
    });

    // Append cover image (either as an ID or a new file)
    if (coverImageId !== null) {
      formData.append('ExistingCoverId', String(coverImageId));
    } else if (coverFile !== null) {
      formData.append('Cover', coverFile);
    }

    // Add area points
    areaPoints.forEach((point, index) => {
      formData.append(`AreaPoints[${index}].index`, point.index.toString());
      formData.append(`AreaPoints[${index}].latitude`, point.latitude.toString());
      formData.append(`AreaPoints[${index}].longitude`, point.longitude.toString());
    });

    // Submit to backend
    this.repairService.editRepair(formData).subscribe({
      next: (response) => {
        console.log('Repair updated successfully', response);
        this.messageService.showMessage(["UpdateSuccessfully"], 'success');
        this.router.navigate(['/repair']);
      },
      error: (error) => {
        console.error('Error updating repair', error);
        this.messageService.showMessage(["ErrorUpdatingRepair"], 'error');
      }
    });

  } else {
    // Handle validation errors
    let errorMessage = [];
    
    if (!this.repairEditFilterForm.valid) {
      errorMessage.push("PleaseCompleteTheForm");
    }
    
    if (templateContent.length === 0) {
      errorMessage.push("PleaseAddContent");
    }
    
    if (this.clickedCoordinates.length === 0) {
      errorMessage.push("PleaseAddAreaPoints");
    }
    
    this.messageService.showMessage(errorMessage, 'error');
  }
}

   convertBase64ToByteArray(base64: string): Uint8Array {
     const binaryString = atob(base64.split(',')[1]); // Decode base64 string
     const byteArray = new Uint8Array(binaryString.length);
     for (let i = 0; i < binaryString.length; i++) {
       byteArray[i] = binaryString.charCodeAt(i);
     }
     return byteArray;
   }
 
 
   deleteBorderPoint(index: number) {
     console.log(this.clickedCoordinates)
     const removedPoint = this.clickedCoordinates.splice(index, 1)[0];
     console.log(this.clickedCoordinates)
 
     const markerIndex = this.markerPositions.findIndex(position =>
       position.latitude === removedPoint.latitude && position.longitude === removedPoint.longitude
     );
 
     if (markerIndex > -1) {
       this.currentMarkers[markerIndex].map = null;  
       this.currentMarkers.splice(markerIndex, 1);
       this.markerPositions.splice(markerIndex, 1);
     }
 
     // Update indexes of remaining points and markers
     this.clickedCoordinates.forEach((coord, i) => {
       coord.index = i; // Realign indexes
     });
 
 
     if (this.highlightedIndex === index) {
       this.highlightedIndex = null;
     } else if (this.highlightedIndex !== null && this.highlightedIndex > index) {
       this.highlightedIndex--;
     }
 
     this.changeDetectorRef.detectChanges();
 
     this.updatePolylinePath();
   }
 
   async initializeGoogleMaps() {
     try {
       await this.googleMapsService.loadLibraries();
 
       this.center = {
         lat: 42.482798, 
         lng: 26.503206 
 
       };
 
       this.map = await this.googleMapsService.initializeMap(
         this.mapContainer.nativeElement,
         this.center,
         this.zoom,
         this.dayMapId,
         'day'
       );
 
       this.polyline = new google.maps.Polyline({
         map: this.map,
         path: [],
         strokeColor: '#800080',
         strokeOpacity: 1.0,
         strokeWeight: 2,
       });
 
       this.clickedCoordinates.forEach((points: { latitude: number; longitude: number }, index: number) => {
         let pointsGoogle: google.maps.LatLngLiteral = { lat: points.latitude, lng: points.longitude };
 
         let iconUrl: string | undefined = undefined;
         if (index === 0) {
           iconUrl = this.firstMarkerIcon.url;
         } else if (index === this.clickedCoordinates.length - 1 && index !== 0) {
           iconUrl = this.lastMarkerIcon.url;
         }
 
         const newMarker = this.googleMapsService.addAdvancedMarker(
           pointsGoogle,
           index,
           undefined,
           undefined,
           iconUrl,
           undefined,
           true
         );
 
         newMarker.addListener('click', () => {
           this.onMarkerClick(points.latitude, points.longitude);
         });
 
         this.currentMarkers.unshift(newMarker); // Using push here
         this.markerPositions.unshift({ latitude: points.latitude, longitude: points.longitude });
       });
 
       this.updatePolylinePath();
 
       this.map.addListener('click', (event: google.maps.MapMouseEvent) => {
         if (!event.latLng) return; // Ensure the click event has coordinates
       
         const clickedPosition: google.maps.LatLngLiteral = {
           lat: event.latLng.lat(),
           lng: event.latLng.lng(),
         };
       
         if (this.currentMarkers.length > 0) {
           if (this.isFirstTime) {
             // First time: Remove the last marker
             const lastMarker = this.currentMarkers[this.currentMarkers.length - 1];
             this.removeMarker(lastMarker);
             this.isFirstTime = false;
           } else {
             // Subsequent times: Remove the first marker
             const firstMarker = this.currentMarkers[0];
             this.removeMarker(firstMarker);
           }
         }
       
         // Create new marker at clicked position
         this.currentMarker = this.createMarker(clickedPosition, this.firstMarkerIcon.url);
       
         // Store marker data
         this.currentMarkers.unshift(this.currentMarker);
         this.markerPositions.unshift({ latitude: clickedPosition.lat, longitude: clickedPosition.lng });
       
         this.clickedCoordinates.unshift({
           latitude: clickedPosition.lat,
           longitude: clickedPosition.lng,
           index: 0
         });
       
         // Update indexes
         this.clickedCoordinates.forEach((coord, i) => {
           coord.index = i;
         });
   
         this.changeDetectorRef.detectChanges();
       
         // Add click listener to marker
         this.currentMarker.addListener('click', () => {
           this.onMarkerClick(clickedPosition.lat, clickedPosition.lng);
         });
       
         // Update polyline if applicable
         this.updatePolylinePath();
       });
       
     } catch (error) {
       console.error('Error loading Google Maps:', error);
     }
   }
 
   onMarkerClick(latitude: number, longitude: number) {
     const index = this.clickedCoordinates.findIndex(point =>
         point.latitude === latitude && point.longitude === longitude
     );
 
     if (index !== -1) {
         this.highlightedIndex = index;
         this.scrollToHighlighted();
     }
   }
 
   async ngAfterViewInit(): Promise<void> {
     this.googleMapsService.setViewContainerRef(this.markerContainer);
   }

  protected onBack() {
    this.router.navigate(['/repair']);
  } 
  
  
  private createMarker(position: google.maps.LatLngLiteral, iconUrl: string): google.maps.marker.AdvancedMarkerElement {
 
     const newMarker = this.googleMapsService.addAdvancedMarker(
       position,
       2,
       undefined,
       undefined,
       iconUrl, 
       undefined,
       true
     );
   
     return newMarker;
   }
     
 
   private removeMarker(marker: google.maps.marker.AdvancedMarkerElement): void {
     const position = marker.position as google.maps.LatLngLiteral; 
 
     marker.map = null; 
 
     const markerIndex = this.currentMarkers.indexOf(marker);
     if (markerIndex > -1) {
       this.currentMarkers.splice(markerIndex, 1);  
       this.markerPositions.splice(markerIndex, 1); 
     }
 
     const newMarker = this.googleMapsService.addAdvancedMarker(
       position,
       2,
     );
   
     this.currentMarkers.splice(markerIndex, 0, newMarker);
 
     // Insert the position at the same index in markerPositions array
     this.markerPositions.splice(markerIndex, 0, { latitude: position.lat, longitude: position.lng });
 
     newMarker.addListener('click', () => {
     this.onMarkerClick(position.lat, position.lng);
   });
     
   }
 
    selectThumbnail(index: number) {
       this.selectedThumbnailIndex = index;
       this.repairEditFilterForm.patchValue({
         thumbnailIndex: this.selectedThumbnailIndex,
       });
     }

     private updateCoverImage() {
     if (this.imageSrcs.length > 0 && this.selectedThumbnailIndex !== null) {
       // Reset isCover for all images
       this.imageSrcs.forEach((img, i) => {
         img.isCover = i === this.selectedThumbnailIndex;
       });
     }
   }
   
     onChangeContent() {
       const templateContentControl = this.repairEditFilterForm.get('templateContent');
       if (templateContentControl) {
         templateContentControl.setValue(this.content);
       }
     }
   
     openFileDialog() {
       if (this.fileInput) {
         this.fileInput.nativeElement.click();
       }
     }
   
     onFileSelected(event: Event) {
      const fileInput = event.target as HTMLInputElement;
      if (fileInput.files && fileInput.files.length > 0) {
        Array.from(fileInput.files).forEach((file: File) => {
          const reader = new FileReader();
    
          // Read the file as a data URL to get the content in base64
          reader.onload = (e: any) => {
            // Create a file object with dynamically populated properties
            const fileDetails = {
              fileName: file.name,              // Get the file name
              contentType: file.type,           // Get the content type (MIME type)
              extension: file.name.split('.').pop() || '', // Extract the file extension
              content: e.target.result,         // Base64 encoded content of the file
              isCover: false                    // Initialize isCover to false
            };
    
            // Push the file details to the array
            this.imageSrcs.push(fileDetails);
    
            // If this is the first image, automatically set it as the thumbnail and cover
            if (this.imageSrcs.length === 1) {
              this.selectedThumbnailIndex = 0;
              this.imageSrcs[0].isCover = true;
            }
            
            // Update the form control
            this.repairEditFilterForm.patchValue({
              thumbnailIndex: this.selectedThumbnailIndex
            });
          };
    
          // Read the file as base64
          reader.readAsDataURL(file);
        });
      }
    }
     
      removePhoto(index: number) {
      const removedWasCover = this.imageSrcs[index].isCover;
      this.imageSrcs.splice(index, 1); // Remove image at the given index
 
      if (this.fileInput) {
       this.fileInput.nativeElement.value = '';
     }
      
      // Update thumbnail selection if needed
      if (this.selectedThumbnailIndex === index) {
        // If we removed the selected thumbnail, select the first image if available
        this.selectedThumbnailIndex = this.imageSrcs.length > 0 ? 0 : null;
        
        // If the removed image was the cover and we have other images, set the new selection as cover
        if (removedWasCover && this.imageSrcs.length > 0 && this.selectedThumbnailIndex !== null) {
          this.imageSrcs[this.selectedThumbnailIndex].isCover = true;
        }
      } else if (this.selectedThumbnailIndex !== null && index < this.selectedThumbnailIndex) {
        // If we removed an image before the selected one, update the index
        this.selectedThumbnailIndex--;
      }
      
      // Update the form control
      this.repairEditFilterForm.patchValue({
        thumbnailIndex: this.selectedThumbnailIndex
      });
    }
  
    openTranslationModal(language?: string) {
       
     let heading;
     let content;
 
     if(language === undefined) {
      heading = this.repairEditFilterForm.value.heading?.trim();
      content = this.repairEditFilterForm.value.templateContent?.trim(); // Read from the form
     } else if(language !== undefined) {
       const selectedTranslation = this.translatedLanguages.find(
         translation => translation.languageShort === language
       );
 
       heading = selectedTranslation?.heading
       content = selectedTranslation?.content
     }
 
      content = this.cleanContent(content);
  
      if (!heading || !content) {
        this.messageService.showMessage(['HeadingAndContentCannotBeEmpty'], 'error')
        return; // Exit function if both are empty
      }
      
      const dialogRef = this.dialog.open(TranslationModalComponent, {
        width: '693px',
        height: '95vh',
        data: {
          heading: heading,
          content: content,
          selectedLanguage: language
        }
      });
  
      dialogRef.afterClosed().subscribe(result => {
        if (result) {
          // Check if the translation already exists for the selected language
          const existingTranslationIndex = this.translatedLanguages.findIndex(
            translation => translation.languageShort === result.languageShort
          );
  
          if (existingTranslationIndex !== -1) {
            // If it exists, update the existing translation
            this.translatedLanguages[existingTranslationIndex] = {
              languageShort: result.languageShort,
              languageFullName: result.languageFullName,
              heading: result.heading,
              content: result.content
            };
          } else {
            // If it doesn't exist, add a new translation
            this.translatedLanguages.push({
              languageShort: result.languageShort,
              languageFullName: result.languageFullName,
              heading: result.heading,
              content: result.content
            });
          }
        }
      });
    }
  
    removeTranslation(language: string) {
      const dialogRef = this.dialog.open(TranslationDeleteModalComponent, {
        width: '530px'
      });
  
      dialogRef.afterClosed().subscribe(result => {
        if (result) {
          // Proceed with deletion if the user confirmed
          this.translatedLanguages = this.translatedLanguages.filter(
            translation => translation.languageShort !== language
          );
        }
      });
    }
  
    cleanContent(content: string): string {
      if (!content) return ''; // Ensure it's not undefined/null
    
      // Remove empty paragraphs, line breaks, and spaces
      content = content.replace(/<(p|br|div|span)>\s*<\/\1>/g, '');
    
      return content.trim(); // Trim remaining spaces
    }
 
    toggleImages() {
     this.showImages = !this.showImages;
    }

  ngOnDestroy(): void {
    if (this.repairDataSubscription) {
      this.repairDataSubscription.unsubscribe();
    }
  }
}
