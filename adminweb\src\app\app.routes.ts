import { Routes } from '@angular/router';
import { NewsComponent } from './news/news.component';
import { authenticatedUserRouteGuard } from './core/guards/authenticated-user-route.guard';



export const routes: Routes = [
    { 
     path: 'news',
     canActivate: [authenticatedUserRouteGuard],
     component: NewsComponent, },
    {
        path: 'login',
        loadComponent: () => import('./login/login.component').then(c => c.LoginComponent),
    },
    {
        path: 'events/culture-events',
        canActivate: [authenticatedUserRouteGuard],
        loadComponent: () => import('./events/culture-events/culture-events.component').then(c => c.CultureEventsComponent)
    },
    {
        path: 'events/sport-events',
        canActivate: [authenticatedUserRouteGuard],
        loadComponent: () => import('./events/sport-events/sport-events.component').then(c => c.SportEventsComponent)
    },
    {
        path: 'parking',
        canActivate: [authenticatedUserRouteGuard],
        loadComponent: () => import('./parking/parking.component').then(c => c.ParkingComponent)
    },
    {
        path: 'report',
        canActivate: [authenticatedUserRouteGuard],
        loadComponent: () => import('./reports/reports.component').then(c => c.ReportsComponent)
    },
    {
        path: 'repair',
        canActivate: [authenticatedUserRouteGuard],
        loadComponent: () => import('./repair/repair.component').then(c => c.RepairComponent)
    },
    {
        path: 'tourism/landmarks',
        canActivate: [authenticatedUserRouteGuard],
        loadComponent: () => import('./tourism/landmarks/landmarks.component').then(c => c.LandmarksComponent)
    },
    {
        path: 'cameras',
        canActivate: [authenticatedUserRouteGuard],
        loadComponent: () => import('./cameras/cameras.component').then(c => c.CamerasComponent)
    },
    {
        path: 'polls',
        canActivate: [authenticatedUserRouteGuard],
        loadComponent: () => import('./polls/polls.component').then(c => c.PollsComponent)
    },
    {
        path: 'news/news-add',
        canActivate: [authenticatedUserRouteGuard],
        loadComponent: () => import('./news/add-news/add-news.component').then(c => c.AddNewsComponent)
    },
    {
        path: 'news/news-edit/:id',
        canActivate: [authenticatedUserRouteGuard],
        loadComponent: () => import('./news/edit-news/edit-news.component').then(c => c.EditNewsComponent)
    },
    {
        path: 'weddings',
        canActivate: [authenticatedUserRouteGuard],
        loadComponent: () => import('./weddings/weddings.component').then(c => c.WeddingsComponent)
    },
    {
        path: 'business/venues/restaurants',
        canActivate: [authenticatedUserRouteGuard],
        loadComponent: () => import('./business/venues/restaurants/restaurants.component').then(c => c.RestaurantsComponent)
    },
    {
        path: 'business/venues/cafes',
        canActivate: [authenticatedUserRouteGuard],
        loadComponent: () => import('./business/venues/cafes/cafes.component').then(c => c.CafesComponent)
    },
    {
        path: 'business/venues/bars',
        canActivate: [authenticatedUserRouteGuard],
        loadComponent: () => import('./business/venues/bars/bars.component').then(c => c.BarsComponent)
    },
    {
        path: 'business/venues/pastryShop',
        canActivate: [authenticatedUserRouteGuard],
        loadComponent: () => import('./business/venues/pastry-shop/pastry-shop.component').then(c => c.PastryShopComponent)
    },
    {
        path: 'events/sport-events/sport-events-add',
        canActivate: [authenticatedUserRouteGuard],
        loadComponent: () => import('./events/sport-events/add-sport-event/add-sport-event.component').then(c => c.AddSportEventComponent)
    },
    {
        path: 'events/sport-events/sport-events-edit/:id',
        canActivate: [authenticatedUserRouteGuard],
        loadComponent: () => import('./events/sport-events/edit-sport-event/edit-sport-event.component').then(c => c.EditSportEventComponent)
    },
    {
        path: 'events/culture-events/culture-events-add',
        canActivate: [authenticatedUserRouteGuard],
        loadComponent: () => import('./events/culture-events/add-culture-event/add-culture-event.component').then(c => c.AddCultureEventComponent)
    },
    {
        path: 'events/culture-events/culture-events-edit/:id',
        canActivate: [authenticatedUserRouteGuard],
        loadComponent: () => import('./events/culture-events/edit-culture-event/edit-culture-event.component').then(c => c.EditCultureEventComponent)
    },
    {
        path: 'events/celebrations/celebrations-add',
        canActivate: [authenticatedUserRouteGuard],
        loadComponent: () => import('./events/celebrations/add-celebrations/add-celebrations.component').then(c => c.AddCelebrationsComponent)
    },
    {
        path: 'events/celebrations/celebrations-edit/:id',
        canActivate: [authenticatedUserRouteGuard],
        loadComponent: () => import('./events/celebrations/edit-celebrations/edit-celebrations.component').then(c => c.EditCelebrationsComponent)
    },
    {
        path: 'parking/parking-add',
        canActivate: [authenticatedUserRouteGuard],
        loadComponent: () => import('./parking/add-parking/add-parking.component').then(c => c.AddParkingComponent)
    },
    {
        path: 'parking/parking-edit/:id',
        canActivate: [authenticatedUserRouteGuard],
        loadComponent: () => import('./parking/edit-parking/edit-parking.component').then(c => c.EditParkingComponent)
    },
    {
        path: 'business/gas-stations',
        canActivate: [authenticatedUserRouteGuard],
        loadComponent: () => import('./business/gas-stations/gas-stations.component').then(c => c.GasStationsComponent)
    },
    {
        path: 'business/shops',
        canActivate: [authenticatedUserRouteGuard],
        loadComponent: () => import('./business/shops/shops.component').then(c => c.ShopsComponent)
    },
    {
        path: 'business/accommodation/hotels',
        canActivate: [authenticatedUserRouteGuard],
        loadComponent: () => import('./business/accommodations/hotels/hotels.component').then(c => c.HotelsComponent)
    },
    {
        path: 'business/accommodation/guest-houses',
        canActivate: [authenticatedUserRouteGuard],
        loadComponent: () => import('./business/accommodations/guest-houses/guest-houses.component').then(c => c.GuestHousesComponent)
    },
    {
        path: 'business/finance/banks',
        canActivate: [authenticatedUserRouteGuard],
        loadComponent: () => import('./business/finance/banks/banks.component').then(c => c.BanksComponent)
    },
    {
        path: 'business/finance/currency-change',
        canActivate: [authenticatedUserRouteGuard],
        loadComponent: () => import('./business/finance/currency-change/currency-change.component').then(c => c.CurrencyChangeComponent)
    },
    {
        path: 'business/finance/insurance-companies',
        canActivate: [authenticatedUserRouteGuard],
        loadComponent: () => import('./business/finance/insurance-companies/insurance-companies.component').then(c => c.InsuranceCompaniesComponent)
    },
    {
        path: 'business/finance/atms',
        canActivate: [authenticatedUserRouteGuard],
        loadComponent: () => import('./business/finance/atms/atms.component').then(c => c.AtmsComponent)
    },
    {
        path: 'business/ecology/bio-shops',
        canActivate: [authenticatedUserRouteGuard],
        loadComponent: () => import('./business/ecology/bio-shops/bio-shops.component').then(c => c.BioShopsComponent)
    },
    {
        path: 'business/ecology/farms',
        canActivate: [authenticatedUserRouteGuard],
        loadComponent: () => import('./business/ecology/farms/farms.component').then(c => c.FarmsComponent)
    },
    {
        path: 'business/ecology/recycling-centers',
        canActivate: [authenticatedUserRouteGuard],
        loadComponent: () => import('./business/ecology/recycling-centers/recycling-centers.component').then(c => c.RecyclingCentersComponent)
    },
    {
        path: 'business/ecology/ecology-initiatives',
        canActivate: [authenticatedUserRouteGuard],
        loadComponent: () => import('./business/ecology/ecology-initiatives/ecology-initiatives.component').then(c => c.EcologyInitiativesComponent)
    },
    {
        path: 'business/culture/museums',
        canActivate: [authenticatedUserRouteGuard],
        loadComponent: () => import('./business/culture/museums/museums.component').then(c => c.MuseumsComponent)
    },
    {
        path: 'business/culture/theaters',
        canActivate: [authenticatedUserRouteGuard],
        loadComponent: () => import('./business/culture/theaters/theaters.component').then(c => c.TheatersComponent)
    },
    {
        path: 'business/culture/galleries',
        canActivate: [authenticatedUserRouteGuard],
        loadComponent: () => import('./business/culture/galleries/galleries.component').then(c => c.GalleriesComponent)
    },
    {
        path: 'tourism/legends-and-myths',
        canActivate: [authenticatedUserRouteGuard],
        loadComponent: () => import('./tourism/legends-and-myths/legends-and-myths.component').then(c => c.LegendsAndMythsComponent)
    },
    {
        path: 'tourism/culture-and-artistic-places',
        canActivate: [authenticatedUserRouteGuard],
        loadComponent: () => import('./tourism/culture-and-artistic-places/culture-and-artistic-places.component').then(c => c.CultureAndArtisticPlacesComponent)
    },
    {
        path: 'tourism/routes-and-activities',
        canActivate: [authenticatedUserRouteGuard],
        loadComponent: () => import('./tourism/routes-and-activities/routes-and-activities.component').then(c => c.RoutesAndActivitiesComponent)
    },
    {
        path: 'tourism/family-fun',
        canActivate: [authenticatedUserRouteGuard],
        loadComponent: () => import('./tourism/family-fun/family-fun.component').then(c => c.FamilyFunComponent)
    },
    {
        path: 'tourism/night-life',
        canActivate: [authenticatedUserRouteGuard],
        loadComponent: () => import('./tourism/night-life/night-life.component').then(c => c.NightLifeComponent)
    },
    {
        path: 'tourism/transport',
        canActivate: [authenticatedUserRouteGuard],
        loadComponent: () => import('./tourism/transport/transport.component').then(c => c.TransportComponent)
    },
    {
        path: 'tourism/travel-agencies',
        canActivate: [authenticatedUserRouteGuard],
        loadComponent: () => import('./tourism/travel-agencies/travel-agencies.component').then(c => c.TravelAgenciesComponent)
    },
    {
        path: 'work-and-training/work',
        canActivate: [authenticatedUserRouteGuard],
        loadComponent: () => import('./work-and-training/work/work.component').then(c => c.WorkComponent)
    },
    {
        path: 'work-and-training/internships-and-programs',
        canActivate: [authenticatedUserRouteGuard],
        loadComponent: () => import('./work-and-training/internships-and-programs/internships-and-programs.component').then(c => c.InternshipsAndProgramsComponent)
    },
    {
        path: 'education/kindergardens',
        canActivate: [authenticatedUserRouteGuard],
        loadComponent: () => import('./education/kindergardens/kindergardens.component').then(c => c.KindergardensComponent)
    },
    {
        path: 'education/nursery',
        canActivate: [authenticatedUserRouteGuard],
        loadComponent: () => import('./education/nursery/nursery.component').then(c => c.NurseryComponent)
    },
    {
        path: 'education/child-nutrition-center',
        canActivate: [authenticatedUserRouteGuard],
        loadComponent: () => import('./education/child-nutrition-center/child-nutrition-center.component').then(c => c.ChildNutritionCenterComponent)
    },
    {
        path: 'education/schools',
        canActivate: [authenticatedUserRouteGuard],
        loadComponent: () => import('./education/schools/schools.component').then(c => c.SchoolsComponent)
    },
    {
        path: 'education/universities',
        canActivate: [authenticatedUserRouteGuard],
        loadComponent: () => import('./education/universities/universities.component').then(c => c.UniversitiesComponent)
    },
    {
        path: 'education/developments-centers',
        canActivate: [authenticatedUserRouteGuard],
        loadComponent: () => import('./education/developments-centers/developments-centers.component').then(c => c.DevelopmentsCentersComponent)
    },
    {
        path: 'health/pharmacies',
        canActivate: [authenticatedUserRouteGuard],
        loadComponent: () => import('./health/pharmacies/pharmacies.component').then(c => c.PharmaciesComponent)
    },
    {
        path: 'health/medical-establishments',
        canActivate: [authenticatedUserRouteGuard],
        loadComponent: () => import('./health/medical-establishments/medical-establishments.component').then(c => c.MedicalEstablishmentsComponent)
    },
    {
        path: 'health/doctors-offices',
        canActivate: [authenticatedUserRouteGuard],
        loadComponent: () => import('./health/doctors-offices/doctors-offices.component').then(c => c.DoctorsOfficesComponent)
    },
    {
        path: 'health/medical-labs',
        canActivate: [authenticatedUserRouteGuard],
        loadComponent: () => import('./health/medical-labs/medical-labs.component').then(c => c.MedicalLabsComponent)
    },
    {
        path: 'health/veterinaries',
        canActivate: [authenticatedUserRouteGuard],
        loadComponent: () => import('./health/veterinaries/veterinaries.component').then(c => c.VeterinariesComponent)
    },
    {
        path: 'events/celebrations',
        canActivate: [authenticatedUserRouteGuard],
        loadComponent: () => import('./events/celebrations/celebrations.component').then(c => c.CelebrationsComponent)
    },
    {
        path: 'sport/sport-clubs',
        canActivate: [authenticatedUserRouteGuard],
        loadComponent: () => import('./sport/sport-clubs/sport-clubs.component').then(c => c.SportClubsComponent)
    },
    {
        path: 'sport/sport-facilities',
        canActivate: [authenticatedUserRouteGuard],
        loadComponent: () => import('./sport/sport-facilities/sport-facilities.component').then(c => c.SportFacilitiesComponent)
    },
    {


        path: 'repair/repair-add',
        canActivate: [authenticatedUserRouteGuard],
        loadComponent: () => import('./repair/add-repair/add-repair.component').then(c => c.AddRepairComponent)
    },
    {
        path: 'repair/repair-edit/:id',
        canActivate: [authenticatedUserRouteGuard],
        loadComponent: () => import('./repair/edit-repair/edit-repair.component').then(c => c.EditRepairComponent)
    },
    {
        path: 'users',
        canActivate: [authenticatedUserRouteGuard],
        data: { roles: ['AdminPlatform', 'Admin'] },
        loadComponent: () => import('./users/users.component').then(c => c.UsersComponent)
    },
    {
        path: 'add-item',
        canActivate: [authenticatedUserRouteGuard],
        loadComponent: () => import('./shared/reusable-add-component/reusable-add-component.component')
          .then(c => c.ReusableAddComponentComponent)
    },
    {
        path: 'edit-item/:id',
        canActivate: [authenticatedUserRouteGuard],
        loadComponent: () => import('./shared/reusable-edit-component/reusable-edit-component.component')
          .then(c => c.ReusableEditComponentComponent)
    },
    {
        path: 'platform-variables',
        canActivate: [authenticatedUserRouteGuard],
        data: { roles: ['AdminPlatform', 'Admin'] },
        loadComponent: () => import('./platform-variables/platform-variables.component').then(c => c.PlatformVariablesComponent)
    },
    {
        path: 'announcements',
        canActivate: [authenticatedUserRouteGuard],
        loadComponent: () => import('./announcements/announcements.component').then(c => c.AnnouncementsComponent)
    },


    { path: '**', pathMatch: 'full', redirectTo: 'news' }, // catch any unfound routes and redirect to home page
];
