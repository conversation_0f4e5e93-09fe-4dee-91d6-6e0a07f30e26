import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';

@Component({
  selector: 'app-delete-news',
  standalone: true,
  imports: [
        TranslateModule,
        MatIconModule,
        MatDialogModule
  ],
  templateUrl: './delete-news.component.html',
  styleUrl: './delete-news.component.css'
})
export class DeleteNewsComponent {
  constructor(
    public dialogRef: MatDialogRef<DeleteNewsComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) { }

  onNoClick(): void {
    this.dialogRef.close();
  }

  onYesClick(): void {
    this.dialogRef.close(true); // Потвърждение за изтриване
  }
}
