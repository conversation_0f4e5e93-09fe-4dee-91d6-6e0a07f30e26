.wrapper {
    margin: 10px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    height: 100vh;
    max-height: 90vh;
    overflow: hidden;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    background-color: #FFFFFF;
  }
  
  .form-container {
    flex: 1;
    margin-right: 20px; /* Adjust as needed for spacing */
  }
  
  .map-container {
    display: flex;
    align-items: flex-start; /* Align the map to the top */
    flex-direction: column;
  }

  .id-field {
    margin-left: 10px;
    margin-bottom: -20px;
  }

  .description {
    height: 110px;
    width: 620px;
    border-radius: 2px;
    border: 1px;
  }
  
  .border-points-container {
    max-height: 400px; /* Adjust this to fit your needs */
    overflow-y: auto; /* Makes the container scrollable */
    scroll-behavior: smooth; /* Smooth scroll */
  }
  
  .highlighted-marker {
    background-color: #e0f7fa; /* Light cyan background */
    border-left: 4px solid var(--primary-color); /* Darker teal left border */
    transition: background-color 0.3s ease; /* Smooth transition */
  }
  
  .scrollable-container{
    max-height: 300px;
    overflow-y: auto;
    padding: 10px;
    border-radius: 4px;
    width: 100%;
    margin-bottom: 10px;
  }
  
  .data-button-div{
    display: flex;
    margin-bottom: -3%;
  }
  
  .data-div{
  display: flex;
  margin-right: -7%;
  }
  
  .delete-button{
    border: 0;
    background-color: transparent; 
    color: red;
    margin-left: 27%;
  }
  
  .separator {
    border-bottom: 1px solid #ccc; /* Adds a bottom border for separation */
    padding: 16px 0; /* Adds vertical padding */
    margin-bottom: 16px; /* Adds space between the rows */
  }
  
  .close-icon{
    cursor: pointer;
    font-size: 35px;
    border: none;
    background-color: transparent;
  }

  .crud-buttons{
    display: flex;
    margin-left: 25%;
  }

  /* Translation and Image Upload Styles - Added from first CSS */
  .translated-languages {
    margin-left: 10px;
    max-width: 100%;
    margin-top: 20px;
  }
  
  .language-buttons {
    max-height: 200px;
    overflow-y: auto;
  }
  
  .language-button {
    margin-left: 3px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-basis: 115px; /* Control width per button */
    max-width: 100%;  /* Prevent buttons from growing larger than the container */
    margin-top: 10px;
    box-sizing: border-box; /* Ensures padding doesn't mess with the width */
  }

  .language-button button {
    margin-left: 10px; /* Add some space between the language name and the button */
  }
  
  .language-button mat-icon {
    font-size: 21px; /* Optional: Adjust icon size if needed */
  }

  .uploaded-images {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    max-height: 500px;
    overflow-y: auto;
  }
  
  .image-wrapper {
    position: relative;
    display: inline-block;
    margin-top: 9px;
    margin-left: 10px;
    margin-bottom: 10px;
  }
  
  .image-box {
    position: relative;
    display: flex;
    align-items: center;
  }
  
  .uploaded-image {
    width: 300px;
    height: 200px;
    object-fit: cover;
    border-radius: 5px;
  }
  
  .add-button-container {
    margin-left: 10px;
    margin-top: 25px;
    display: flex;
    justify-content: flex-start;
  }
  
  .add-button {
    width: 225px;
    height: 40px;
    border-radius: 5px;
    box-shadow: 0px 1px 3px 0px #0000004D;
    box-shadow: 0px 4px 8px 3px #00000026;
    background: #FFFFFF;
    margin-right: 10px;
  }

  .translate-button {
    width: 225px;
    height: 40px;
    border-radius: 5px;
    box-shadow: 0px 1px 3px 0px #0000004D;
    box-shadow: 0px 4px 8px 3px #00000026;
    background: #383838;
    color: white;
    margin-right: 10px;
  }
  
  .image-container {
    position: relative;
    display: inline-block;
    width: 80%;
    height: 80%;
    margin-top: 10px;
    max-width: 600px;
  }

  /* Add styling for the selected thumbnail */
  .selected-thumbnail {
    position: relative;
  }
  
  /* Add the "Cover" text indicator with more vibrant color */
  .selected-thumbnail::after {
    content: attr(data-label);
    position: absolute;
    bottom: 15px;
    left: 15px;
    background-color: var(--primary-color); /* Bright blue background */
    color: white;
    padding: 3px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
    z-index: 2;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3); /* Subtle shadow for better visibility */
  }
  
  /* Keep your existing shadow styling for the selected thumbnail */
  .selected-thumbnail .uploaded-image {
    box-shadow: 0 0 8px 3px rgba(21, 23, 27, 0.6);
    transition: all 0.2s ease-in-out;
    border-radius: 8px;
  }
  
  .remove-icon {
    position: absolute;
    top: -10px;
    background-color: rgba(255, 255, 255, 0.7); /* Slight white background for better visibility */
    border-radius: 50%;
    margin-left: 105%;
  }

  .example-card {
    display: flex;
    margin-top: 30px;
    margin-bottom: 30px;
  }
  
  .card-content-title {
    margin-bottom: 10px;
  }
  /* End of Translation and Image Upload Styles */
  
  .save-button{
    width: 135px;
    height: 40px;
    gap: 0px;
    border-radius: 5px;
    opacity: 0px;
    background-color: #383838 !important;
    color: white !important;
    box-shadow: 0px 1px 3px 0px #0000004D;
    box-shadow: 0px 4px 8px 3px #00000026;
    margin-left: 4%;
  }

  .cancel-button{
    width: 100px;
    height: 40px;
    gap: 0px;
    border-radius: 5px;
    opacity: 0px;
    background-color: #FFFFFF !important;
    box-shadow: 0px 1px 3px 0px #0000004D;
    box-shadow: 0px 4px 8px 3px #00000026;
  }
  
  .details-report-content-row{
    display: flex;
    width: 100%; /* Ensure it takes full width */
    justify-content: space-between; /* Space between elements */
  }
  
  .information{
    width: 100%;
    height: 25px;
    font-size: 15px;
    font-weight: 700;
    line-height: 24px;
    color: var(--Dark-color---Smart-Lighting, #222222);
  }
  
  .icon{
    vertical-align: -5px;
    width: 16.5px;
    padding-right: 30px;
    color: #FBBC05;
  }
  
  .p{
    width: 12rem;
    height: 24px;
    font-size: 16px;
    font-weight: 400;
    line-height: 24px;
    color: #000000;
    margin-left: 29px;
  }
  
  /* Optional: You can adjust the colors and sizes as needed */
  .details-report-content-row {
    /* Optional styles to enhance visual appeal */
    background-color: #f9f9f9; /* Light background for better visibility */
    border-radius: 4px; /* Rounded corners */
    transition: background-color 0.3s; /* Smooth background transition on hover */
  }
  
  .details-report-content-row:hover {
    background-color: #eaeaea; /* Darker background on hover */
  }
  
  .map {
    width: 100%;
    height: 100%;
    box-shadow: 0px 4px 4px 0px #00000040;
  }
  
  .search{
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  }
  
  google-map div.map-container{
  height: 722px;
  }
  
  .search-input{
  height: 40px;
  width: 305px;
  border-radius: 2px;
  border: 1px ; /* Added border for clarity */
  }
  
  .search-button {
    background-color: #383838;
    color: white;
    width: 80px;
    height: 44px; /* Match input height */
    margin-top: 3px;
    margin-left: 10px;
    border: none; /* Remove border */
    border-radius: 4px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    cursor: pointer;
  }
  
  .header {
    display: flex;
    align-items: center;
    justify-content: space-between; /* Align items to the start */
    height: 63px;
    width: 100%; /* Use full width */
  }
  
  .header button {
    width: 100px;
    height: 40px;
    border-radius: 5px;
    margin-left: 10px;
    margin-right: 10px;
    box-shadow: 0px 1px 3px 0px #0000004D;
    box-shadow: 0px 4px 8px 3px #00000026;
  
  }
  
  .h1 {
    size: 20px;
    color: var(--primary-color);
    font-weight: bold;
  }
  
  .filter-container {
    align-items: center;
  }
  
  .wide-dropdown {
    width: 220px;
    margin-right: 10px;
  }
  
  .date {
    width: 300px;
    border-radius: 2px;
    border: 1px;
    margin-top: 25px;
    margin-left: 10px;
  }
  
  .date,
  .long-fields,
  .priority {
  width: calc(33% - 10px); /* Three fields per row with spacing between */
  }
  
  .date {
  width: 300px;
  border-radius: 2px;
  border: 1px;
  margin-right: 10px;
  }
  
  .mat-form-fields {
  width: 300px;
  border: 1px;
  margin-right: 10px;
  }
  
  .priority {
    width: 167px;
    border-radius: 2px;
    border: 1px;
    margin-right: 10px;
  }
  
  .left-big-border {
    height: 33px;
  }
  
  .buttons{
    display: flex;
    flex-direction: row;
    justify-content: space-between;
  }
  
  .save-button{
    width: 135px;
    height: 40px;
    gap: 0px;
    border-radius: 5px;
    opacity: 0px;
    background-color: #383838 !important;
    color: white !important;
    box-shadow: 0px 1px 3px 0px #0000004D;
    box-shadow: 0px 4px 8px 3px #00000026;
    margin-right: 10px;
  }
  
  .map-fields{
    display: flex;
    flex-direction: row;
    justify-content: space-between;
  }
  
  .map-form-fields{
  width: 15rem;
  border: 1px;
  margin-right: 10px;
  }
  
  #map {
    height: 79vh;
    width: 100%;
    box-sizing: border-box;
  }
  
  .mat-mdc-text-field-wrapper {
    height: 0px;
    flex: auto;
    will-change: auto;
  }
  
  .main-content {
    display: flex;
    padding-top: 23px;
    padding-left: 40px;
    width: 100%; /* Use full width */
    justify-content: space-between;
  }
  
  .map-container {
    display: flex;
    width: 100%;
  }
  
  .map {
    display: flex;
    width: 100%;
  }

  /* Additional styles from first CSS for proper form field display */
  .group-label {
    font-weight: bold;       /* Make the label bold */
    color: black;         /* Set a primary color */
    margin-top: 10px;       /* Add some space above */
    margin-bottom: 5px;     /* Add some space below */
    font-size: 1.1em;       /* Increase font size for visibility */
  }

  .heading{
    display: flex;
    align-items: center;
    justify-content: flex-start; /* Align items to the start */
    height: 63px;
    width: 711px; /* Use full width */
    padding: 20px 15px 20px 10px;
  }

  .mat-form-field {
    width: 100%; /* Make the fields take full width */
    margin-right: 0; /* Remove horizontal margin */
  }
  
  /* Adjust text field wrapper height */
  :host ::ng-deep .mat-mdc-text-field-wrapper {
    height: 44px;
  }
  
  :host ::ng-deep .mdc-text-field--outlined .mat-mdc-form-field-infix,
  :host ::ng-deep .mdc-text-field--no-label .mat-mdc-form-field-infix {
    padding-top: 12px;
  }
  
  /* Responsive adjustments */
  @media screen and (max-width: 768px) {
    .wrapper {
      overflow-y: auto;
      margin: 10px 5px; /* Adjusted margin for better spacing */
    }
  
    .header {
      width: 100%;
      padding: 10px;
    }
  
    .h1 {
      margin-left: 0px;
      font-size: 18px; /* Slightly smaller font size for smaller screens */
      margin-top: 10px; /* Space between button and heading */
    }
  
    .main-content {
      flex-direction: column;
      padding: 10px; /* Added padding for better spacing */
      width: 100%;
    }
  
    .filter-container {
      flex-direction: column;
      align-items: stretch;
      width: 200px;
      margin-bottom: 10px; /* Added margin to separate sections */
    }
  
    .wide-dropdown,
  .mat-form-field,
  .date,
  .long-fields,
  .description,
  .search,
  .priority {
  width: 100%;
  margin-right: 0;
  margin-bottom: 10px; /* Added margin to separate elements */
  }
  
  }

  @media screen and (min-width: 1538px) {
    .border-points-container {
        max-height: 480px; /* Adjust this to fit your needs */
        overflow-y: auto; /* Makes the container scrollable */
        scroll-behavior: smooth; /* Smooth scroll */
      }
  }