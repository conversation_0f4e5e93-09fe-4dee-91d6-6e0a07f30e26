.contentPanel{
    padding: 2px;
    width: 100%;
    border-top: 8px solid var(--primary-color);
    justify-content: space-between;
    border-bottom: 1px solid #ccc;
  }
  
  .panelHead{
    margin: 20px;
    display: flex;
    align-items: center;
  }
  
  .h1{
    text-align: center;
    color: var(--color-main);
    width: 100%;
    border-bottom: 1px solid var(--color-gray-border) !important;
    margin: 0;
    font-family: 'Roboto', sans-serif;
    font-size: 20px;
    font-weight: 700;
    line-height: 16px;
  }
  
  .close-icon{
      cursor: pointer;
      font-size: 35px;
      border: none;
      background-color: transparent;
  }
  
  .button-yes{
    background-color: #383838;
    color: white;
    width: 100px;
    height: 44px; /* Match input height */
    margin-top: 3px;
    margin-left: 10px;
    border: none; /* Remove border */
    border-radius: 4px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    cursor: pointer;
  }
  
  .button-no {
    background-color: white;
    color: #383838;
    width: 100px;
    height: 44px; /* Match input height */
    margin-top: 3px;
    margin-left: 10px;
    border: none; /* Remove border */
    border-radius: 4px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    cursor: pointer;
  }
  
  .publish-content{
    text-align: center;
    margin: 50px 0;
  }
  
  .mat-mdc-dialog-actions.mat-mdc-dialog-actions-align-center{
    padding-bottom: 20px;
  }