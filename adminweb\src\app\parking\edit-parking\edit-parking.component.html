<div class="wrapper">
  <div class="header">
    <button mat-raised-button (click)="onBack()">
      <mat-icon>arrow_back</mat-icon>
      <span>{{ 'Back' | translate }}</span>
    </button>
    <h1 class="h1">{{"ParkingZoneCorrecting" | translate}}</h1>
    <div style="width: 100px;"></div>
  </div>
  <mat-divider></mat-divider>
  <div style="padding: 20px 40px;">
  <mat-form-field appearance="outline" style="width: 100%; max-width: 400px; margin-bottom: -15px;">
  <mat-label>{{"name" | translate}}</mat-label>
  <input matInput [(ngModel)]="data.name" [placeholder]="'enterName' | translate" maxlength="50" #input>
  <mat-hint align="end">{{input.value.length || 0}}/50</mat-hint>
</mat-form-field>
</div>
<mat-divider></mat-divider>
  <div class="main-content">
    <div #borderPointsContainer class="border-points-container scrollable-container">
      <div *ngFor="let borderPoint of clickedCoordinates; let i = index" 
      #borderPointRow 
      class="details-report-content-row separator"
      [ngClass]="{'highlighted-marker': highlightedIndex === i}">
        <div fxLayout="row" fxLayoutAlign="space-between center" class="data-button-div">
          <div fxLayout="column" class="data-div">

            <div class="latitude-div">
            <strong class="information"><mat-icon class="icon">place</mat-icon>{{ 'lat' | translate }}</strong>
            <p class="p">{{borderPoint.latitude}}</p>
            </div>
             
            <div class="longitude-div">
            <strong class="information"><mat-icon class="icon">place</mat-icon>{{ 'lng' | translate }}</strong>
            <p class="p">{{borderPoint.longitude}}</p>
            </div>

          </div>
    
          <!-- Trash bin to delete the border point -->
          <button mat-icon-button class="delete-button" color="warn"  (click)="deleteBorderPoint(i)">
            <mat-icon>delete</mat-icon>
          </button>
        </div>
      </div>
    </div>

    <!-- map -->
    <div class="map-container">
      <div #mapContainer id="map"></div>    
      <div #markerContainer style="display: none;"></div>
    </div>


  </div>
    <!-- Add button to create border points -->
    <button mat-raised-button tabindex="1" class="save-button" (click)="addBorderPoints()">
      <span>{{ 'Save' | translate }}</span>
  </button>
</div>
