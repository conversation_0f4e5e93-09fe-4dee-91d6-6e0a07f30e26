<!-- Modal Header -->
<div class="contentPanel">
    <div class="panelHead">
      <h2 class="h1">{{ "Translate" | translate }}</h2>
      <button class="close-icon" mat-icon-button [mat-dialog-close]="false">
        <mat-icon>close</mat-icon>
      </button>
    </div>
  </div>
  
  <!-- Modal Content -->
  <div class="details-report-content-row">
    <!-- Language Selection -->
    <mat-form-field appearance="outline" class="full-width language-select">
      <mat-label>{{ "SelectLanguage" | translate }}</mat-label>
      <mat-select [(value)]="selectedLanguage">
        <mat-option value="bg">{{ "Bulgarian" | translate }}</mat-option>
        <mat-option value="es">{{"Spanish" | translate }}</mat-option>
        <mat-option value="en">{{"English" | translate }}</mat-option>
        <mat-option value="de">{{"German" | translate }}</mat-option>
        <mat-option value="it">{{"Italian" | translate }}</mat-option>
        <mat-option value="tr">{{"Turkish" | translate }}</mat-option>
        <mat-option value="el">{{"Greek" | translate }}</mat-option>
        <mat-option value="sr">{{"Serbian" | translate }}</mat-option>
        <mat-option value="ro">{{"Romanian" | translate }}</mat-option>
        <mat-option value="fr">{{"French" | translate }}</mat-option>
        <mat-option value="uk">{{"Ukrainian" | translate }}</mat-option>
      </mat-select>
    </mat-form-field>
  
    <!-- Translate Button -->
    <button mat-raised-button class="translate-button" (click)="translateText()" [disabled]="!selectedLanguage">{{ "Translate" | translate }}</button>
    
    <div class="data">
   <!-- Translated Heading -->
<div class="heading">
  <mat-form-field appearance="outline" class="translated-heading-field">
    <mat-label>{{ "TranslatedHeading" | translate }}</mat-label>
    <!-- Add matInput directive and [(ngModel)] -->
    <input matInput
           [(ngModel)]="translatedHeading"
           [attr.placeholder]="'TranslatedHeading' | translate" 
           (input)="onHeadingChange($event)" />
  </mat-form-field>
</div>

  
    <!-- Translated Content -->
    <div class="example-card">
      <mat-card>
      <mat-card-header>
        <mat-card-title class="card-content-title">{{ 'Content' | translate }}</mat-card-title>
      </mat-card-header>
      <mat-card-content class="card-content">
        <app-editor (focusout)="onChangeContent()" [(content)]="content" [disabled]="false"></app-editor>
      </mat-card-content>
      </mat-card>
      </div>

  </div>
  
  <!-- Modal Footer with Actions -->
  <div class="buttons">
    <button type="submit" mat-raised-button tabindex="1" class="save" (click)="onSave()" [disabled]="!selectedLanguage">
      <mat-icon>save</mat-icon>
      <span>{{ 'Save' | translate }}</span>
    </button>
  </div>

  


