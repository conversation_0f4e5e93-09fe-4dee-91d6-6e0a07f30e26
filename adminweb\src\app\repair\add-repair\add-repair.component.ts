import { CommonModule } from '@angular/common';
import { ChangeDetector<PERSON><PERSON>, Component, ElementRef, OnDestroy, OnInit, QueryList, ViewChild, ViewChildren, ViewContainerRef } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { MatDividerModule } from '@angular/material/divider';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { GoogleMapsModule } from '@angular/google-maps';
import { ActivatedRoute, Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { GoogleMapsService } from '../../core/services/google.maps.service';
import { MapGetModel } from '../../shared/interfaces/map/map-get.model';
import { environment } from '../../../environments/enviroment';
import { ParkingService } from '../../core/services/parking.service';
import { MatFormFieldModule } from '@angular/material/form-field';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatInputModule } from '@angular/material/input';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule, MatOptionModule } from '@angular/material/core';
import { MatSelectModule } from '@angular/material/select';
import { MatCardModule } from '@angular/material/card';
import { EditorComponent } from '../../shared/editor/editor.component';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { Validators } from 'ngx-editor';
import { TranslationDeleteModalComponent } from '../../shared/translation-delete-modal/translation-delete-modal.component';
import { TranslationModalComponent } from '../../shared/translation-modal/translation-modal.component';
import { MessageService } from '../../core/services/message.service';
import { RepairService } from '../../core/services/repairs.service';
import { ParkngSimpleBorderPoint } from '../../shared/interfaces/parking/parking-simple-border-point.model';

@Component({
  selector: 'app-add-repair',
  standalone: true,
  imports: [
  TranslateModule,
  MatIconModule,
  MatDialogModule,
  GoogleMapsModule,
  CommonModule,
  MatDividerModule,
  MatButtonModule,
  MatFormFieldModule,
  ReactiveFormsModule,
  MatInputModule,
  MatDatepickerModule,
  MatNativeDateModule,
  MatOptionModule,
  MatSelectModule,
  MatCardModule,
  EditorComponent,
  FormsModule,
  MatSlideToggleModule
  ],
  templateUrl: './add-repair.component.html',
  styleUrl: './add-repair.component.css'
})
export class AddRepairComponent {
  @ViewChild('mapContainer') mapContainer!: ElementRef;
  @ViewChild('markerContainer', { read: ViewContainerRef, static: true }) markerContainer!: ViewContainerRef;
  @ViewChildren('borderPointRow') borderPointRows!: QueryList<ElementRef>;
  @ViewChild('fileInput') fileInput: ElementRef | undefined;

  repairAddFilterForm!: FormGroup;
  protected content = '';
  protected imageSrcs: { 
    fileName: string; 
    contentType: string; 
    extension: string; 
    content: string; 
  }[] = [];
  protected translatedLanguages: { languageShort: string, languageFullName: string, heading: string, content: string }[] = [];
  protected selectedThumbnailIndex: number | null = null;
   protected showImages: boolean = true;

  constructor(
    private googleMapsService: GoogleMapsService,
    private route: ActivatedRoute,
    private router: Router,
    private parkingService: ParkingService,
    private changeDetectorRef: ChangeDetectorRef,
    private fb: FormBuilder,
    public dialog: MatDialog, 
    private messageService: MessageService,
    private repairService: RepairService
  ) {
    this.repairAddFilterForm = this.fb.group({
      fromDate: [null],
      toDate: [null],
      heading: ['', [Validators.maxLength(60)]],
      templateContent: [''],
      thumbnailIndex: [null]
     });
  }

  protected data: { radius: number | string | null, borderPoints: any[] } = { radius: null, borderPoints: [] };
  protected mapHeight: number = 0;
  protected mapWidth: number = 0;
  protected center!: google.maps.LatLngLiteral;
  protected zoom = 13;
  private map: google.maps.Map | undefined;
  private dayMapId = environment.dayMapId;
  //private aggregatedGroupsData: AggregatedGroupModel[] = [];
  private marker: google.maps.marker.AdvancedMarkerElement | undefined;
  private currentMarker: any = null;
  protected clickedCoordinates: { latitude: number, longitude: number, index: number}[] = [];
  private currentMarkers: google.maps.marker.AdvancedMarkerElement[] = [];
  private polyline: google.maps.Polyline | undefined;
  private markerPositions: { latitude: number; longitude: number }[] = [];
  protected parkingId!: number;
  protected highlightedIndex: number | null = null;
  protected parkingRadius!: number;
  protected firstMarkerIcon = {
    url: 'assets/images/end-point-marker.svg',
  };
  protected lastMarkerIcon = {
    url: 'assets/images/start-point-marker.svg',
  };
  private isFirstTime = true;

  private parkingDataSubscription!: Subscription;

 async ngOnInit() {
   this.initializeGoogleMaps();
  }

  get form() {
    return this.repairAddFilterForm.controls;
  }

 private updatePolylinePath() {
     if (this.polyline) {
       const path = this.clickedCoordinates.map(coord => new google.maps.LatLng(coord.latitude, coord.longitude));
       this.polyline.setPath(path);
     }
   }
 
   private scrollToHighlighted() {
     
     if (this.highlightedIndex !== null) {
       const element = this.borderPointRows.toArray()[this.highlightedIndex].nativeElement;
       element.scrollIntoView({ behavior: 'smooth', block: 'nearest', inline: 'start' });
       this.changeDetectorRef.detectChanges();
     }
       
   }
 
  addBorderPoints() {
  // Validate form and content
  const templateContent = this.cleanContent(this.repairAddFilterForm.value.templateContent || this.content);
  
  if (this.repairAddFilterForm.valid && templateContent.length > 0 && this.clickedCoordinates.length > 0) {
    
    // Calculate midpoint for main coordinates
    const midpoint = this.googleMapsService.calculateMidpoint(
      this.clickedCoordinates.map(coord => ({ lat: coord.latitude, lng: coord.longitude }))
    );
    
    // Update form with thumbnail index
    this.repairAddFilterForm.patchValue({
      thumbnailIndex: this.selectedThumbnailIndex,
    });
    
    const files = this.imageSrcs.map((src) => {
        const byteArray = this.convertBase64ToByteArray(src.content);
        const blob = new Blob([byteArray], { type: src.contentType });
        const file = new File([blob], src.fileName, { type: src.contentType });
        return file;
      });

      const coverFile = this.selectedThumbnailIndex !== null && this.imageSrcs[this.selectedThumbnailIndex]
        ? files[this.selectedThumbnailIndex]
        : null;

      const filesWithoutCover = files.filter((file, index) => index !== this.selectedThumbnailIndex);
    
    // Prepare translations
    const translations = this.translatedLanguages.map((translation) => ({
      name: translation.heading,
      description: translation.content,
      languageName: translation.languageFullName,
      languageCode: translation.languageShort,
    }));
    
    // Prepare area points (border points)
    const areaPoints = this.clickedCoordinates.map((coord, index) => ({
      index: index,
      latitude: coord.latitude,
      longitude: coord.longitude,
    }));
    
    // Create FormData
    const formData = new FormData();
    
    // Basic repair information
    formData.append('Name', this.repairAddFilterForm.value.heading || '');
    formData.append('Description', templateContent);
    
    // Handle dates
    if (this.repairAddFilterForm.value.fromDate) {
      const fromDate = this.repairAddFilterForm.value.fromDate;
      const startDate = fromDate instanceof Date ? fromDate : new Date(fromDate);
      formData.append('StartDate', startDate.toISOString());
    }
    
    if (this.repairAddFilterForm.value.toDate) {
      const toDate = this.repairAddFilterForm.value.toDate;
      const endDate = toDate instanceof Date ? toDate : new Date(toDate);
      formData.append('EndDate', endDate.toISOString());
    }
    
    // Add translations
    translations.forEach((translation, index) => {
      formData.append(`Translations[${index}].name`, translation.name);
      formData.append(`Translations[${index}].description`, translation.description);
      formData.append(`Translations[${index}].languageCode`, translation.languageCode);
      formData.append(`Translations[${index}].languageName`, translation.languageName);
      formData.append(`Translations[${index}].repairId`, '0'); // Will be set by backend
    });

     filesWithoutCover.forEach((file, index) => {
        formData.append(`Files[${index}]`, file, file.name);
      });

      if (this.selectedThumbnailIndex !== null && this.imageSrcs[this.selectedThumbnailIndex]) { 
        const coverSrc = this.imageSrcs[this.selectedThumbnailIndex];
      
        if (coverSrc) {
          const byteArray = this.convertBase64ToByteArray(coverSrc.content);
          const blob = new Blob([byteArray], { type: coverSrc.contentType });
          const file = new File([blob], coverSrc.fileName, { type: coverSrc.contentType });
      
          formData.append('Cover', file);
        }
      }
    
    // Add area points (border points)
    areaPoints.forEach((point, index) => {
      formData.append(`AreaPoints[${index}].index`, point.index.toString());
      formData.append(`AreaPoints[${index}].latitude`, point.latitude.toString());
      formData.append(`AreaPoints[${index}].longitude`, point.longitude.toString());
    });
    
    // Submit the form
    this.repairService.addRepair(formData).subscribe({
      next: (response) => {
        console.log('Repair added successfully', response);
      },
      error: (error) => {
        console.error('Error adding repair', error);
        this.messageService.showMessage(["ErrorCreatingRepair"], 'error');
      },
      complete: () => {
        console.log('Repair request complete');
        this.messageService.showMessage(["CreateSuccessfully"], 'success');
        this.router.navigate(['/repair']); // Adjust route as needed
      }
    });
    
  } else {
    // Handle validation errors
    let errorMessage = [];
    
    if (!this.repairAddFilterForm.valid) {
      errorMessage.push("PleaseCompleteTheForm");
    }
    
    if (templateContent.length === 0) {
      errorMessage.push("PleaseAddContent");
    }
    
    if (this.clickedCoordinates.length === 0) {
      errorMessage.push("PleaseAddBorderPoints");
    }
    
    this.messageService.showMessage(errorMessage, 'error');
  }
}

convertBase64ToByteArray(base64: string): Uint8Array {
    const binaryString = atob(base64.split(',')[1]);
    const byteArray = new Uint8Array(binaryString.length);
    for (let i = 0; i < binaryString.length; i++) {
      byteArray[i] = binaryString.charCodeAt(i);
    }
    return byteArray;
  }

// Helper method to get file extension if not provided
private getFileExtension(fileName: string): string {
  return fileName.split('.').pop()?.toLowerCase() || '';
}

  deleteBorderPoint(index: number) {
    console.log(this.clickedCoordinates)
    const removedPoint = this.clickedCoordinates.splice(index, 1)[0];
    console.log(this.clickedCoordinates)

    const markerIndex = this.markerPositions.findIndex(position =>
      position.latitude === removedPoint.latitude && position.longitude === removedPoint.longitude
    );

    if (markerIndex > -1) {
      this.currentMarkers[markerIndex].map = null;  
      this.currentMarkers.splice(markerIndex, 1);
      this.markerPositions.splice(markerIndex, 1);
    }

    // Update indexes of remaining points and markers
    this.clickedCoordinates.forEach((coord, i) => {
      coord.index = i; // Realign indexes
    });


    if (this.highlightedIndex === index) {
      this.highlightedIndex = null;
    } else if (this.highlightedIndex !== null && this.highlightedIndex > index) {
      this.highlightedIndex--;
    }

    this.changeDetectorRef.detectChanges();

    this.updatePolylinePath();
  }

  async initializeGoogleMaps() {
    try {
      await this.googleMapsService.loadLibraries();

      this.center = {
        lat: 42.482798, 
        lng: 26.503206 

      };

      this.map = await this.googleMapsService.initializeMap(
        this.mapContainer.nativeElement,
        this.center,
        this.zoom,
        this.dayMapId,
        'day'
      );

      this.polyline = new google.maps.Polyline({
        map: this.map,
        path: [],
        strokeColor: '#800080',
        strokeOpacity: 1.0,
        strokeWeight: 2,
      });

      this.clickedCoordinates.forEach((points: { latitude: number; longitude: number }, index: number) => {
        let pointsGoogle: google.maps.LatLngLiteral = { lat: points.latitude, lng: points.longitude };

        let iconUrl: string | undefined = undefined;
        if (index === 0) {
          iconUrl = this.firstMarkerIcon.url;
        } else if (index === this.clickedCoordinates.length - 1 && index !== 0) {
          iconUrl = this.lastMarkerIcon.url;
        }

        const newMarker = this.googleMapsService.addAdvancedMarker(
          pointsGoogle,
          index,
          undefined,
          undefined,
          iconUrl,
          undefined,
          true
        );

        newMarker.addListener('click', () => {
          this.onMarkerClick(points.latitude, points.longitude);
        });

        this.currentMarkers.unshift(newMarker); // Using push here
        this.markerPositions.unshift({ latitude: points.latitude, longitude: points.longitude });
      });

      this.updatePolylinePath();

      this.map.addListener('click', (event: google.maps.MapMouseEvent) => {
        if (!event.latLng) return; // Ensure the click event has coordinates
      
        const clickedPosition: google.maps.LatLngLiteral = {
          lat: event.latLng.lat(),
          lng: event.latLng.lng(),
        };
      
        if (this.currentMarkers.length > 0) {
          if (this.isFirstTime) {
            // First time: Remove the last marker
            const lastMarker = this.currentMarkers[this.currentMarkers.length - 1];
            this.removeMarker(lastMarker);
            this.isFirstTime = false;
          } else {
            // Subsequent times: Remove the first marker
            const firstMarker = this.currentMarkers[0];
            this.removeMarker(firstMarker);
          }
        }
      
        // Create new marker at clicked position
        this.currentMarker = this.createMarker(clickedPosition, this.firstMarkerIcon.url);
      
        // Store marker data
        this.currentMarkers.unshift(this.currentMarker);
        this.markerPositions.unshift({ latitude: clickedPosition.lat, longitude: clickedPosition.lng });
      
        this.clickedCoordinates.unshift({
          latitude: clickedPosition.lat,
          longitude: clickedPosition.lng,
          index: 0
        });
      
        // Update indexes
        this.clickedCoordinates.forEach((coord, i) => {
          coord.index = i;
        });
  
        this.changeDetectorRef.detectChanges();
      
        // Add click listener to marker
        this.currentMarker.addListener('click', () => {
          this.onMarkerClick(clickedPosition.lat, clickedPosition.lng);
        });
      
        // Update polyline if applicable
        this.updatePolylinePath();
      });
      
    } catch (error) {
      console.error('Error loading Google Maps:', error);
    }
  }

  onMarkerClick(latitude: number, longitude: number) {
    const index = this.clickedCoordinates.findIndex(point =>
        point.latitude === latitude && point.longitude === longitude
    );

    if (index !== -1) {
        this.highlightedIndex = index;
        this.scrollToHighlighted();
    }
  }


  async ngAfterViewInit(): Promise<void> {
    this.googleMapsService.setViewContainerRef(this.markerContainer);
  }

  protected onBack() {
    this.router.navigate(['/repair']);
  } 
  
  
  private createMarker(position: google.maps.LatLngLiteral, iconUrl: string): google.maps.marker.AdvancedMarkerElement {

    const newMarker = this.googleMapsService.addAdvancedMarker(
      position,
      2,
      undefined,
      undefined,
      iconUrl, 
      undefined,
      true
    );
  
    return newMarker;
  }
    

  private removeMarker(marker: google.maps.marker.AdvancedMarkerElement): void {
    const position = marker.position as google.maps.LatLngLiteral; 

    marker.map = null; 

    const markerIndex = this.currentMarkers.indexOf(marker);
    if (markerIndex > -1) {
      this.currentMarkers.splice(markerIndex, 1);  
      this.markerPositions.splice(markerIndex, 1); 
    }

    const newMarker = this.googleMapsService.addAdvancedMarker(
      position,
      2,
    );
  
    this.currentMarkers.splice(markerIndex, 0, newMarker);

    // Insert the position at the same index in markerPositions array
    this.markerPositions.splice(markerIndex, 0, { latitude: position.lat, longitude: position.lng });

    newMarker.addListener('click', () => {
    this.onMarkerClick(position.lat, position.lng);
  });
    
  }

   selectThumbnail(index: number) {
      this.selectedThumbnailIndex = index;
      this.repairAddFilterForm.patchValue({
        thumbnailIndex: this.selectedThumbnailIndex,
      });
    }
  
    onChangeContent() {
      const templateContentControl = this.repairAddFilterForm.get('templateContent');
      if (templateContentControl) {
        templateContentControl.setValue(this.content);
      }
    }
  
    openFileDialog() {
      if (this.fileInput) {
        this.fileInput.nativeElement.click();
      }
    }
  
    onFileSelected(event: Event) {
      const fileInput = event.target as HTMLInputElement;
      if (fileInput.files && fileInput.files.length > 0) {
        Array.from(fileInput.files).forEach((file: File) => {
          const reader = new FileReader();
    
          reader.onload = (e: any) => {
            const fileDetails = {
              fileName: file.name,
              contentType: file.type,
              extension: file.name.split('.').pop() || '',
              content: e.target.result
            };
    
            this.imageSrcs.push(fileDetails);
    
            if (this.imageSrcs.length === 1) {
              this.selectedThumbnailIndex = 0;
            }
          };
    
          reader.readAsDataURL(file);
        });
      }
    }
    
    removePhoto(index: number) {
      this.imageSrcs.splice(index, 1);
  
      if (this.fileInput) {
        this.fileInput.nativeElement.value = '';
      }
      
      if (this.selectedThumbnailIndex === index) {
        this.selectedThumbnailIndex = this.imageSrcs.length > 0 ? 0 : null;
      } else if (this.selectedThumbnailIndex !== null && index < this.selectedThumbnailIndex) {
        this.selectedThumbnailIndex--;
      }
    }
  
    openTranslationModal(language?: string) {
      const heading = this.repairAddFilterForm.value.heading?.trim();
      let content = this.repairAddFilterForm.value.templateContent?.trim();
      
      content = this.cleanContent(content);
      
      if (!heading || !content) {
        this.messageService.showMessage(['HeadingAndContentCannotBeEmpty'], 'error');
        return;
      }
      
      let existingTranslation = undefined;
      if (language) {
        existingTranslation = this.translatedLanguages.find(
          translation => translation.languageShort === language
        );
      }
      
      const dialogRef = this.dialog.open(TranslationModalComponent, {
        width: '693px',
        height: '95vh',
        data: {
          heading: existingTranslation ? existingTranslation.heading : this.repairAddFilterForm.value.heading,
          content: existingTranslation ? existingTranslation.content : this.content,
          selectedLanguage: language
        }
      });
    
      dialogRef.afterClosed().subscribe(result => {
        if (result) {
          const existingTranslationIndex = this.translatedLanguages.findIndex(
            translation => translation.languageShort === result.languageShort
          );
    
          if (existingTranslationIndex !== -1) {
            this.translatedLanguages[existingTranslationIndex] = {
              languageShort: result.languageShort,
              languageFullName: result.languageFullName,
              heading: result.heading,
              content: result.content
            };
          } else {
            this.translatedLanguages.push({
              languageShort: result.languageShort,
              languageFullName: result.languageFullName,
              heading: result.heading,
              content: result.content
            });
          }
        }
      });
    }
    
    removeTranslation(language: string) {
      const dialogRef = this.dialog.open(TranslationDeleteModalComponent, {
        width: '530px'
      });
      
      dialogRef.afterClosed().subscribe(result => {
        if (result) {
          this.translatedLanguages = this.translatedLanguages.filter(
            translation => translation.languageShort !== language
          );
        }
      });
    }
    
    cleanContent(content: string): string {
      if (!content) return '';
      content = content.replace(/<(p|br|div|span)>\s*<\/\1>/g, '');
      return content.trim();
    }

    ngOnDestroy(): void {
      if (this.parkingDataSubscription) {
        this.parkingDataSubscription.unsubscribe();
      }
    }
}
