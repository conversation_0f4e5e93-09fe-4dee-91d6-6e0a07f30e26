import { Inject, Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { AuthService } from './auth.service';
import { UserCreateModel } from '../../shared/interfaces/users/users-create.model';
import { environment } from '../../../environments/enviroment';
import { EnumsModel } from '../../shared/interfaces/enums/enum.model';
import { UsersGetModel } from '../../shared/interfaces/users/users-get.model';
import { UserResetPasswordModel } from '../../shared/interfaces/users/users-reset-password.model';
import { GeneratePassword } from '../../shared/interfaces/users/users-generate-password.model';
import { UserEditModel } from '../../shared/interfaces/users/users-edit.model';
import { UsersGetByIdModel } from '../../shared/interfaces/users/users-get-by-id.model';


@Injectable({
  providedIn: 'root'
})
export class UsersService {

  private apiUrl = 'https://localhost:44347/Manufacturers'; // Replace with your actual backend URL

  constructor(private http: HttpClient, private authService: AuthService) {}

  
  getUsers(
    search:           string | null,
    role:             string | null,
    sortColumn:       string | null,
    sortDirection:    string | null,
    pageNumber:       number,
    pageSize:         number,
  ): Observable<UsersGetModel> {
    return this.http.get<UsersGetModel>(`${environment.apiUrl}/Users`, {
      params: new HttpParams()
      .set('search', search ? search : '')
      .set('role', role ? role : '')
      .set('sortColumn', sortColumn ? sortColumn : '')
      .set('sortDirection', sortDirection ? sortDirection : '')
      .set('pageNumber', pageNumber.toString())
      .set('pageSize', pageSize.toString())
    });
  }

  getUsersById(userId: string): Observable<UsersGetByIdModel> {
    return this.http.get<UsersGetByIdModel>(`${environment.apiUrl}/Users/<USER>
  }

  getGeneratedPassword(): Observable<GeneratePassword> {
    return this.http.get<GeneratePassword>(`${environment.apiUrl}/Users/<USER>
  }


  addUsers(user: UserCreateModel): Observable<string> {
    return this.http.post(`${environment.apiUrl}/Users`, user, { responseType: 'text' });
  }
  
  editUser(user: UserEditModel) {
    return this.http.put(`${environment.apiUrl}/Users/<USER>
  }

  resetPassword(resetPassword: UserResetPasswordModel, userId: number) {
    return this.http.put(`${environment.apiUrl}/Users/<USER>/${userId}`, resetPassword)
  }


  deleteUser(userId: number) {
    return this.http.delete<number>(`${environment.apiUrl}/Users/<USER>
  }


  getRolesTypes(): Observable<EnumsModel> {
    return this.http.get<EnumsModel>(`${environment.apiUrl}/Enums/UserRoles`);
  }




}