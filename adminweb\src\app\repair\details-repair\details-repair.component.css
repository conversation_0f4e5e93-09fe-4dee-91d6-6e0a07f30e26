.contentPanel {
    padding: 2px;
    width: 100%;
    border-top: 8px solid var(--primary-color);
    justify-content: space-between;
    border-bottom: 1px solid #ccc;
  }
  
  .panelHead {
    margin: 20px;
    display: flex;
    align-items: center;
  }
  
  .h1 {
    text-align: center;
    color: var(--color-main);
    width: 100%;
    border-bottom: 1px solid var(--color-gray-border) !important;
    margin: 0;
    font-family: 'Roboto', sans-serif;
    font-size: 20px;
    font-weight: 700;
    line-height: 16px;
    margin-left: 30px;
  }
  
  .close-icon {
    cursor: pointer;
    font-size: 35px;
    border: none;
    background-color: transparent;
  }
  
  .details-report-content-row {
    display: flex;
    justify-content: space-between;
    margin: 8px 15px;
    padding-bottom: 5px;
    border-bottom: 1px solid #f0f0f0;
  }
  
  #map {
    box-shadow: 0px 4px 4px 0px #00000040;
    margin-left: 15px;
    width: 500px;
    height: 380px;
    margin-bottom: 10px;
    gap: 0px;
    box-sizing: border-box;
  }
  
  strong {
    color: #000000;
  }
  
  p {
    color: #000000;
    margin: 0;
  }
  
  /* Image Slider Styles */
  .image-slider-container {
    margin: 0 15px 15px;
  }
  
  .slider-wrapper {
    position: relative;
    overflow: hidden;
    height: 180px; /* Reduced from 250px */
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  .slider {
    display: flex;
    transition: transform 0.4s ease;
    height: 100%;
  }
  
  .slide {
    min-width: 100%;
    height: 100%;
  }
  
  .slide img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .slider-controls {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 10px;
  }
  
  .nav-btn {
    color: var(--primary-color);
    background-color: transparent;
  }
  
  .slide-indicators {
    display: flex;
    justify-content: center;
    margin: 0 10px;
  }
  
  .slide-indicators span {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #ccc;
    margin: 0 4px;
    cursor: pointer;
    transition: background-color 0.3s ease;
  }
  
  .slide-indicators span.active {
    background-color: var(--primary-color);
  }