import { Injectable, ViewChild, ViewContainerRef } from '@angular/core';
import { Loader } from '@googlemaps/js-api-loader';
import { DynamicComponentLoaderService } from './dynamic-component-loader.service';
import { HttpClient } from '@angular/common/http';
import { lastValueFrom } from 'rxjs';
import { MatDialog } from '@angular/material/dialog';
import { group } from '@angular/animations';
import { environment } from '../../../environments/enviroment';
import { AdvancedMarkerModel } from '../../shared/interfaces/google-map/advanced-marker-model';
import { MarkerModel } from '../../shared/interfaces/google-map/marker.model';

@Injectable({
  providedIn: 'root',
})
export class GoogleMapsService {
  @ViewChild('markerContainer', { read: ViewContainerRef, static: true })
  markerContainer!: ViewContainerRef;

  private loader: Loader;
  private markers: MarkerModel[] = [];
  private zones: (google.maps.Polygon | google.maps.Circle)[] = [];
  private map: google.maps.Map | undefined;
  private infoWindow!: google.maps.InfoWindow;
 //private markerGroups: MarkerGroupModel[] = [];
  private geocoder: google.maps.Geocoder | undefined;
  private viewContainerRef: ViewContainerRef | undefined;
  private placesService: google.maps.places.PlacesService | undefined;
  private isActivePanel = false;
  private defaultIconUrl =
    'https://maps.google.com/mapfiles/ms/icons/red-dot.png';
  private currentActiveGroup: number | null = null;
  private isClickInsideMarker = false;

  constructor(
    private dynamicComponentLoader: DynamicComponentLoaderService,
    private dialog: MatDialog,
    private http: HttpClient,
  ) {
    this.loader = new Loader({
      apiKey: environment.googleMapsApiKey,
      version: 'weekly',
    });
  }

  // async loadMapsLibrary(): Promise<void> {
  //   await this.loader.importLibrary('maps');
  // }
  //
  // async loadMarkerLibrary(): Promise<void> {
  //   if (!window['google'] || !google.maps) {
  //     throw new Error('Google Maps API is not loaded.');
  //   }
  //
  //   await google.maps.importLibrary('marker');
  // }
  //
  // async loadPlacesLibrary(): Promise<void> {
  //   if (!window['google'] || !google.maps) {
  //     throw new Error('Google Maps API is not loaded.');
  //   }
  //
  //   await google.maps.importLibrary('places');
  // }

  public async loadLibraries(): Promise<void> {
    await this.loader.importLibrary('maps');
    await google.maps.importLibrary('marker');
    await google.maps.importLibrary('places');
    this.infoWindow = new google.maps.InfoWindow();
  }

  public async initializeMap(
    mapElement: HTMLElement,
    center: google.maps.LatLngLiteral,
    zoom: number,
    mapId: string,
    style: string
  ): Promise<google.maps.Map> {
    if (!window['google'] || !google.maps) {
      throw new Error('Google Maps API is not loaded.');
    }

    //this.unloadMap();

    if (style == 'day') {
      this.map = new google.maps.Map(mapElement, {
        center,
        zoom,
        mapId,
      });
    } else {
      this.map = new google.maps.Map(mapElement, {
        center,
        zoom,
        mapId,
        mapTypeControlOptions: {
          mapTypeIds: [
            'roadmap',
            'satellite',
            'hybrid',
            'terrain',
            'styled_map',
          ],
        },
      });
      const styledMapType = new google.maps.StyledMapType(
        await this.loadStyledMap(),
        { name: 'Styled Map' }
      );

      this.map.mapTypes.set('styled_map', styledMapType);
      this.map.setMapTypeId('styled_map');
    }

    this.placesService = new google.maps.places.PlacesService(this.map);
    this.geocoder = new google.maps.Geocoder();

    this.map.addListener('zoom_changed', () => {
      this.scaleMarkers();
      //this.replaceMarkersBasedOnZoom();
    });
    // clear selected markers
    this.map.addListener('click', () => {
      if (this.isClickInsideMarker) {
        //this.updateAdvancedMarker(null);
        this.isClickInsideMarker = false;
      }
    });
    return this.map;
  }

  private loadStyledMap(): Promise<any> {
    return lastValueFrom(this.http.get('assets/map-night-vision-style.json'));
  }

  public addAdvancedMarker(
    position: google.maps.LatLngLiteral,
    groupIndex: number,
    details?: AdvancedMarkerModel,
    type?: string,
    iconUrl?: string,
    radius?: number,
    isVisible: boolean = true,
    isForMonitoring: boolean = false,
    markerId?: number,
    isPopulated?: boolean,
    isRightClickAvailable = false,
    markerPanelGroup?: number,
    isFromScreen?: boolean,
  ) {
    if (!this.map) {
      throw new Error('Map is not initialized. Call initializeMap() first.');
    }

    const markerElement = document.createElement('div');
    markerElement.classList.add(`custom-marker`);
    markerElement.innerHTML = `<img src="${
      iconUrl || this.defaultIconUrl
    }" alt="${details?.details?.model ?? ''}" class="custom-marker">`;
    if (markerPanelGroup) {
      markerElement.classList.add(`ep-group-${markerPanelGroup}`);
    }

    if (!isVisible) {
      markerElement.style.display = 'none';
    }
    
    const marker = new google.maps.marker.AdvancedMarkerElement({
        map: this.map,
        position,
        content: markerElement,
        title: details?.details?.model ?? '',
      });

    return marker; // Return the marker so it can be managed externally
  }
  
  /*
  public clearAllMarkers(): void {
     
    // Clear aggregated markers
    this.markerGroups.forEach((group) => {
      if (group.aggregatedMarker) {
        // Remove aggregated marker from the map
        group.aggregatedMarker.marker.map = null;
      }

      group.aggregatedMarker = undefined;
    });
   
    
    // Clear individual markers
    this.markerGroups.forEach((group) => {
      group.markers.forEach(({ marker }) => {
        if (marker) {
          marker.map = null;
        }
      });
      group.markers = []
    });

    
  
    // Remove zones
    this.zones.forEach((zone) => {
      zone.setMap(null);
    });
    this.zones = []; // Clear zones array
  
    this.markerGroups.length = 0;

    // Log for debugging
    console.log('All markers, aggregated markers, and zones cleared', this.markerGroups);
    
  }
  */
 
  /*
  private openDetailsPanel(
    type: string | undefined,
    markerId: number | undefined,
    position: google.maps.LatLngLiteral,
    details?: AdvancedMarkerModel,
    isPopulated?: boolean,
    isFromMap?: boolean,
  ) {
    if (this.infoWindow && this.viewContainerRef) {
      if (type === 'lamp' && markerId) {
        const infoWindowContent = document.createElement('div');
        this.infoWindow.setContent(infoWindowContent);
        this.infoWindow.setPosition(position);
        this.infoWindow.open(this.map);

        this.luminaireAssemblyService
          .getLuminaireAssemblyById(markerId!)
          .subscribe((data) => {
            if (isPopulated) {
              this.luminaireAssemblyService.updateLuminaireAssemblyData(data);
            }

            this.dynamicComponentLoader.loadComponent(
              LampDetailsComponent,
              infoWindowContent,
              this.viewContainerRef!,
              { data: data, infoWindow: this.infoWindow , isFromMap: isFromMap}
            );
          });
      } else if (type === 'electric-panel' && markerId) {
        const infoWindowContent = document.createElement('div');
        this.infoWindow.setContent(infoWindowContent);
        this.infoWindow.setPosition(position);
        this.infoWindow.open(this.map);

        this.panelsService.getPanelById(markerId!).subscribe((data) => {
          if (isPopulated) {
            this.panelsService.updatePanelData(data);
          }

          this.dynamicComponentLoader.loadComponent(
            ElectricPanelDetailsComponent,
            infoWindowContent,
            this.viewContainerRef!,
            { data: data, infoWindow: this.infoWindow, isFromMap: isFromMap }
          );
        });
      }
    }
  }
  */
  
  /*
  public setZone(
    zone: {
      latitude: number;
      longitude: number;
      borderPoints?: { latitude: number; longitude: number }[];
      radius?: number;
    },
    isPopulated?: boolean,
    isBorderPoints?: boolean
  ): void {
    if (!this.map) {
      throw new Error('Map is not initialized. Call initializeMap() first.');
    }

    const zoneCenter = { lat: zone.latitude, lng: zone.longitude };

    // Check if borderPoints should be used
    if (isBorderPoints && zone.borderPoints && zone.borderPoints.length > 0) {
      // Map borderPoints to LatLng objects
      const borderPoints = zone.borderPoints.map((point) => ({
        lat: point.latitude,
        lng: point.longitude,
      }));

      // Draw the polygon
      const polygon = new google.maps.Polygon({
        paths: borderPoints,
        map: this.map,
        fillColor: '#FF69B4', // Pink color
        fillOpacity: 0,
        strokeColor: '#00FF00', // Green color
        strokeOpacity: 0.8,
        strokeWeight: 2,
        clickable: true,
      });

      this.zones.push(polygon);

      // Add a click listener to the polygon
      google.maps.event.addListener(
        polygon,
        'click',
        (event: { latLng: any }) => {
          const latLng = event.latLng;

          // Prepare the zone data with the clicked coordinates
          const updatedZoneData = {
            ...zone,
            coordinates: {
              lat: latLng.lat(),
              lng: latLng.lng(),
            },
          };

          if (isPopulated) {
            this.zonesService.updateZoneData(updatedZoneData);
          }

          if (this.isClickInsideMarker) {
            this.updateAdvancedMarker(null);
            this.isClickInsideMarker = false;
          }

          google.maps.event.trigger(this.map!, 'click', event);
        }
      );

    } else {
      // Draw the circle if borderPoints is false or not provided
      const circle = new google.maps.Circle({
        map: this.map,
        center: zoneCenter,
        radius: zone.radius || 0, // Radius in meters
        fillColor: '#FF69B4', // Pink color
        fillOpacity: 0,
        strokeColor: '#FF69B4', // Pink color
        strokeOpacity: 0.8,
        strokeWeight: 2,
        clickable: true,
      });

      this.zones.push(circle);

      // Add a click listener to the circle
      google.maps.event.addListener(
        circle,
        'click',
        (event: { latLng: any }) => {
          const latLng = event.latLng;

          // Prepare the zone data with the clicked coordinates
          const updatedZoneData = {
            ...zone,
            coordinates: {
              lat: latLng.lat(),
              lng: latLng.lng(),
            },
          };

          if (isPopulated) {
            this.zonesService.updateZoneData(updatedZoneData);
          }
          // clear selected markers
          if (this.isClickInsideMarker) {
            this.updateAdvancedMarker(null);
            this.isClickInsideMarker = false;
          }
          google.maps.event.trigger(this.map!, 'click', event);
        }
      );
    }
  }
  */
  
  /*
  private addAggregatedMarker(
    position: google.maps.LatLngLiteral,
    groupIndex: number,
    count: number | null,
    countColor: string,
    iconUrl?: string
  ): void {
    if (!this.map) {
      throw new Error('Map is not initialized. Call initializeMap() first.');
    }

    const markerElement = document.createElement('div');
    markerElement.classList.add('custom-marker');
    const countHtml = count !== null && count !== undefined 
  ? `<div class="light-count" style="color: ${countColor};">${count}</div>` 
  : '';

markerElement.innerHTML = `
  <div class="custom-marker">
    <img src="${
      iconUrl || this.defaultIconUrl
    }" alt="Aggregated Marker" class="custom-marker">
    ${countHtml} <!-- Include countHtml if count exists -->
  </div>
`;

    markerElement.style.display = 'none'; // Initially hide aggregated markers

    const marker = new google.maps.marker.AdvancedMarkerElement({
      map: this.map,
      position,
      content: markerElement,
      ...(count !== null && count !== undefined && { title: '' }),
    });

    if (!this.markerGroups[groupIndex]) {
      this.markerGroups[groupIndex] = {
        markers: [],
        aggregatedMarker: undefined as any,
      };
    }
    this.markerGroups[groupIndex].aggregatedMarker = {
      marker,
      element: markerElement,
      count,
    };
  }
    */
  
  /*
  public replaceMarkersBasedOnZoom(isCustomThresholdZoom?: boolean): void {
    if (!this.map) return;
    const zoom = this.map.getZoom();
    let thresholdZoom = 15;

    if (isCustomThresholdZoom) {
      thresholdZoom = 5; // Define your zoom level threshold
    }

    this.markerGroups.forEach((group) => {
      if (zoom && zoom < thresholdZoom) {
        // Hide individual markers and show aggregated markers
        group.markers.forEach(
          ({ element }) => (element.style.display = 'none')
        );
        if (group.aggregatedMarker) {
          group.aggregatedMarker.element.style.display = 'block';
        }
      } else {
        // Show individual markers and hide aggregated markers
        group.markers.forEach(
          ({ element }) => (element.style.display = 'block')
        );
        if (group.aggregatedMarker) {
          group.aggregatedMarker.element.style.display = 'none';
        }
      }
    });
  }
 */
  
  /*
  public initializeAggregatedMarkers(
    aggregatedGroups: AggregatedGroupModel[],
    countColor: string
  ): void {
    aggregatedGroups.forEach((group) => {
      this.addAggregatedMarker(
        group.position,
        group.groupIndex,
        group.count,
        countColor,
        group.iconUrl
      );
    });
  }
    */

  public setViewContainerRef(viewContainerRef: ViewContainerRef): void {
    this.viewContainerRef = viewContainerRef;
  }

  public searchPlace(
    query: string,
    location: google.maps.LatLngLiteral,
    radius: number,
    callback: (
      results: google.maps.places.PlaceResult[] | null,
      status: google.maps.places.PlacesServiceStatus
    ) => void
  ): void {
    if (!this.placesService) {
      throw new Error('PlacesService is not initialized.');
    }

    const request: google.maps.places.FindPlaceFromQueryRequest = {
      query,
      fields: ['name', 'geometry'],
      locationBias: {
        radius,
        center: location,
      },
    };

    this.placesService.findPlaceFromQuery(request, callback);
  }

  public geocodeAddress(
    address: string,
    callback: (
      results: google.maps.GeocoderResult[] | null,
      status: google.maps.GeocoderStatus
    ) => void
  ): void {
    if (!this.geocoder) {
      throw new Error('Geocoder is not initialized.');
    }

    this.geocoder.geocode({ address }, callback);
  }

  public setMapCenterAndZoom(
    position: google.maps.LatLngLiteral,
    zoom: number
  ): void {
    if (!this.map) {
      throw new Error('Map is not initialized. Call initializeMap() first.');
    }
    this.map.setCenter(position);
    this.map.setZoom(zoom);
  }

  public showSearchInfoWindow(
    position: google.maps.LatLngLiteral,
    content: string
  ): void {
    const styledContent = `
    <div class="custom-info-search-window">
      <h4>${content}</h4>
    </div>
  `;
    this.infoWindow.setPosition(position);
    this.infoWindow.setContent(styledContent);
    this.infoWindow.open(this.map);
  }
  
  
  private scaleMarkers(): void {
    if (!this.map) return;
    const zoom = this.map.getZoom();
    const scale = 1 + (zoom! - 18) / 10; // Adjust the scaling factor as needed
    this.markers.forEach(({ element }) => {
      const img = element.querySelector('img') as HTMLElement;
      if (img) {
        img.style.transform = `scale(${scale})`;
      }
    });
  }
  

  setClickListener(
    callback: (latLng: google.maps.LatLngLiteral) => void
  ): void {
    if (!this.map) {
      throw new Error('Map is not initialized. Call initializeMap() first.');
    }

    this.map.addListener('click', (event: google.maps.MapMouseEvent) => {
      if (event.latLng) {
        const latLng: google.maps.LatLngLiteral = {
          lat: event.latLng.lat(),
          lng: event.latLng.lng(),
        };
        callback(latLng);
      }
    });
  }
  
  
  public unloadMap() {
    if (this.map) {
      google.maps.event.clearInstanceListeners(this.map);
      if (this.markers) {
        this.markers.forEach(({ marker }) => (marker.map = null));
        this.markers = [];
      }
      this.map = undefined;
    }
  }
    
  
 
  public calculateMidpoint(coords: { lat: number; lng: number }[]): { lat: number; lng: number } {
    if (coords.length === 0) {
      throw new Error("Coordinate array is empty");
  }

  // Step 1: Calculate rough geographical center
  let centerLat = 0, centerLng = 0;
  coords.forEach(({ lat, lng }) => {
      centerLat += lat;
      centerLng += lng;
  });
  centerLat /= coords.length;
  centerLng /= coords.length;

  // Step 2: Compute distances from the rough center
  const distances = coords.map(({ lat, lng }) => {
      const distance = Math.sqrt((lat - centerLat) ** 2 + (lng - centerLng) ** 2);
      return { lat, lng, distance };
  });

  // Step 3: Select the middle markers (e.g., top 50% closest to center)
  const sortedDistances = distances.sort((a, b) => a.distance - b.distance);
  const middleMarkers = sortedDistances.slice(0, Math.ceil(coords.length / 2)); // Adjust percentage as needed

  // Step 4: Calculate the center of the middle markers
  let weightedLat = 0, weightedLng = 0;
  middleMarkers.forEach(({ lat, lng }) => {
      weightedLat += lat;
      weightedLng += lng;
  });
  weightedLat /= middleMarkers.length;
  weightedLng /= middleMarkers.length;

  const midpoint = { lat: weightedLat, lng: weightedLng };

  return midpoint;
  }
  

}
