<mat-toolbar class="tool-bar">
  <button mat-icon-button aria-label="Menu icon" *ngIf="!isMobile && isLoggedIn" (click)="toggleSidebar()">
      <mat-icon *ngIf="!isLoginPage()">menu</mat-icon>
    </button>
    <div class="toolbar-title">
      <div class="navigation-bar-left">
        <h1>
          Smart Yambol
        </h1>
        <span class="toolbar-separator">|</span> <!-- Separator -->
        <span class="admin-text">
          {{ isLoginPage() ? 
            ('LoginText' | translate) : 
            (role === 'AdminPlatform' ? 
              ('AdminPlatform' | translate) : 
              (role?.startsWith('Admin') ? 
                ('Admin' | translate) : 
                (role?.startsWith('UserMunicipality') ? 
                  ('UserMunicipality' | translate) : 
                  roleLabel))) 
          }}
        </span> <!-- Administrator text -->
      </div>
      <div class="logo-container">
      <img src="../../assets/images/logo/logo.png" alt="Smart Lighting Logo" class="logo"/>
      </div>
      <div class="navigation-bar-right">
        <div class="navigation-login-icon" *ngIf="!isLoginPage()">
          <button mat-button (click)="onLogOut()" matTooltip="{{ userFullName }}">
            <mat-icon>logout</mat-icon>
          </button>
        </div>

        <div class="language-dropdown">
          <span class="selected-language">
            {{ selectedLanguage }}
            <mat-icon class="language-button" (click)="toggleLanguage()" [ngClass]="{'rotated' : isLanguageChosen}">expand_more</mat-icon>
          </span>
        </div>
      </div>
    </div>
    <!-- Line -->
    <!-- <div class="toolbar-line"></div> -->
  </mat-toolbar>

  <mat-sidenav-container>
    <mat-sidenav [disableClose]="true" *ngIf="!isLoginPage()" #sidenav [opened]="true" mode="side" class="sidebar" [ngClass]="{'collapsed': shouldCollapseNavbar, 'expanded': !shouldCollapseNavbar}">
      <mat-nav-list>
      
        <!-- News -->
        <a mat-list-item routerLink="/news" [routerLinkActive]="['is-active']">
          <span class="menu-selection-item" [ngClass]="{'justify-content-center': shouldCollapseNavbar}">
            <mat-icon>article</mat-icon>
            <span class="text" *ngIf="!shouldCollapseNavbar">{{ "News" | translate }}</span>
          </span>
        </a>

        <mat-divider></mat-divider>

      <!-- Weddings -->
      <a mat-list-item routerLink="/weddings" [routerLinkActive]="['is-active']">
        <span class="menu-selection-item" [ngClass]="{'justify-content-center': shouldCollapseNavbar}">
          <mat-icon>favorite</mat-icon>
          <span class="text" *ngIf="!shouldCollapseNavbar">{{ "weddings" | translate }}</span>
        </span>
      </a>

      <mat-divider></mat-divider>

      <!-- Dropdown for Business -->
      <a mat-list-item (click)="isSubMenuBusiness = !isSubMenuBusiness;">
        <span class="expandable-menu-selection-item" [ngClass]="{'justify-content-center': shouldCollapseNavbar}">
          <mat-icon class="events-menu-icon">business</mat-icon>
          <span class="text" *ngIf="!shouldCollapseNavbar">{{ "business" | translate }}</span>
          <mat-icon class="menu-button" [ngClass]="{'rotated' : isSubMenuBusiness}" *ngIf="!shouldCollapseNavbar">expand_more</mat-icon>
        </span>
    </a>

    <mat-divider *ngIf="isSubMenuBusiness"></mat-divider>

     <!-- Dropdown for Venues -->
     <div class="nestedmenus" *ngIf="isSubMenuBusiness">
  <a mat-list-item (click)="isSubMenuVenues = !isSubMenuVenues;">
    <span class="expandable-menu-selection-item" [ngClass]="{'justify-content-center': shouldCollapseNavbar}">
      <mat-icon class="events-menu-icon">restaurant</mat-icon>
      <span class="text" *ngIf="!shouldCollapseNavbar">{{ "hoReKa" | translate }}</span>
      <mat-icon class="menu-button" [ngClass]="{'rotated' : isSubMenuVenues}" *ngIf="!shouldCollapseNavbar">expand_more</mat-icon>
    </span>
</a>
</div>

<mat-divider *ngIf="isSubMenuVenues && isSubMenuBusiness"></mat-divider>

        <!-- Restaurants -->
        <div class="events-submenu business-sub-sub-menu" *ngIf="isSubMenuVenues && isSubMenuBusiness">
          <a mat-list-item routerLink="business/venues/restaurants" [routerLinkActive]="['is-active']">
            <span class="submenu-selection-item" [ngClass]="{'justify-content-center': shouldCollapseNavbar}">
              <mat-icon>dining</mat-icon>
              <span class="text" *ngIf="!shouldCollapseNavbar">{{ "restaurants" | translate }}</span>
            </span>
          </a>
        </div>

<mat-divider *ngIf="isSubMenuVenues && isSubMenuBusiness"></mat-divider>

        <!-- Cafes -->
        <div class="events-submenu business-sub-sub-menu" *ngIf="isSubMenuVenues && isSubMenuBusiness">
          <a mat-list-item routerLink="business/venues/cafes" [routerLinkActive]="['is-active']">
            <span class="submenu-selection-item" [ngClass]="{'justify-content-center': shouldCollapseNavbar}">
              <mat-icon>coffee</mat-icon>
              <span class="text" *ngIf="!shouldCollapseNavbar">{{ "cafes" | translate }}</span>
            </span>
          </a>
        </div>

  <mat-divider *ngIf="isSubMenuVenues && isSubMenuBusiness"></mat-divider>

        <!-- bars -->
        <div class="events-submenu business-sub-sub-menu" *ngIf="isSubMenuVenues && isSubMenuBusiness">
          <a mat-list-item routerLink="business/venues/bars" [routerLinkActive]="['is-active']">
            <span class="submenu-selection-item" [ngClass]="{'justify-content-center': shouldCollapseNavbar}">
              <mat-icon>local_bar</mat-icon>
              <span class="text" *ngIf="!shouldCollapseNavbar">{{ "bars" | translate }}</span>
            </span>
          </a>
        </div>

  <mat-divider *ngIf="isSubMenuVenues && isSubMenuBusiness"></mat-divider>

        <!-- pastryShop -->
        <div class="events-submenu business-sub-sub-menu" *ngIf="isSubMenuVenues && isSubMenuBusiness">
          <a mat-list-item routerLink="business/venues/pastryShop" [routerLinkActive]="['is-active']">
            <span class="submenu-selection-item" [ngClass]="{'justify-content-center': shouldCollapseNavbar}">
              <mat-icon>cake</mat-icon>
              <span class="text" *ngIf="!shouldCollapseNavbar">{{ "pastryShop" | translate }}</span>
            </span>
          </a>
        </div>

  <mat-divider *ngIf="isSubMenuBusiness"></mat-divider>

        <!-- gasStation -->
        <div class="nested-single-menus" *ngIf="isSubMenuBusiness">
          <a mat-list-item routerLink="business/gas-stations" [routerLinkActive]="['is-active']">
            <span class="submenu-selection-item" [ngClass]="{'justify-content-center': shouldCollapseNavbar}">
              <mat-icon>local_gas_station</mat-icon>
              <span class="text" *ngIf="!shouldCollapseNavbar">{{ "gasStation" | translate }}</span>
            </span>
          </a>
        </div>

  <mat-divider *ngIf="isSubMenuBusiness"></mat-divider>

        <!-- shops -->
        <div class="nested-single-menus" *ngIf="isSubMenuBusiness">
          <a mat-list-item routerLink="business/shops" [routerLinkActive]="['is-active']">
            <span class="submenu-selection-item" [ngClass]="{'justify-content-center': shouldCollapseNavbar}">
              <mat-icon>shopping_cart</mat-icon>
              <span class="text" *ngIf="!shouldCollapseNavbar">{{ "shops" | translate }}</span>
            </span>
          </a>
        </div>

  <mat-divider *ngIf="isSubMenuBusiness"></mat-divider>

  <!-- Dropdown for Accommodation -->
  <div class="nestedmenus" *ngIf="isSubMenuBusiness">
  <a mat-list-item (click)="isSubMenuAccommodation = !isSubMenuAccommodation;">
       <span class="expandable-menu-selection-item" [ngClass]="{'justify-content-center': shouldCollapseNavbar}">
         <mat-icon class="events-menu-icon">hotel</mat-icon>
         <span class="text" *ngIf="!shouldCollapseNavbar">{{ "Accommodation" | translate }}</span>
         <mat-icon class="menu-button" [ngClass]="{'rotated' : isSubMenuAccommodation}" *ngIf="!shouldCollapseNavbar">expand_more</mat-icon>
       </span>
   </a>
   </div>

   <mat-divider *ngIf="isSubMenuAccommodation && isSubMenuBusiness"></mat-divider>
   
   <!-- hotels -->
   <div class="events-submenu business-sub-sub-menu" *ngIf="isSubMenuAccommodation && isSubMenuBusiness">
    <a mat-list-item routerLink="business/accommodation/hotels" [routerLinkActive]="['is-active']">
      <span class="submenu-selection-item" [ngClass]="{'justify-content-center': shouldCollapseNavbar}">
        <mat-icon>bed</mat-icon>
        <span class="text" *ngIf="!shouldCollapseNavbar">{{ "hotels" | translate }}</span>
      </span>
    </a>
  </div>

  <mat-divider *ngIf="isSubMenuAccommodation && isSubMenuBusiness"></mat-divider>
   
  <!-- guestHouses -->
  <div class="events-submenu business-sub-sub-menu" *ngIf="isSubMenuAccommodation && isSubMenuBusiness">
   <a mat-list-item routerLink="business/accommodation/guest-houses" [routerLinkActive]="['is-active']">
     <span class="submenu-selection-item" [ngClass]="{'justify-content-center': shouldCollapseNavbar}">
       <mat-icon>cottage</mat-icon>
       <span class="text" *ngIf="!shouldCollapseNavbar">{{ "guestHouses" | translate }}</span>
     </span>
   </a>
 </div>

 <mat-divider *ngIf="isSubMenuBusiness"></mat-divider>

  <!-- Dropdown for Finance -->
  <div class="nestedmenus" *ngIf="isSubMenuBusiness">
  <a mat-list-item (click)="isSubMenuFinance = !isSubMenuFinance;">
       <span class="expandable-menu-selection-item" [ngClass]="{'justify-content-center': shouldCollapseNavbar}">
         <mat-icon class="events-menu-icon">attach_money</mat-icon>
         <span class="text" *ngIf="!shouldCollapseNavbar">{{ "Finance" | translate }}</span>
         <mat-icon class="menu-button" [ngClass]="{'rotated' : isSubMenuFinance}" *ngIf="!shouldCollapseNavbar">expand_more</mat-icon>
       </span>
   </a>
   </div>

   <mat-divider *ngIf="isSubMenuFinance && isSubMenuBusiness"></mat-divider>
   
   <!-- banks -->
   <div class="events-submenu business-sub-sub-menu" *ngIf="isSubMenuFinance && isSubMenuBusiness">
    <a mat-list-item routerLink="business/finance/banks" [routerLinkActive]="['is-active']">
      <span class="submenu-selection-item" [ngClass]="{'justify-content-center': shouldCollapseNavbar}">
        <mat-icon>credit_card</mat-icon>
        <span class="text" *ngIf="!shouldCollapseNavbar">{{ "banks" | translate }}</span>
      </span>
    </a>
  </div>

  <mat-divider *ngIf="isSubMenuFinance && isSubMenuBusiness"></mat-divider>
   
   <!-- currencyChange -->
   <div class="events-submenu business-sub-sub-menu" *ngIf="isSubMenuFinance && isSubMenuBusiness">
    <a mat-list-item routerLink="business/finance/currency-change" [routerLinkActive]="['is-active']">
      <span class="submenu-selection-item" [ngClass]="{'justify-content-center': shouldCollapseNavbar}">
        <mat-icon>currency_exchange</mat-icon>
        <span class="text" *ngIf="!shouldCollapseNavbar">{{ "currencyChange" | translate }}</span>
      </span>
    </a>
  </div>

  <mat-divider *ngIf="isSubMenuFinance && isSubMenuBusiness"></mat-divider>
   
   <!-- insuranceCompanies -->
   <div class="events-submenu business-sub-sub-menu" *ngIf="isSubMenuFinance && isSubMenuBusiness">
    <a mat-list-item routerLink="business/finance/insurance-companies" [routerLinkActive]="['is-active']">
      <span class="submenu-selection-item" [ngClass]="{'justify-content-center': shouldCollapseNavbar}">
        <mat-icon>security</mat-icon>
        <span class="text" *ngIf="!shouldCollapseNavbar">{{ "insuranceCompanies" | translate }}</span>
      </span>
    </a>
  </div>

  <mat-divider *ngIf="isSubMenuFinance && isSubMenuBusiness"></mat-divider>
   
   <!-- ATMs -->
   <div class="events-submenu business-sub-sub-menu" *ngIf="isSubMenuFinance && isSubMenuBusiness">
    <a mat-list-item routerLink="business/finance/atms" [routerLinkActive]="['is-active']">
      <span class="submenu-selection-item" [ngClass]="{'justify-content-center': shouldCollapseNavbar}">
        <mat-icon>atm</mat-icon>
        <span class="text" *ngIf="!shouldCollapseNavbar">{{ "ATMs" | translate }}</span>
      </span>
    </a>
  </div>

  <mat-divider *ngIf="isSubMenuBusiness"></mat-divider>

  <!-- Dropdown for Ecology -->
  <div class="nestedmenus" *ngIf="isSubMenuBusiness">
  <a mat-list-item (click)="isSubMenuEcology = !isSubMenuEcology;">
       <span class="expandable-menu-selection-item" [ngClass]="{'justify-content-center': shouldCollapseNavbar}">
         <mat-icon class="events-menu-icon">nature</mat-icon>
         <span class="text" *ngIf="!shouldCollapseNavbar">{{ "Ecology" | translate }}</span>
         <mat-icon class="menu-button" [ngClass]="{'rotated' : isSubMenuEcology}" *ngIf="!shouldCollapseNavbar">expand_more</mat-icon>
       </span>
   </a>
   </div>

   <mat-divider *ngIf="isSubMenuEcology && isSubMenuBusiness"></mat-divider>
   
   <!-- bioShops -->
   <div class="events-submenu business-sub-sub-menu" *ngIf="isSubMenuEcology && isSubMenuBusiness">
    <a mat-list-item routerLink="business/ecology/bio-shops" [routerLinkActive]="['is-active']">
      <span class="submenu-selection-item" [ngClass]="{'justify-content-center': shouldCollapseNavbar}">
        <mat-icon>eco</mat-icon>
        <span class="text" *ngIf="!shouldCollapseNavbar">{{ "bioShops" | translate }}</span>
      </span>
    </a>
  </div>

  <mat-divider *ngIf="isSubMenuEcology && isSubMenuBusiness"></mat-divider>
   
   <!-- farms -->
   <div class="events-submenu business-sub-sub-menu" *ngIf="isSubMenuEcology && isSubMenuBusiness">
    <a mat-list-item routerLink="business/ecology/farms" [routerLinkActive]="['is-active']">
      <span class="submenu-selection-item" [ngClass]="{'justify-content-center': shouldCollapseNavbar}">
        <mat-icon>agriculture</mat-icon>
        <span class="text" *ngIf="!shouldCollapseNavbar">{{ "farms" | translate }}</span>
      </span>
    </a>
  </div>

  <mat-divider *ngIf="isSubMenuEcology && isSubMenuBusiness"></mat-divider>
   
   <!-- recyclingCenter -->
   <div class="events-submenu business-sub-sub-menu" *ngIf="isSubMenuEcology && isSubMenuBusiness">
    <a mat-list-item routerLink="business/ecology/recycling-centers" [routerLinkActive]="['is-active']">
      <span class="submenu-selection-item" [ngClass]="{'justify-content-center': shouldCollapseNavbar}">
        <mat-icon>recycling</mat-icon>
        <span class="text" *ngIf="!shouldCollapseNavbar">{{ "recyclingCenter" | translate }}</span>
      </span>
    </a>
  </div>

  <mat-divider *ngIf="isSubMenuEcology && isSubMenuBusiness"></mat-divider>
   
   <!-- ecologyInitiatives -->
   <div class="events-submenu business-sub-sub-menu" *ngIf="isSubMenuEcology && isSubMenuBusiness">
    <a mat-list-item routerLink="business/ecology/ecology-initiatives" [routerLinkActive]="['is-active']">
      <span class="submenu-selection-item" [ngClass]="{'justify-content-center': shouldCollapseNavbar}">
        <mat-icon>nature_people</mat-icon>
        <span class="text" *ngIf="!shouldCollapseNavbar">{{ "ecologyInitiatives" | translate }}</span>
      </span>
    </a>
  </div>

  <mat-divider *ngIf="isSubMenuBusiness"></mat-divider>

  <!-- Dropdown for Culture -->
  <div class="nestedmenus" *ngIf="isSubMenuBusiness">
  <a mat-list-item (click)="isSubMenuCulture = !isSubMenuCulture;">
       <span class="expandable-menu-selection-item" [ngClass]="{'justify-content-center': shouldCollapseNavbar}">
         <mat-icon class="events-menu-icon">theaters</mat-icon>
         <span class="text" *ngIf="!shouldCollapseNavbar">{{ "Culture" | translate }}</span>
         <mat-icon class="menu-button" [ngClass]="{'rotated' : isSubMenuCulture}" *ngIf="!shouldCollapseNavbar">expand_more</mat-icon>
       </span>
   </a>
   </div>

   <mat-divider *ngIf="isSubMenuCulture && isSubMenuBusiness"></mat-divider>
   
   <!-- museums -->
   <div class="events-submenu business-sub-sub-menu" *ngIf="isSubMenuCulture && isSubMenuBusiness">
    <a mat-list-item routerLink="business/culture/museums" [routerLinkActive]="['is-active']">
      <span class="submenu-selection-item" [ngClass]="{'justify-content-center': shouldCollapseNavbar}">
        <mat-icon>museum</mat-icon>
        <span class="text" *ngIf="!shouldCollapseNavbar">{{ "museums" | translate }}</span>
      </span>
    </a>
  </div>

  <mat-divider *ngIf="isSubMenuCulture && isSubMenuBusiness"></mat-divider>
   
   <!-- theaters -->
   <div class="events-submenu business-sub-sub-menu" *ngIf="isSubMenuCulture && isSubMenuBusiness">
    <a mat-list-item routerLink="business/culture/theaters" [routerLinkActive]="['is-active']">
      <span class="submenu-selection-item" [ngClass]="{'justify-content-center': shouldCollapseNavbar}">
        <mat-icon>local_activity</mat-icon>
        <span class="text" *ngIf="!shouldCollapseNavbar">{{ "theaters" | translate }}</span>
      </span>
    </a>
  </div>

  <mat-divider *ngIf="isSubMenuCulture && isSubMenuBusiness"></mat-divider>
   
   <!-- galleries -->
   <div class="events-submenu business-sub-sub-menu" *ngIf="isSubMenuCulture && isSubMenuBusiness">
    <a mat-list-item routerLink="business/culture/galleries" [routerLinkActive]="['is-active']">
      <span class="submenu-selection-item" [ngClass]="{'justify-content-center': shouldCollapseNavbar}">
        <mat-icon>collections</mat-icon>
        <span class="text" *ngIf="!shouldCollapseNavbar">{{ "galleries" | translate }}</span>
      </span>
    </a>
  </div>

    <mat-divider></mat-divider>

    <!-- Dropdown for Tourism -->
    <a mat-list-item (click)="isSubMenuTourism = !isSubMenuTourism;">
      <span class="expandable-menu-selection-item" [ngClass]="{'justify-content-center': shouldCollapseNavbar}">
        <mat-icon class="events-menu-icon">map</mat-icon>
        <span class="text" *ngIf="!shouldCollapseNavbar">{{ "tourism" | translate }}</span>
        <mat-icon class="menu-button" [ngClass]="{'rotated' : isSubMenuTourism}" *ngIf="!shouldCollapseNavbar">expand_more</mat-icon>
      </span>
  </a>

  <mat-divider *ngIf="isSubMenuTourism"></mat-divider>

        <!-- legends and myths -->
        <div class="nested-single-menus" *ngIf="isSubMenuTourism">
          <a mat-list-item routerLink="tourism/legends-and-myths" [routerLinkActive]="['is-active']">
            <span class="submenu-selection-item" [ngClass]="{'justify-content-center': shouldCollapseNavbar}">
              <mat-icon>history_edu</mat-icon>
              <span class="text" *ngIf="!shouldCollapseNavbar">{{ "legendsAndMyths" | translate }}</span>
            </span>
          </a>
        </div>

  <mat-divider *ngIf="isSubMenuTourism"></mat-divider>

    <!-- Landmarks -->
    <div class="nested-single-menus" *ngIf="isSubMenuTourism">
    <a mat-list-item routerLink="tourism/landmarks" [routerLinkActive]="['is-active']">
      <span class="submenu-selection-item" [ngClass]="{'justify-content-center': shouldCollapseNavbar}">
        <mat-icon>tour</mat-icon>
        <span class="text" *ngIf="!shouldCollapseNavbar">{{ "landmarks" | translate }}</span>
      </span>
    </a>
    </div>

  <mat-divider *ngIf="isSubMenuTourism"></mat-divider>

    <!-- cultureAndArtisticPlaces -->
    <div class="nested-single-menus" *ngIf="isSubMenuTourism">
    <a mat-list-item routerLink="tourism/culture-and-artistic-places" [routerLinkActive]="['is-active']">
      <span class="submenu-selection-item" [ngClass]="{'justify-content-center': shouldCollapseNavbar}">
        <mat-icon>account_balance</mat-icon>
        <span class="text" *ngIf="!shouldCollapseNavbar">{{ "cultureAndArtisticPlaces" | translate }}</span>
      </span>
    </a>
    </div>

  <mat-divider *ngIf="isSubMenuTourism"></mat-divider>

    <!-- RoutesAndActivities -->
    <div class="nested-single-menus" *ngIf="isSubMenuTourism">
    <a mat-list-item routerLink="tourism/routes-and-activities" [routerLinkActive]="['is-active']">
      <span class="submenu-selection-item" [ngClass]="{'justify-content-center': shouldCollapseNavbar}">
        <mat-icon>alt_route</mat-icon>
        <span class="text" *ngIf="!shouldCollapseNavbar">{{ "RoutesAndActivities" | translate }}</span>
      </span>
    </a>
    </div>

  <mat-divider *ngIf="isSubMenuTourism"></mat-divider>

    <!-- FamilyFun -->
    <div class="nested-single-menus" *ngIf="isSubMenuTourism">
    <a mat-list-item routerLink="tourism/family-fun" [routerLinkActive]="['is-active']">
      <span class="submenu-selection-item" [ngClass]="{'justify-content-center': shouldCollapseNavbar}">
        <mat-icon>family_restroom</mat-icon>
        <span class="text" *ngIf="!shouldCollapseNavbar">{{ "FamilyFun" | translate }}</span>
      </span>
    </a>
    </div>

  <mat-divider *ngIf="isSubMenuTourism"></mat-divider>

    <!-- NightLife -->
    <div class="nested-single-menus" *ngIf="isSubMenuTourism">
    <a mat-list-item routerLink="tourism/night-life" [routerLinkActive]="['is-active']">
      <span class="submenu-selection-item" [ngClass]="{'justify-content-center': shouldCollapseNavbar}">
        <mat-icon>nightlife</mat-icon>
        <span class="text" *ngIf="!shouldCollapseNavbar">{{ "NightLife" | translate }}</span>
      </span>
    </a>
    </div>

  <mat-divider *ngIf="isSubMenuTourism"></mat-divider>

    <!-- Transport -->
    <div class="nested-single-menus" *ngIf="isSubMenuTourism">
    <a mat-list-item routerLink="tourism/transport" [routerLinkActive]="['is-active']">
      <span class="submenu-selection-item" [ngClass]="{'justify-content-center': shouldCollapseNavbar}">
        <mat-icon>directions_bus</mat-icon>
        <span class="text" *ngIf="!shouldCollapseNavbar">{{ "transport" | translate }}</span>
      </span>
    </a>
    </div>

  <mat-divider *ngIf="isSubMenuTourism"></mat-divider>

    <!-- travelAgencies -->
    <div class="nested-single-menus" *ngIf="isSubMenuTourism">
    <a mat-list-item routerLink="tourism/travel-agencies" [routerLinkActive]="['is-active']">
      <span class="submenu-selection-item" [ngClass]="{'justify-content-center': shouldCollapseNavbar}">
        <mat-icon>public</mat-icon>
        <span class="text" *ngIf="!shouldCollapseNavbar">{{ "travelAgencies" | translate }}</span>
      </span>
    </a>
    </div>

  <mat-divider></mat-divider>

  <!-- Dropdown for WorkAndTraining -->
  <a mat-list-item (click)="isSubMenuWorkAndTraining = !isSubMenuWorkAndTraining;">
    <span class="expandable-menu-selection-item" [ngClass]="{'justify-content-center': shouldCollapseNavbar}">
      <mat-icon class="events-menu-icon">assignment</mat-icon>
      <span class="text" *ngIf="!shouldCollapseNavbar">{{ "workAndTraining" | translate }}</span>
      <mat-icon class="menu-button" [ngClass]="{'rotated' : isSubMenuWorkAndTraining}" *ngIf="!shouldCollapseNavbar">expand_more</mat-icon>
    </span>
</a>

<mat-divider *ngIf="isSubMenuWorkAndTraining"></mat-divider>

        <!-- work -->
        <div class="nested-single-menus" *ngIf="isSubMenuWorkAndTraining">
          <a mat-list-item routerLink="work-and-training/work" [routerLinkActive]="['is-active']">
            <span class="submenu-selection-item" [ngClass]="{'justify-content-center': shouldCollapseNavbar}">
              <mat-icon>business_center</mat-icon>
              <span class="text" *ngIf="!shouldCollapseNavbar">{{ "work" | translate }}</span>
            </span>
          </a>
        </div>

<mat-divider *ngIf="isSubMenuWorkAndTraining"></mat-divider>

        <!-- internshipsAndPrograms -->
        <div class="nested-single-menus" *ngIf="isSubMenuWorkAndTraining">
          <a mat-list-item routerLink="work-and-training/internships-and-programs" [routerLinkActive]="['is-active']">
            <span class="submenu-selection-item" [ngClass]="{'justify-content-center': shouldCollapseNavbar}">
              <mat-icon>badge</mat-icon>
              <span class="text" *ngIf="!shouldCollapseNavbar">{{ "internshipsAndPrograms" | translate }}</span>
            </span>
          </a>
        </div>
 
<mat-divider></mat-divider>

<!-- Dropdown for Education -->
<a mat-list-item (click)="isSubMenuEducation = !isSubMenuEducation;">
  <span class="expandable-menu-selection-item" [ngClass]="{'justify-content-center': shouldCollapseNavbar}">
    <mat-icon class="events-menu-icon">auto_stories</mat-icon>
    <span class="text" *ngIf="!shouldCollapseNavbar">{{ "Education" | translate }}</span>
    <mat-icon class="menu-button" [ngClass]="{'rotated' : isSubMenuEducation}" *ngIf="!shouldCollapseNavbar">expand_more</mat-icon>
  </span>
</a>

<mat-divider *ngIf="isSubMenuEducation"></mat-divider>

        <!-- kindergardens -->
        <div class="nested-single-menus" *ngIf="isSubMenuEducation">
          <a mat-list-item routerLink="education/kindergardens" [routerLinkActive]="['is-active']">
            <span class="submenu-selection-item" [ngClass]="{'justify-content-center': shouldCollapseNavbar}">
              <mat-icon>toys</mat-icon>
              <span class="text" *ngIf="!shouldCollapseNavbar">{{ "kindergardens" | translate }}</span>
            </span>
          </a>
        </div>

<mat-divider *ngIf="isSubMenuEducation"></mat-divider>

        <!-- nursery -->
        <div class="nested-single-menus" *ngIf="isSubMenuEducation">
          <a mat-list-item routerLink="education/nursery" [routerLinkActive]="['is-active']">
            <span class="submenu-selection-item" [ngClass]="{'justify-content-center': shouldCollapseNavbar}">
              <mat-icon>crib</mat-icon>
              <span class="text" *ngIf="!shouldCollapseNavbar">{{ "nursery" | translate }}</span>
            </span>
          </a>
        </div>

<mat-divider *ngIf="isSubMenuEducation"></mat-divider>

        <!-- childNutritionCenter -->
        <div class="nested-single-menus" *ngIf="isSubMenuEducation">
          <a mat-list-item routerLink="education/child-nutrition-center" [routerLinkActive]="['is-active']">
            <span class="submenu-selection-item" [ngClass]="{'justify-content-center': shouldCollapseNavbar}">
              <mat-icon>lunch_dining</mat-icon>
              <span class="text" *ngIf="!shouldCollapseNavbar">{{ "childNutritionCenter" | translate }}</span>
            </span>
          </a>
        </div>

 <mat-divider *ngIf="isSubMenuEducation"></mat-divider>

        <!-- schools -->
        <div class="nested-single-menus" *ngIf="isSubMenuEducation">
          <a mat-list-item routerLink="education/schools" [routerLinkActive]="['is-active']">
            <span class="submenu-selection-item" [ngClass]="{'justify-content-center': shouldCollapseNavbar}">
              <mat-icon>menu_book</mat-icon>
              <span class="text" *ngIf="!shouldCollapseNavbar">{{ "schools" | translate }}</span>
            </span>
          </a>
        </div>

  <mat-divider *ngIf="isSubMenuEducation"></mat-divider>

        <!-- universities -->
        <div class="nested-single-menus" *ngIf="isSubMenuEducation">
          <a mat-list-item routerLink="education/universities" [routerLinkActive]="['is-active']">
            <span class="submenu-selection-item" [ngClass]="{'justify-content-center': shouldCollapseNavbar}">
              <mat-icon>school</mat-icon>
              <span class="text" *ngIf="!shouldCollapseNavbar">{{ "universities" | translate }}</span>
            </span>
          </a>
        </div>

  <mat-divider *ngIf="isSubMenuEducation"></mat-divider>

        <!-- developmentCenters -->
        <div class="nested-single-menus" *ngIf="isSubMenuEducation">
          <a mat-list-item routerLink="education/developments-centers" [routerLinkActive]="['is-active']">
            <span class="submenu-selection-item" [ngClass]="{'justify-content-center': shouldCollapseNavbar}">
              <mat-icon>groups</mat-icon>
              <span class="text" *ngIf="!shouldCollapseNavbar">{{ "developmentCenters" | translate }}</span>
            </span>
          </a>
        </div>

<mat-divider></mat-divider>

<!-- Dropdown for Health -->
<a mat-list-item (click)="isSubMenuHealth = !isSubMenuHealth;">
  <span class="expandable-menu-selection-item" [ngClass]="{'justify-content-center': shouldCollapseNavbar}">
    <mat-icon class="events-menu-icon">medical_services</mat-icon>
    <span class="text" *ngIf="!shouldCollapseNavbar">{{ "health" | translate }}</span>
    <mat-icon class="menu-button" [ngClass]="{'rotated' : isSubMenuHealth}" *ngIf="!shouldCollapseNavbar">expand_more</mat-icon>
  </span>
</a>

<mat-divider *ngIf="isSubMenuHealth"></mat-divider>

        <!-- pharmacies -->
        <div class="nested-single-menus" *ngIf="isSubMenuHealth">
          <a mat-list-item routerLink="health/pharmacies" [routerLinkActive]="['is-active']">
            <span class="submenu-selection-item" [ngClass]="{'justify-content-center': shouldCollapseNavbar}">
              <mat-icon>local_pharmacy</mat-icon>
              <span class="text" *ngIf="!shouldCollapseNavbar">{{ "pharmacies" | translate }}</span>
            </span>
          </a>
        </div>

<mat-divider *ngIf="isSubMenuHealth"></mat-divider>

        <!-- medicalEstablishments -->
        <div class="nested-single-menus" *ngIf="isSubMenuHealth">
          <a mat-list-item routerLink="health/medical-establishments" [routerLinkActive]="['is-active']">
            <span class="submenu-selection-item" [ngClass]="{'justify-content-center': shouldCollapseNavbar}">
              <mat-icon>local_hospital</mat-icon>
              <span class="text" *ngIf="!shouldCollapseNavbar">{{ "medicalEstablishments" | translate }}</span>
            </span>
          </a>
        </div>

<mat-divider *ngIf="isSubMenuHealth"></mat-divider>

        <!-- doctorsOffices -->
        <div class="nested-single-menus" *ngIf="isSubMenuHealth">
          <a mat-list-item routerLink="health/doctors-offices" [routerLinkActive]="['is-active']">
            <span class="submenu-selection-item" [ngClass]="{'justify-content-center': shouldCollapseNavbar}">
              <mat-icon>healing</mat-icon>
              <span class="text" *ngIf="!shouldCollapseNavbar">{{ "doctorsOffices" | translate }}</span>
            </span>
          </a>
        </div>

  <mat-divider *ngIf="isSubMenuHealth"></mat-divider>

        <!-- medicalLabs -->
        <div class="nested-single-menus" *ngIf="isSubMenuHealth">
          <a mat-list-item routerLink="health/medical-labs" [routerLinkActive]="['is-active']">
            <span class="submenu-selection-item" [ngClass]="{'justify-content-center': shouldCollapseNavbar}">
              <mat-icon>science</mat-icon>
              <span class="text" *ngIf="!shouldCollapseNavbar">{{ "medicalLabs" | translate }}</span>
            </span>
          </a>
        </div>

  <mat-divider *ngIf="isSubMenuHealth"></mat-divider>

        <!-- veterinaries -->
        <div class="nested-single-menus" *ngIf="isSubMenuHealth">
          <a mat-list-item routerLink="health/veterinaries" [routerLinkActive]="['is-active']">
            <span class="submenu-selection-item" [ngClass]="{'justify-content-center': shouldCollapseNavbar}">
              <mat-icon>pets</mat-icon>
              <span class="text" *ngIf="!shouldCollapseNavbar">{{ "veterinaries" | translate }}</span>
            </span>
          </a>
        </div>

<mat-divider></mat-divider>

        <!-- Dropdown for Events -->
        <a mat-list-item (click)="isSubMenuEvents = !isSubMenuEvents;">
            <span class="expandable-menu-selection-item" [ngClass]="{'justify-content-center': shouldCollapseNavbar}">
              <mat-icon class="events-menu-icon">people</mat-icon>
              <span class="text" *ngIf="!shouldCollapseNavbar">{{ "events" | translate }}</span>
              <mat-icon class="menu-button" [ngClass]="{'rotated' : isSubMenuEvents}" *ngIf="!shouldCollapseNavbar">expand_more</mat-icon>
            </span>
        </a>

        <mat-divider *ngIf="isSubMenuEvents"></mat-divider>

        <!-- Culture -->
        <div class="events-submenu events-sub-menu" *ngIf="isSubMenuEvents">
          <a mat-list-item routerLink="/events/culture-events" [routerLinkActive]="['is-active']">
            <span class="submenu-selection-item" [ngClass]="{'justify-content-center': shouldCollapseNavbar}">
              <mat-icon>theater_comedy</mat-icon>
              <span class="text" *ngIf="!shouldCollapseNavbar">{{ "cultureEvents" | translate }}</span>
            </span>
          </a>
        </div>

        <mat-divider *ngIf="isSubMenuEvents"></mat-divider>

        <!-- Sport -->
        <div class="events-submenu events-sub-menu" *ngIf="isSubMenuEvents">
          <a mat-list-item routerLink="/events/sport-events" [routerLinkActive]="['is-active']">
            <span class="submenu-selection-item" [ngClass]="{'justify-content-center': shouldCollapseNavbar}">
              <mat-icon>sports_basketballt</mat-icon>
              <span class="text" *ngIf="!shouldCollapseNavbar">{{ "sportEvents" | translate }}</span>
            </span>
          </a>
        </div>

        <mat-divider *ngIf="isSubMenuEvents"></mat-divider>

        <!-- celebrations -->
        <div class="events-submenu events-sub-menu" *ngIf="isSubMenuEvents">
          <a mat-list-item routerLink="/events/celebrations" [routerLinkActive]="['is-active']">
            <span class="submenu-selection-item" [ngClass]="{'justify-content-center': shouldCollapseNavbar}">
              <mat-icon>event</mat-icon>
              <span class="text" *ngIf="!shouldCollapseNavbar">{{ "celebrations" | translate }}</span>
            </span>
          </a>
        </div>


      <mat-divider></mat-divider>

      <!-- Dropdown for Sport -->
      <a mat-list-item (click)="isSubMenuSport = !isSubMenuSport;">
        <span class="expandable-menu-selection-item" [ngClass]="{'justify-content-center': shouldCollapseNavbar}">
          <mat-icon class="events-menu-icon">sports_soccer</mat-icon>
          <span class="text" *ngIf="!shouldCollapseNavbar">{{ "sport" | translate }}</span>
          <mat-icon class="menu-button" [ngClass]="{'rotated' : isSubMenuSport}" *ngIf="!shouldCollapseNavbar">expand_more</mat-icon>
        </span>
    </a>

    <mat-divider *ngIf="isSubMenuSport"></mat-divider>

        <!-- sportClubs -->
        <div class="events-submenu events-sub-menu" *ngIf="isSubMenuSport">
          <a mat-list-item routerLink="/sport/sport-clubs" [routerLinkActive]="['is-active']">
            <span class="submenu-selection-item" [ngClass]="{'justify-content-center': shouldCollapseNavbar}">
              <mat-icon>fitness_center</mat-icon>
              <span class="text" *ngIf="!shouldCollapseNavbar">{{ "sportClubs" | translate }}</span>
            </span>
          </a>
        </div>

    <mat-divider *ngIf="isSubMenuSport"></mat-divider>

        <!-- sportFacilities -->
        <div class="events-submenu events-sub-menu" *ngIf="isSubMenuSport">  
          <a mat-list-item routerLink="/sport/sport-facilities" [routerLinkActive]="['is-active']">
            <span class="submenu-selection-item" [ngClass]="{'justify-content-center': shouldCollapseNavbar}">
              <mat-icon>stadium</mat-icon>
              <span class="text" *ngIf="!shouldCollapseNavbar">{{ "sportFacilities" | translate }}</span>
            </span>
          </a>
        </div>

      <mat-divider></mat-divider>

        <!-- repair -->
        <a mat-list-item routerLink="/repair" [routerLinkActive]="['is-active']">
          <span class="menu-selection-item" [ngClass]="{'justify-content-center': shouldCollapseNavbar}">
            <mat-icon>construction</mat-icon>
            <span class="text" *ngIf="!shouldCollapseNavbar">{{ "repair" | translate }}</span>
          </span>
        </a>

    <mat-divider></mat-divider>


    <!-- Parking -->
    <a mat-list-item routerLink="/parking" [routerLinkActive]="['is-active']">
        <span class="menu-selection-item" [ngClass]="{'justify-content-center': shouldCollapseNavbar}">
          <mat-icon>directions_car</mat-icon>
          <span class="text" *ngIf="!shouldCollapseNavbar">{{ "parking" | translate }}</span>
        </span>
      </a>

      <mat-divider></mat-divider>


    <!-- Reports -->
    <a mat-list-item routerLink="/report" [routerLinkActive]="['is-active']">
        <span class="menu-selection-item" [ngClass]="{'justify-content-center': shouldCollapseNavbar}">
          <mat-icon>report</mat-icon>
          <span class="text" *ngIf="!shouldCollapseNavbar">{{ "reports" | translate }}</span>
        </span>
      </a>

  <mat-divider></mat-divider>
  

    <!-- Cameras -->
    <a mat-list-item routerLink="/cameras" [routerLinkActive]="['is-active']">
      <span class="menu-selection-item" [ngClass]="{'justify-content-center': shouldCollapseNavbar}">
        <mat-icon>photo_camera</mat-icon>
        <span class="text" *ngIf="!shouldCollapseNavbar">{{ "cameras" | translate }}</span>
      </span>
    </a>

  <mat-divider></mat-divider>

    <!-- polls -->
    <a mat-list-item routerLink="/polls" [routerLinkActive]="['is-active']">
      <span class="menu-selection-item" [ngClass]="{'justify-content-center': shouldCollapseNavbar}">
        <mat-icon>poll</mat-icon>
        <span class="text" *ngIf="!shouldCollapseNavbar">{{ "polls" | translate }}</span>
      </span>
    </a>

  <mat-divider></mat-divider>

    <!-- Users -->
    <a mat-list-item routerLink="/users" [routerLinkActive]="['is-active']" *ngIf="role !== 'UserMunicipality'">
      <span class="menu-selection-item" [ngClass]="{'justify-content-center': shouldCollapseNavbar}">
        <mat-icon>person</mat-icon>
        <span class="text" *ngIf="!shouldCollapseNavbar">{{ "users" | translate }}</span>
      </span>
    </a>

  <mat-divider></mat-divider>

    <!-- platform-variables -->
    <a mat-list-item routerLink="/platform-variables" [routerLinkActive]="['is-active']" *ngIf="role !== 'UserMunicipality'">
      <span class="menu-selection-item" [ngClass]="{'justify-content-center': shouldCollapseNavbar}">
        <mat-icon>developer_mode</mat-icon>
        <span class="text" *ngIf="!shouldCollapseNavbar">{{ "variables" | translate }}</span>
      </span>
    </a>

    <mat-divider></mat-divider>

    <!-- Announcements -->
    <a mat-list-item routerLink="/announcements" [routerLinkActive]="['is-active']">
      <span class="menu-selection-item" [ngClass]="{'justify-content-center': shouldCollapseNavbar}">
        <mat-icon>messages</mat-icon>
        <span class="text" *ngIf="!shouldCollapseNavbar">{{ "announcements" | translate }}</span>
      </span>
    </a>

  <mat-divider *ngIf="role !== 'UserMunicipality'"></mat-divider>

        </mat-nav-list>
      </mat-sidenav>

    <mat-sidenav-content class="content" [ngClass]="{'content-collapsed': shouldCollapseNavbar}">
      <router-outlet></router-outlet>
    </mat-sidenav-content>
  </mat-sidenav-container>