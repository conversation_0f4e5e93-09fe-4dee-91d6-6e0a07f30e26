<form [formGroup]="legendsAndMythsFilterForm">
  <div class="wrapper">
    <div class="header">
        <div class="filter-container">

        <!-- search-->
        <mat-form-field appearance="outline" class="mat-form-field">
          <mat-label>{{'SearchFilter' | translate}}</mat-label>
          <input matInput formControlName="search">
      </mat-form-field>

       <!-- Minimalistic Checkbox with Tooltip for isVerified -->
      <mat-checkbox formControlName="isVerified" matTooltip="{{ 'showOnlyVerified' | translate }}">
        <span class="checkbox-label">{{"verified" | translate}}</span>
      </mat-checkbox>


            <button mat-icon-button type="submit" class="search-button" (click)="onSearch()">
                <mat-icon>search</mat-icon>
            </button>
        </div>
    </div>
    <mat-divider></mat-divider>

    <div class="main-content">
        <div class="title">
            <span class="left-big-border"></span>
            <h1 class="h1">{{ 'legendsAndMyths' | translate }}</h1>
        </div>

        <div class="add-button-container">
            <button mat-raised-button tabindex="1" class="add-button" (click)="openAddDialog()">
                <mat-icon>add</mat-icon>
                <span>{{ 'AddLegendsAndMyths' | translate }}</span>
            </button>

            <!-- Import Button -->
            <button mat-raised-button class="import-button" 
                    (click)="openImportDialog()" 
                    [disabled]="isImporting"
                    matTooltip="{{ 'ImportItems' | translate }}">
                <mat-icon>{{ isImporting ? 'hourglass_empty' : 'file_upload' }}</mat-icon>
                <span>{{ isImporting ? ('Importing' | translate) : ('Import' | translate) }}</span>
            </button>

            <!-- Hidden File Input -->
            <input #fileInput type="file" accept=".xlsx,.xls" style="display: none" (change)="onFileSelected($event)">
        </div>
    </div>

    <div class="card-column">
      <mat-card class="custom-card table-container">
          <div class="mat-elevation-z8 custom-table">
            <table mat-table [dataSource]="dataSource" matSort (matSortChange)="onSortChange($event)">

              <!-- ID Column -->
              <ng-container matColumnDef="id">
                <th mat-header-cell *matHeaderCellDef mat-sort-header class="custom-header"> ID </th>
                <td mat-cell *matCellDef="let element" class="custom-cell"> {{element.id}} </td>
              </ng-container>

              <!-- name Column -->
              <ng-container matColumnDef="name">
                <th mat-header-cell *matHeaderCellDef mat-sort-header class="custom-header"> {{'name' | translate}} </th>
                <td mat-cell *matCellDef="let element" class="custom-cell"> {{element.name | translate}} </td>
              </ng-container>

              <!-- latestVerification Column -->
              <ng-container matColumnDef="latestVerification">
                <th mat-header-cell *matHeaderCellDef mat-sort-header class="custom-header"> {{'latestVerification' | translate}} </th>
                <td mat-cell *matCellDef="let element" class="custom-cell" matTooltip="{{element.latestVerification}}"> {{element.latestVerification | date: 'dd/MM/yyyy'}} </td>
              </ng-container>

              <!-- verifiedBy Column -->
              <ng-container matColumnDef="verifiedBy">
                <th mat-header-cell *matHeaderCellDef mat-sort-header class="custom-header"> {{'verifiedBy' | translate}} </th>
                <td mat-cell *matCellDef="let element" class="custom-cell" matTooltip="{{element.verifiedBy}}"> {{element.verifiedBy}} </td>
              </ng-container>

              <!-- phoneNumber Column -->
              <ng-container matColumnDef="phoneNumber">
                <th mat-header-cell *matHeaderCellDef mat-sort-header class="custom-header"> {{'phoneNumber' | translate}} </th>
                <td mat-cell *matCellDef="let element" class="custom-cell" matTooltip="{{element.phoneNumber}}"> {{element.phoneNumber}} </td>
              </ng-container>

              <!-- website Column -->
              <ng-container matColumnDef="website">
                <th mat-header-cell *matHeaderCellDef mat-sort-header class="custom-header"> {{'website' | translate}} </th>
                <td mat-cell *matCellDef="let element" class="custom-cell truncate" matTooltip="{{element.website}}"> {{element.website}} </td>
              </ng-container>

              <!-- latitude Column -->
              <ng-container matColumnDef="latitude">
                <th mat-header-cell *matHeaderCellDef mat-sort-header class="custom-header"> {{'latitude' | translate}} </th>
                <td mat-cell *matCellDef="let element" class="custom-cell truncate" matTooltip="{{element.latitude}}"> {{element.latitude}} </td>
              </ng-container>

              <!-- longitude Column -->
              <ng-container matColumnDef="longitude">
                <th mat-header-cell *matHeaderCellDef mat-sort-header class="custom-header"> {{'longitude' | translate}} </th>
                <td mat-cell *matCellDef="let element" class="custom-cell truncate" matTooltip="{{element.longitude}}"> {{element.longitude}} </td>
              </ng-container>
            
              <!-- Actions Column -->
              <ng-container matColumnDef="actions">
                <th mat-header-cell *matHeaderCellDef class="custom-header actions-header"> {{'actions' | translate}} </th>
                <td mat-cell *matCellDef="let element" class="custom-cell">
                  <button mat-icon-button [matMenuTriggerFor]="menu">
                    <mat-icon>more_horizontal</mat-icon>
                  </button>
                  <mat-menu #menu="matMenu">
                    <div class="menu-actions">
                      <button mat-menu-item (click)="openPreview(element)" class="preview-button">
                        <mat-icon>phone_iphone</mat-icon>
                      </button>
                      <button mat-menu-item (click)="viewDetails(element)" class="details-button">
                        <mat-icon>info</mat-icon>
                      </button>
                      <button mat-menu-item (click)="editElement(element)" class="edit-button">
                        <mat-icon>edit</mat-icon>
                      </button>
                      <button mat-menu-item (click)="verify(element)" class="verify-button">
                        <mat-icon>verified</mat-icon>
                      </button>
                      <button mat-menu-item (click)="deleteElement(element)" class="delete-button">
                        <mat-icon>delete</mat-icon>
                      </button>
                    </div>
                  </mat-menu>
                </td>
              </ng-container>
            
              <tr mat-header-row *matHeaderRowDef="displayedColumns; sticky: true" class="custom-row"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedColumns;" class="custom-row" (click)="onRowClick($event, row)" 
              (dblclick)="onRowDoubleClick($event, row)"></tr>
            
            </table>


          </div>
      </mat-card>
      <div class="mat-paginator-sticky table-footer">
        <div class="buttons table-footer-left">
         
         </div>
         <div>
          <mat-paginator 
            [length]="totalCount"
            [pageSize]="paginationSort.pageSize"
            [pageSizeOptions]="[25, 50, 100]"
            showFirstLastButtons
            (page)="onPageChange($event)"
            aria-label="Select page of periodic elements">
          </mat-paginator>
         </div>
      </div>
    </div>

  </div>
</form>