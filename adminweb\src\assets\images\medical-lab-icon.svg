<svg width="28" height="36" viewBox="0 0 28 36" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_f_738_3711)">
<ellipse cx="12" cy="29" rx="4" ry="2" fill="black" fill-opacity="0.12"/>
</g>
<mask id="path-2-outside-1_738_3711" maskUnits="userSpaceOnUse" x="1.33301" y="1.25" width="26" height="32" fill="black">
<rect fill="white" x="1.33301" y="1.25" width="26" height="32"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M20.9016 22.5711C23.791 20.5234 25.6663 17.2229 25.6663 13.5C25.6663 7.2868 20.443 2.25 13.9997 2.25C7.55635 2.25 2.33301 7.2868 2.33301 13.5C2.33301 17.2229 4.20832 20.5234 7.09774 22.5711C9.28849 24.1629 12.3745 26.672 13.0951 30.7071C13.1745 31.1518 13.5479 31.4966 13.9997 31.4966C14.4514 31.4966 14.8249 31.1518 14.9043 30.7071C15.6248 26.6719 18.7109 24.1629 20.9016 22.5711Z"/>
</mask>
<path fill-rule="evenodd" clip-rule="evenodd" d="M20.9016 22.5711C23.791 20.5234 25.6663 17.2229 25.6663 13.5C25.6663 7.2868 20.443 2.25 13.9997 2.25C7.55635 2.25 2.33301 7.2868 2.33301 13.5C2.33301 17.2229 4.20832 20.5234 7.09774 22.5711C9.28849 24.1629 12.3745 26.672 13.0951 30.7071C13.1745 31.1518 13.5479 31.4966 13.9997 31.4966C14.4514 31.4966 14.8249 31.1518 14.9043 30.7071C15.6248 26.6719 18.7109 24.1629 20.9016 22.5711Z" fill="#00BFD4"/>
<path d="M20.9016 22.5711L20.3234 21.7551L20.3138 21.7621L20.9016 22.5711ZM7.09774 22.5711L7.6856 21.762L7.67595 21.7552L7.09774 22.5711ZM13.0951 30.7071L12.1106 30.8829L12.1106 30.8829L13.0951 30.7071ZM14.9043 30.7071L15.8887 30.8829L15.8887 30.8829L14.9043 30.7071ZM21.4799 23.3869C24.6146 21.1654 26.6663 17.57 26.6663 13.5H24.6663C24.6663 16.8757 22.9675 19.8814 20.3234 21.7552L21.4799 23.3869ZM26.6663 13.5C26.6663 6.70055 20.9607 1.25 13.9997 1.25V3.25C19.9253 3.25 24.6663 7.87305 24.6663 13.5H26.6663ZM13.9997 1.25C7.03866 1.25 1.33301 6.70055 1.33301 13.5H3.33301C3.33301 7.87305 8.07405 3.25 13.9997 3.25V1.25ZM1.33301 13.5C1.33301 17.57 3.38476 21.1654 6.51953 23.387L7.67595 21.7552C5.03189 19.8814 3.33301 16.8758 3.33301 13.5H1.33301ZM14.0795 30.5313C13.2833 26.0726 9.88416 23.3596 7.68556 21.7621L6.50992 23.3801C8.69282 24.9662 11.4657 27.2714 12.1106 30.8829L14.0795 30.5313ZM13.9997 30.4966C14.0152 30.4966 14.0312 30.4997 14.0451 30.5052C14.0584 30.5105 14.0669 30.5166 14.0715 30.5207C14.076 30.5246 14.0778 30.5275 14.0784 30.5287C14.0792 30.53 14.0794 30.5309 14.0795 30.5313L12.1106 30.8829C12.2663 31.7544 13.0143 32.4966 13.9997 32.4966V30.4966ZM13.9199 30.5313C13.9199 30.5309 13.9202 30.53 13.9209 30.5287C13.9216 30.5275 13.9234 30.5246 13.9278 30.5207C13.9325 30.5166 13.9409 30.5105 13.9542 30.5052C13.9682 30.4997 13.9842 30.4966 13.9997 30.4966V32.4966C14.9851 32.4966 15.7331 31.7544 15.8887 30.8829L13.9199 30.5313ZM20.3138 21.7621C18.1152 23.3596 14.716 26.0726 13.9199 30.5313L15.8887 30.8829C16.5336 27.2713 19.3066 24.9662 21.4895 23.38L20.3138 21.7621Z" fill="url(#paint0_linear_738_3711)" mask="url(#path-2-outside-1_738_3711)"/>
<g clip-path="url(#clip0_738_3711)">
<path d="M20.4952 18.3L16.02 11.2306V7.85125H17.0597V6H10.7116V7.85312H11.7598V11.2325L7.27612 18.3C7.10847 18.5633 7.01324 18.8681 7.00049 19.1824C6.98775 19.4967 7.05797 19.8086 7.20373 20.0853C7.34949 20.3619 7.56536 20.593 7.82851 20.754C8.09166 20.915 8.39231 21 8.69864 21H19.0672C19.3741 20.9995 19.6751 20.9139 19.9386 20.7523C20.202 20.5907 20.4181 20.3591 20.564 20.0819C20.7099 19.8046 20.7801 19.4921 20.7674 19.1773C20.7546 18.8624 20.6593 18.557 20.4915 18.2931L20.4958 18.3H20.4952ZM19.9443 19.7388C19.8592 19.9002 19.7331 20.0351 19.5794 20.129C19.4258 20.223 19.2503 20.2726 19.0714 20.2725H8.69864C8.51906 20.2728 8.34276 20.2231 8.18842 20.1289C8.03408 20.0346 7.90746 19.8992 7.82197 19.7371C7.73649 19.5749 7.69533 19.392 7.70286 19.2078C7.71038 19.0236 7.76632 18.8449 7.86473 18.6906L7.8623 18.6944L12.4573 11.4444V7.85H15.3097V11.4462L19.8999 18.6962C20.0033 18.8531 20.0654 19.0469 20.0654 19.2556C20.0654 19.4337 20.0204 19.6012 19.9413 19.7462L19.9437 19.7413L19.9443 19.7388Z" fill="white"/>
<path d="M15.7729 13.7194H12.0045L8.56228 19.1556C8.54464 19.1832 8.53463 19.2151 8.53335 19.248C8.53206 19.2809 8.53954 19.3136 8.55498 19.3425C8.58542 19.3969 8.64081 19.4325 8.70533 19.4331H19.0726C19.1033 19.4333 19.1335 19.425 19.1599 19.409C19.1864 19.3931 19.2082 19.3701 19.223 19.3425C19.2389 19.3138 19.2472 19.2805 19.2458 19.2476C19.2444 19.2146 19.234 19.1827 19.2157 19.1556L15.7729 13.7194Z" fill="white"/>
</g>
<defs>
<filter id="filter0_f_738_3711" x="6" y="25" width="12" height="8" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="1" result="effect1_foregroundBlur_738_3711"/>
</filter>
<linearGradient id="paint0_linear_738_3711" x1="13.9997" y1="2.25" x2="13.9997" y2="31.4966" gradientUnits="userSpaceOnUse">
<stop stop-color="#626262"/>
<stop offset="1" stop-color="#626262"/>
</linearGradient>
<clipPath id="clip0_738_3711">
<rect width="14" height="15" fill="white" transform="translate(7 6)"/>
</clipPath>
</defs>
</svg>
