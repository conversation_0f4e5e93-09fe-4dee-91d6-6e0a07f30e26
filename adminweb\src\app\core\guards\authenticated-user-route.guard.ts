import { inject } from '@angular/core';
import { CanActivateFn, Router } from '@angular/router';
import { AuthService } from '../services/auth.service';

export const authenticatedUserRouteGuard: CanActivateFn = (route, state) => {
  const authService = inject(AuthService);
  const router = inject(Router);

  // Check if the user is authenticated
  if (authService.isUserAuthenticated()) {
    const requiredRoles = route.data['roles']; // Change to 'roles' to match the route data structure

    // If roles are specified, check if the user has any of those roles
    if (requiredRoles) {
      const userRole = authService.getRole(); // Ensure you have this method in AuthService
      if (requiredRoles.includes(userRole)) { // Check if the user role is in the required roles array
        return true; // User is authenticated and has one of the required roles
      } else {
        router.navigate(['/unauthorized']); // Redirect to an unauthorized page if role does not match
        return false; // Deny access
      }
    }

    return true; // User is authenticated, no role required
  } else {
    router.navigate(['/login']); // Redirect to login if not authenticated
    return false; // Deny access
  }
};