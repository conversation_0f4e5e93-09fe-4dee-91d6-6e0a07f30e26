import { CommonModule } from '@angular/common';
import { Component, ElementRef, Inject, OnInit, ViewChild } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';


@Component({
  selector: 'app-details-repair',
  standalone: true,
  imports: [
  TranslateModule,
  MatIconModule,
  MatDialogModule,
  CommonModule
  ],
  templateUrl: './details-repair.component.html',
  styleUrl: './details-repair.component.css'
})
export class DetailsRepairComponent implements OnInit {
  @ViewChild('slider') slider!: ElementRef;
    
      currentSlide = 0;
    
      constructor(
        public dialogRef: MatDialogRef<DetailsRepairComponent>,
        @Inject(MAT_DIALOG_DATA) public data: any) {}


        cleanedDescription: string = '';

        ngOnInit(): void {
          this.cleanedDescription = this.cleanContent(this.data.data.description);
        }
    
    
      // Slider navigation methods
      nextSlide(): void {
        if (this.currentSlide < this.data.data.images.length - 1) {
          this.currentSlide++;
        } else {
          this.currentSlide = 0; // Loop back to the first slide
        }
        this.updateSliderPosition();
      }
    
      prevSlide(): void {
        if (this.currentSlide > 0) {
          this.currentSlide--;
        } else {
          this.currentSlide = this.data.data.images.length - 1; // Loop to the last slide
        }
        this.updateSliderPosition();
      }
    
      goToSlide(index: number): void {
        this.currentSlide = index;
        this.updateSliderPosition();
      }
    
      updateSliderPosition(): void {
        if (this.slider && this.slider.nativeElement) {
          const translateX = -this.currentSlide * 100;
          this.slider.nativeElement.style.transform = `translateX(${translateX}%)`;
        }
      }

      cleanContent(content: string): string {
        console.log(content);
      if (!content) return ''; // Ensure it's not undefined/null
    
      // Remove empty paragraphs, line breaks, and spaces
      content = content.replace(/<(p|br|div|span)>\s*<\/\1>/g, '');
    
      return content.trim(); // Trim remaining spaces
    }
    
      onClose(): void {
        this.dialogRef.close();
      }
}
