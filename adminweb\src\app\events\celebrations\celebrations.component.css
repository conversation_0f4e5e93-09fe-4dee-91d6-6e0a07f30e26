.wrapper {
  margin: 10px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  height: 90vh;
  max-height: 90vh;
  overflow-y: auto;
  overflow: hidden;
  background-color: #FFF;
}

.header {
  width: 100%;
  padding: 20px 15px 20px 10px;
}

.export-button{
  box-shadow: 0px 0px 0px 0px #00000040;
  color: #CCCCCC;
  width: 108px;
  height: 21.82;
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;
}

.truncate {
  white-space: nowrap; /* Prevents text from wrapping to the next line */
  overflow: hidden; /* Hides the overflowing text */
  text-overflow: ellipsis; /* Displays an ellipsis (...) for overflowed text */
  max-width: 150px; /* Sets a maximum width for the cell (adjust as needed) */
}

.actions-header {
  padding-right: 10px !important; /* Adds padding to the right */
}

.overdue-row {
  background-color: #f8d7da; /* Light red */
  color: #721c24; /* Dark red for text */
}

.bottom{
  display: flex;
  flex-wrap: nowrap;
  justify-content: space-around;
  align-items: flex-end;
}

.buttons{
margin-right: 400px;
display: flex;
}

.export-button span{
font-size: 16px;
}

.export-button mat-icon{
width: 24px;
height: 24px;
}

.filter-container {
  align-items: center;
  gap: 10px;
}

.wide-dropdown {
  width: 220px;
  margin-right: 10px;
}

.mat-form-field{
 width: 220px;
 margin-right: 10px;
}

.date{
margin-right: 10px;
width: 155px;
}

.long-fields{
margin-right: 10px;
width: 296px;
}

.left-big-border{
height: 33px;
}

.h1{
font-weight: bold;
}

.date-cell{
  padding-left: 20px !important;
  }

  .add-button-container{
    padding-top: 6px;
    }

      .add-button {
        width: 222px;
        height: 44px;
        cursor: pointer;
        border: none;
        border-radius: 5px;
        margin-right: 5px;
        color: white !important;
        background-color: #446B43 !important;
      }

      .card-column {
        display: flex;
        justify-content: center;
        flex-direction: column;
        box-shadow: 0px 3px 3px -2px rgba(0, 0, 0, 0.2), 0px 3px 4px 0px rgba(0, 0, 0, 0.14), 0px 1px 8px 0px rgba(0, 0, 0, 0.12);
      }
    
      .custom-card {
        width: 100%; /* Adjust the width as necessary */
        border-radius: 5px;
        border: 1px solid #ddd; /* Border around the table */
      }
    
      .custom-table {
        height: 100%;
        width: 133%;
      }
    
      .custom-header {
        padding-left: 30px !important;
        font-weight: bold;
        background-color: #F1F1F1 !important;
        color: #333;
        border-bottom: 2px solid #000;
      }
    
      .custom-cell {
      padding-left: 20px !important;
      }
    
      .custom-row {
        border-bottom: 1px solid #ccc;
      }
    
      .custom-row:hover {
        background-color: #f1f1f1;
        cursor: pointer;
      }
    
      .mat-elevation-z8 {
        box-shadow: 0px 3px 3px -2px rgba(0, 0, 0, 0.2), 0px 3px 4px 0px rgba(0, 0, 0, 0.14), 0px 1px 8px 0px rgba(0, 0, 0, 0.12);
        /* border-radius: 11px; */
      }

      .add-button-container{
      padding-top: 6px;
      }

      .add-button {
        width: 235px;
        height: 44px;
        cursor: pointer;
        border: none;
        border-radius: 5px;
        color: white !important;
        background-color: #446B43 !important;
      }

      .settings-button {
        margin-left: 10px;
        top: 4px;
        background-color: #383838;
        color: white;
        width: 44px;
        height: 44px;
        border-radius: 4px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 0; /* Remove any default padding */
      }
      
      .wide-dropdown .mat-input-element {
       height: 100px !important;
      }

      .mat-input-element {
        height: 28px; /* Set the height of the input */
        font-size: 14px; /* Adjust font size as needed */
        padding: 4px 8px; /* Adjust padding to make the input smaller */
        box-sizing: border-box; /* Ensure padding is included in the total height */
      }

      .mat-mdc-text-field-wrapper {
        height: 0px;
        flex: auto;
        will-change: auto;
      }

      .search-button {
        top: 6px;
        position: relative;
        background-color: #383838;
        color: white;
        width: 80px;
        height: 44px;
        border-radius: 4px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
      }

      .main-content {
        display: flex;
        padding: 10px 20px;
        justify-content: space-between;
      }

      .title{
      padding-top: 10px;
      display: flex;
      gap: 10px;
      }

      .info-line {
        display: flex;
      }

      .value-text {
        font-weight: bold;
      }

      .demo-chart {
        height: 100%;
      }

      .mat-paginator-sticky {
        bottom: 0px;
        position: sticky;
        z-index: 10;
      }
      .table-container {
         max-height: 50vh; /* Adjust the height as needed */
         overflow-y: auto;
         overflow-x: auto;
         border: Mixed solid #383838
      }
      /*Make the table design as simple as possible by removing unnecessary decorations
      or keeping them only for specific elements*/
      .mat-mdc-header-cell {
        border-bottom: none;
      }

      .mat-mdc-cell {
        border-bottom: none;
      }

      .mdc-data-table__cell,
      .mdc-data-table__header-cell {
        padding: 0px;
      }

      .mat-mdc-header-cell.border-top-bottom {
        border-bottom:1px solid #CCCCCC;
        border-top:1px solid #CCCCCC;
      }

      /*****************************/

      /*Set the size/height of all rows in the table. The smaller it is - the smaller the gap between each row*/
      mat-row,
      mat-footer-row {
        min-height: 28px;
      }

      .mat-mdc-table mat-header-row.mat-mdc-header-row,
      .mat-mdc-table mat-row.mat-mdc-row,
      .mat-mdc-table mat-footer-row.mat-mdc-footer-cell {
        height: 28px;
      }

      /*****************************/

      .button-more {
        width: 135px;
        text-align: center;
        background-color: #383838;
        color: white;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
      }

      .center-cell{
        display: flex;
        justify-content: center;
      }

      .end-cell{
        display: flex;
        justify-content: flex-end;
      }

      /* mat-form-field mat-label label{
        position: relative;
        top: -10px !important;
      } */

      /* :host ::ng-deep .mdc-floating-label {
        position: relative;
        top: 20px !important;
      } */

      :host ::ng-deep .mat-mdc-text-field-wrapper{
        height: 44px;
      }

      ::ng-deep .mat-form-field-label {
        transform: translateY(-5px); /* Настройте стойността според нуждите */
      }
      :host ::ng-deep .mdc-text-field--outlined .mat-mdc-form-field-infix, .mdc-text-field--no-label .mat-mdc-form-field-infix{
      padding-top: 12px;
      }

      .export-import-button{
        box-shadow: 0px 0px 0px 0px #00000040;
        color: #CCCCCC;
        width: 108px;
        height: 21.82;
        display: flex;
        flex-direction: column;
        flex-wrap: wrap;
      }

      .export-import-button span{
        font-size: 16px;
        }

      .export-import-button mat-icon{
        width: 24px;
        height: 24px;
      }

      .table-footer{
        display: flex;
        justify-content: space-between;
        background-color: white;
        height: 60px;
        border-top: 1px solid #ccc;
        padding: 0 20px;
      }

      .table-footer-left{
        background-color: white;
        display: flex;
        align-items: center;
      }

      .menu-actions{
        display: flex;
        padding: 0 10px;
      }

      .menu-actions button{
        width: 50px;
      }

      .details-button mat-icon{
        color: #366ECA;
      }

      .edit-button mat-icon{
        color: #298341;
      }

      .delete-button mat-icon{
        color: red;;
      }


@media screen and (max-width: 768px) {
  .wrapper {
    overflow-y: auto;
  }

  .main-content {
    flex-direction: column;
  }

  .wide-dropdown, .mat-form-field, .date, .long-fields {
      width: 100%; /* Make fields take full width on smaller screens */
      margin-right: 0; /* Remove right margin on smaller screens */
    }

}
