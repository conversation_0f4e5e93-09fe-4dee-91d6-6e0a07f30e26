import { Component, Inject, OnInit, ViewEncapsulation } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatSnackBarRef, MAT_SNACK_BAR_DATA, } from '@angular/material/snack-bar';
import { MatIcon } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';

@Component({
  selector: 'app-snack-bar',
  standalone: true,
  imports: [TranslateModule, CommonModule],
  templateUrl: './snack-bar.component.html',
  styleUrl: './snack-bar.component.css',
  encapsulation: ViewEncapsulation.None
})
export class SnackBarComponent implements OnInit {
  

  constructor(
    @Inject(MAT_SNACK_BAR_DATA) public data: any,
    public snackBarRef: MatSnackBarRef<SnackBarComponent>,) { }

    closeSnackbar(): void {
      this.snackBarRef.dismiss();
    }

  ngOnInit(): void {
    
  }

}
