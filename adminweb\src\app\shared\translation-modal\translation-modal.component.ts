import { Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { TranslationService } from '../../core/services/translation.service';
import { CommonModule } from '@angular/common';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { FormsModule } from '@angular/forms';
import { EditorComponent } from '../editor/editor.component';
import { MatCardModule } from '@angular/material/card';
import { MatInputModule } from '@angular/material/input';
import { MessageService } from '../../core/services/message.service';

@Component({
  selector: 'app-translation-modal',
  standalone: true,
  imports: [
    CommonModule,
    MatFormFieldModule,
    MatSelectModule,
    MatButtonModule,
    MatIconModule,
    TranslateModule,
    FormsModule,
    EditorComponent,
    MatCardModule,
    MatInputModule,
    MatDialogModule
  ],
  templateUrl: './translation-modal.component.html',
  styleUrl: './translation-modal.component.css'
})
export class TranslationModalComponent implements OnInit {
  protected selectedLanguage: string = ''; // Default language
  protected translatedHeading: string = '';
  protected translatedContent: string = '';

  protected content = '';

  // Define a mapping from language codes to full names
  private languageNames: { [key: string]: string } = {
    bg: 'Bulgarian',
    es: 'Spanish',
    en: 'English',
    de: 'German',
    it: 'Italian',
    tr: 'Turkish',
    el: 'Greek',
    sr: 'Serbian',
    ro: 'Romanian',
    fr: 'French',
    uk: 'Ukrainian'
  }

  constructor(
    private dialogRef: MatDialogRef<TranslationModalComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { heading: string; content: string; selectedLanguage: string; },
    private translationService: TranslationService,
    private messageService: MessageService
  ) {}

  ngOnInit(): void {
    this.translatedHeading = this.cleanText(this.data.heading);
    this.translatedContent = this.cleanText(this.data.content);
    this.content = this.data.content;

    if(this.data.selectedLanguage) {
     this.selectedLanguage = this.data.selectedLanguage;
    }

  }


  translateText() {
    // Check cleaned content without modifying the original
      const cleanedContent = this.cleanContent(this.translatedContent);

      if (this.translatedHeading.trim() === '' || cleanedContent === '') {
        this.messageService.showMessage(['HeadingAndContentCannotBeEmpty'], 'error');
        return;
      }


    if (this.translatedHeading) {
      this.translationService.translate(this.translatedHeading, this.selectedLanguage).subscribe((res: any) => {
        this.translatedHeading = this.cleanText(res.data.translations[0].translatedText);
      });
    }
    
    if (this.translatedContent) {
      this.translationService.translate(this.translatedContent, this.selectedLanguage).subscribe((res: any) => {
        this.translatedContent = this.cleanText(res.data.translations[0].translatedText);
        this.content = this.translatedContent;
      });
    }
  }

  // Function to remove HTML tags and decode HTML entities
  cleanText(text: string): string {
    const textarea = document.createElement("textarea");
    textarea.innerHTML = text;
    return textarea.value;
  }

  onHeadingChange(event: Event): void {
    const inputElement = event.target as HTMLInputElement;
    this.translatedHeading = inputElement.value.trim();
  }

  onChangeContent() {
    this.translatedContent = this.content;
  }
  
  onSave() {
    // Check cleaned content without modifying the original
  const cleanedContent = this.cleanContent(this.translatedContent);

   if (this.translatedHeading.trim() === '' || cleanedContent === '') {
    this.messageService.showMessage(['HeadingAndContentCannotBeEmpty'], 'error');
    return;
  }
    const fullLanguageName = this.languageNames[this.selectedLanguage] || this.selectedLanguage;

    this.dialogRef.close({
      heading: this.translatedHeading,
      content: this.translatedContent,
      languageShort: this.selectedLanguage,
      languageFullName: fullLanguageName
    })
  }

  cleanContent(content: string): string {
    if (!content) return ''; // Ensure it's not undefined/null
  
    return content.replace(/<(p|br|div|span)>\s*<\/\1>/g, '').trim(); // Remove empty tags
  }
  
}
