import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { environment } from '../../../environments/enviroment';
import { Observable, forkJoin, of } from 'rxjs';
import { map, catchError } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class TranslationService {
  private apiUrl = 'https://translation.googleapis.com/language/translate/v2';
  private maxChunkSize = 1000; // Characters per chunk

  constructor(private http: HttpClient) {}

  /**
   * Translates text, handling long texts by splitting into chunks
   * @param text Text to translate
   * @param targetLang Target language code
   * @returns Observable with translation result
   */
  translate(text: string, targetLang: string): Observable<any> {
    // If text is short enough, translate directly
    if (text.length <= this.maxChunkSize) {
      return this.translateChunk(text, targetLang);
    }
    
    // Split long text into chunks
    const chunks = this.splitTextIntoChunks(text, this.maxChunkSize);
    
    // Create an array of translation observables
    const translationObservables = chunks.map(chunk => 
      this.translateChunk(chunk, targetLang).pipe(
        map((response: any) => {
          if (response?.data?.translations?.[0]?.translatedText) {
            return response.data.translations[0].translatedText;
          }
          return '';
        }),
        catchError(error => {
          console.error('Translation chunk error:', error);
          return of(''); // Return empty string on error
        })
      )
    );
    
    // Combine all translation responses
    return forkJoin(translationObservables).pipe(
      map(translatedChunks => {
        const combinedTranslation = translatedChunks.join(' ');
        // Return in same format as single translation
        return {
          data: {
            translations: [{
              translatedText: combinedTranslation
            }]
          }
        };
      })
    );
  }

  /**
   * Translates a single chunk of text
   * @param text Text chunk to translate
   * @param targetLang Target language code
   * @returns Observable with translation result
   */
  private translateChunk(text: string, targetLang: string): Observable<any> {
    const params = {
      q: text,
      target: targetLang,
      key: environment.googleTranslateApiKey
    };

    return this.http.post(this.apiUrl, null, { params });
  }

  /**
   * Splits text into chunks, trying to break at sentence boundaries and word boundaries
   * @param text Text to split
   * @param maxChunkSize Maximum characters per chunk
   * @returns Array of text chunks
   */
  private splitTextIntoChunks(text: string, maxChunkSize: number): string[] {
    const chunks: string[] = [];
    
    // Common sentence ending patterns
    const sentenceEndRegex = /[.!?།。]+[\s\n]*/g;
    
    // Find all sentence boundaries
    const sentences: string[] = [];
    let lastIndex = 0;
    let match;
    
    while ((match = sentenceEndRegex.exec(text)) !== null) {
      sentences.push(text.substring(lastIndex, match.index + match[0].length));
      lastIndex = match.index + match[0].length;
    }
    
    // Add the last part if there is any
    if (lastIndex < text.length) {
      sentences.push(text.substring(lastIndex));
    }
    
    // Group sentences into chunks
    let currentChunk = '';
    
    for (const sentence of sentences) {
      // If adding this sentence exceeds the limit, save current chunk and start a new one
      if (currentChunk.length + sentence.length > maxChunkSize) {
        // If the current chunk is not empty, add it to chunks
        if (currentChunk.length > 0) {
          chunks.push(currentChunk);
          currentChunk = '';
        }
        
        // If a single sentence is longer than maxChunkSize, split it by word boundaries
        if (sentence.length > maxChunkSize) {
          let remaining = sentence;
          while (remaining.length > 0) {
            if (remaining.length <= maxChunkSize) {
              chunks.push(remaining);
              remaining = '';
            } else {
              // Find the last space before the maxChunkSize
              let cutPoint = remaining.substring(0, maxChunkSize).lastIndexOf(' ');
              
              // If no space found, cut at maxChunkSize (rare case for very long words)
              if (cutPoint === -1) {
                cutPoint = maxChunkSize;
              }
              
              chunks.push(remaining.substring(0, cutPoint));
              // Skip the space when starting the next chunk
              remaining = remaining.substring(cutPoint).trimStart();
            }
          }
        } else {
          currentChunk = sentence;
        }
      } else {
        // Add the sentence to the current chunk
        currentChunk += sentence;
      }
    }
    
    // Add the last chunk if not empty
    if (currentChunk.length > 0) {
      chunks.push(currentChunk);
    }
    
    return chunks;
  }
}