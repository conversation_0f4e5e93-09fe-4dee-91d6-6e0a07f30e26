@import 'material-icons/iconfont/material-icons.css';

/* Define primary color */
:root {
    --primary-color: #22D400; /* Replace with your chosen primary color */
    --secondary-color: #383838;
}

html, body {
    height: 100%;
    overflow: hidden !important;
    color: var(--primary-color); /* Set text color to primary color */
    color: var(--secondary-color); /* Set text color to primary color */
}

body {
    margin: 0;
    font-family: Roboto, "Helvetica Neue", sans-serif;
}

.left-big-border {
    border-left: 8px solid #22D400;
}

.brand-pink-color {
    color: #22D400
}

.mat-typography h1[mat-dialog-title] {
  font: 400 24px / 32px Roboto, sans-serif;
  letter-spacing: normal;
  margin-bottom: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.custom-info-search-window {
  font-family: 'Roboto', sans-serif;
  color: #1164d8;
}

.custom-info-search-window h4 {
  margin: 0;
  color: #0f5bd6;
  font-size: 16px;
}

.bold-text {
    font-weight: bold;
}

.small-text {
    font-size: 13px;
}

.flex-5 {
    flex: 5;
}

.flex-2 {
    flex: 2;
}

mat-label{
  display: flex;
  height: 30px;
}

.wrapper{
  background-color: #fff;
}

.error-msg {
  font-size: 12px;
  line-height: 12px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
  margin: 4px 0;
}
.cdk-overlay-pane.cm-xl-modal-dialog .mat-mdc-dialog-content {
  max-height: calc(100% - 120px) !important;
}

textarea[name=comment] {
  resize: none;
}