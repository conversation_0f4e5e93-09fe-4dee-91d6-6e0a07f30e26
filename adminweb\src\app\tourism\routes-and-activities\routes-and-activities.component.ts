import { CommonModule, DatePipe } from '@angular/common';
import { Component, DestroyRef, ElementRef, inject, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatDividerModule } from '@angular/material/divider';
import { MatSelectModule } from '@angular/material/select';
import { MatCardModule } from '@angular/material/card';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatInputModule } from '@angular/material/input';
import { MatMenuModule } from '@angular/material/menu';
import { MatSortModule, Sort } from '@angular/material/sort';
import { Router, RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { MatPseudoCheckboxModule } from '@angular/material/core';
import { MatDialog } from '@angular/material/dialog';
import { MatTooltip } from '@angular/material/tooltip';
import { GridColumnModel } from '../../shared/interfaces/settings/grid-settings.model';
import { PhoneEventsPreviewComponent } from '../../shared/phone-events-preview/phone-events-preview.component';
import { InterestPointsItemEntity } from '../../shared/interfaces/interest-points/interest-points-item.model';
import { PaginationSortModel } from '../../shared/interfaces/paginator/pagination-sort.model';
import { GridSetting } from '../../shared/constants/grid-settings';
import { InterestPointsService } from '../../core/services/interest-points.service';
import { tap } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { InterestPointsGetByIdModel } from '../../shared/interfaces/interest-points/interest-points-get-by-id.model';
import { VerificationModalComponent } from '../../shared/verification-modal/verification-modal.component';
import { ReusableDetailsComponentComponent } from '../../shared/reusable-details-component/reusable-details-component.component';
import { ReusableDeleteComponentComponent } from '../../shared/reusable-delete-component/reusable-delete-component.component';
import { MessageService } from '../../core/services/message.service';

@Component({
  selector: 'app-routes-and-activities',
  standalone: true,
  imports: [
              CommonModule,
              MatDividerModule,
              MatSelectModule,
              MatIconModule,
              MatButtonModule,
              MatCardModule,
              MatTableModule,
              MatPseudoCheckboxModule,
              ReactiveFormsModule,
              TranslateModule,
              MatInputModule,
              MatDatepickerModule,
              FormsModule,
              MatPaginatorModule,
              MatPaginator,
              MatTableModule,
              MatMenuModule,
              MatSortModule,
              RouterModule,
              MatTooltip,
              MatCheckboxModule
  ],
  templateUrl: './routes-and-activities.component.html',
  styleUrl: './routes-and-activities.component.css'
})
export class RoutesAndActivitiesComponent {
  @ViewChild(MatPaginator) paginator!: MatPaginator | null;
  private destroyRef = inject(DestroyRef);
  @ViewChild('fileInput') fileInput!: ElementRef<HTMLInputElement>;


  routesFilterForm!: FormGroup;
  
  private clickTimeout: any; // To manage single-click timeout
  private clickDelay = 300; // Time in milliseconds to distinguish single from double click
  private isDoubleClick = false; // Flag to track if double-click occurred
  protected totalCount = 0;
  protected gridColumns: GridColumnModel[] = [];
  protected gridColors: [] = []
  protected isImporting = false;


  displayedColumns: string[] = [
    'id',
    'name',
    'latestVerification',
    'verifiedBy',
    'phoneNumber',
    'website',
    'latitude',
    'longitude',
    'actions',
  ];

  protected dataSource: MatTableDataSource<InterestPointsItemEntity> =
      new MatTableDataSource<InterestPointsItemEntity>([]);

  protected paginationSort: PaginationSortModel = {
      pageNumber: GridSetting.defaultPageNumber,
      pageSize: GridSetting.defaultPageSize,
      sortColumn: GridSetting.defaultSortColumn,
      sortDirection: GridSetting.defaultSortDirection,
  };

 ngOnInit() {
  this.getInterestPointsData();
 }

  constructor(
    private fb: FormBuilder,
    public dialog: MatDialog,
    private datePipe: DatePipe,
    private router: Router,
    private interestPointsService: InterestPointsService,
    private messageService: MessageService
    ) {
    this.routesFilterForm = this.fb.group({
      search: [''],
      isVerified: [false]
    });
  }

   private getInterestPointsData(paginationSort = this.paginationSort): void {
  
      let search = this.routesFilterForm.value.search;
      let isVerified = this.routesFilterForm.value.isVerified;
      let category = 'Hiking';
      let { pageNumber, pageSize, sortColumn, sortDirection } = paginationSort;
      this.interestPointsService
        .getInterestPoints(
          search,
          isVerified,
          category,
          sortColumn,
          sortDirection,
          pageNumber,
          pageSize
        )
        .pipe(
          tap((data) => {
            this.dataSource = new MatTableDataSource<InterestPointsItemEntity>(data.items);
            this.totalCount = data.totalCount;
          }),
          takeUntilDestroyed(this.destroyRef)
        )
        .subscribe();
    }

    protected onPageChange(event: any) {
      const pageIndex = event.pageIndex + 1; // Paginator index starts from 0, while your API starts from 1
      const pageSize = event.pageSize;
      this.paginationSort = {
        ...this.paginationSort,
        pageSize: pageSize,
        pageNumber: pageIndex,
      };
  
      this.getInterestPointsData();
    }
  
    protected onSortChange(sortState: Sort) {
      this.paginationSort = {
        ...this.paginationSort,
        sortColumn: sortState.active,
        sortDirection: sortState.direction,
      };
      this.getInterestPointsData();
    }
  
    private resetPaginatorSort() {
      if (this.paginator) {
        this.paginator.firstPage();
      }
      this.paginationSort = {
        pageNumber: GridSetting.defaultPageNumber,
        pageSize: GridSetting.defaultPageSize,
        sortColumn: GridSetting.defaultSortColumn,
        sortDirection: GridSetting.defaultSortDirection,
      };
    }

  openAddDialog(): void {
    const category = 'Hiking';
    const returnPath = encodeURIComponent('/tourism/routes-and-activities'); // Encode to avoid route issues
    const title = 'AddRoutesAndActivities';
  
    this.router.navigate(['/add-item'], { 
      queryParams: { category, returnPath, title }
    });
  }
  
  // New import functionality
openImportDialog(): void {
  this.fileInput.nativeElement.click();
}

onFileSelected(event: Event): void {
  const input = event.target as HTMLInputElement;
  if (input.files && input.files.length > 0) {
    const file = input.files[0];
    
    // Validate file type
    if (!this.isValidExcelFile(file)) {
      this.messageService.showMessage(["PleaseSelectValidExcelFile"], 'error');
      return;
    }

    this.importItems(file);
  }
}

private isValidExcelFile(file: File): boolean {
  const validTypes = [
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
    'application/vnd.ms-excel' // .xls
  ];
  return validTypes.includes(file.type) || file.name.endsWith('.xlsx') || file.name.endsWith('.xls');
}

private importItems(file: File): void {
  this.isImporting = true;
  const category = 'Hiking'; // Change this for each screen
  
  this.interestPointsService.importInterestPoints(category, file)
    .pipe(
      tap({
        next: (response) => {
          this.messageService.showMessage(["ImportSuccessfully"], 'success');
          this.getInterestPointsData(); // Refresh the data
          this.fileInput.nativeElement.value = ''; // Clear the file input
        },
        error: (error) => {
          console.error('Import error:', error);
          this.messageService.showMessage(["ImportFailed"], 'error');
          this.fileInput.nativeElement.value = ''; // Clear the file input
        },
        complete: () => {
          this.isImporting = false;
        }
      }),
      takeUntilDestroyed(this.destroyRef)
    )
    .subscribe();
}

  editElement(element: any): void {
    const category = 'Hiking';
    const returnPath = encodeURIComponent('/tourism/routes-and-activities'); // Encode to avoid route issues
    const title = 'EditRoutesAndActivities'; // Change the title to reflect editing
    const markerIcon = 'assets/images/routes-icon.svg'
  
    // Navigate to edit route with the 'id' in the path
    this.router.navigate(['/edit-item', element.id], { 
      queryParams: { category, returnPath, title, markerIcon } // Send category, returnPath, and title as queryParams
    });
  }
  
  
  deleteElement(element: any): void {
    const dialogRef = this.dialog.open(ReusableDeleteComponentComponent, {
      width: '530px',
      data: { id: element.id, title: 'DeleteRoutesAndActivities'}
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.getInterestPointsData();
      }
    });
  }

  viewDetails(element: any): void {
    this.interestPointsService.getInterestPointById(element.id).subscribe({
      next: (data) => {
        const markerIcon = 'assets/images/routes-icon.svg'

        const dialogRef = this.dialog.open(ReusableDetailsComponentComponent, {
          width: '530px',
          height: '810px',
          data: {data: data, markerIcon: markerIcon}
        });
  
        dialogRef.afterClosed().subscribe(() => {
          console.log('Dialog closed');
        });
      },
      error: (error) => {
        console.error('Error fetching details:', error);
      },
      complete: () => {
        console.log('Request completed.');
      }
    })
  }
  

  openPreview(element: any): void {
    this.interestPointsService.getInterestPointById(element.id).subscribe({
      next: (data) => {
        let imgPath;
        if(data.images.length > 0) {
          imgPath = data.images[0].preSignedUrl;  
        } else if(data.images.length <= 0) {
          imgPath = '../../../assets/images/default-background-image.png'
        }

        let cleanedDescription = this.cleanContent(data.description)

        console.log(cleanedDescription, data.description)

        const headerPath = '../../../assets/images/routes-and-activities-header.png'
        const dialogRef = this.dialog.open(PhoneEventsPreviewComponent, {
          width: '480px',
          data: {heading: data.name, content: cleanedDescription, contentImage: imgPath, headerImage: headerPath}
        });
    
        dialogRef.afterClosed().subscribe(result => {
    
        });
      },
      error: (error) => {
        console.error('Error fetching details:', error);
      },
      complete: () => {
        console.log('Request completed.');
      }
    })
  }
  
  verify(element: any): void {
    const dialogRef = this.dialog.open(VerificationModalComponent, {
      width: '530px',
      data: { id: element.id, content: element.content }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.getInterestPointsData();
      }
    });
  }

  onRowClick(event: MouseEvent, row: any): void {
    // Check if the event target is a button or inside a specific column
    const target = event.target as HTMLElement;
  
    // Prevent row click if the target is a button, icon, or menu
    if (
      target.closest('button') ||
      target.closest('mat-menu') ||
      target.closest('.actions-header')
    ) {
      return;
    }
  
    // Reset double-click flag
    this.isDoubleClick = false;

    // Set a timeout for single-click action
    this.clickTimeout = setTimeout(() => {
      if (!this.isDoubleClick) {
        this.openPreview(row); // Trigger single-click action
      }
    }, this.clickDelay);
  }
  
  onRowDoubleClick(event: MouseEvent, row: any): void {
    // Check if the event target is a button or inside a specific column
    const target = event.target as HTMLElement;
  
    // Prevent row double-click if the target is a button, icon, or menu
    if (
      target.closest('button') ||
      target.closest('mat-menu') ||
      target.closest('.actions-header')
    ) {
      return;
    }
  
    // Set double-click flag to true
    this.isDoubleClick = true;

    // Clear the single-click timeout to prevent its execution
    clearTimeout(this.clickTimeout);

    // Trigger double-click action
    this.editElement(row);
  }
  
  cleanContent(content: string): string {
    if (!content) return ''; // Ensure it's not undefined/null
  
    return content
      .replace(/<\/?p>/g, '') // Remove opening and closing <p> tags
      .replace(/<br\s*\/?>/g, ' ') // Replace <br> tags with space
      .replace(/\s+/g, ' ') // Replace multiple spaces with a single space
      .trim(); // Trim leading/trailing spaces
  }
  

  onSearch() {
    this.resetPaginatorSort();
    this.getInterestPointsData();
  }

  changeSticky(element: string) {
    return this.gridColumns.find(column => column.columnName === element)?.fixed;
  }
}
