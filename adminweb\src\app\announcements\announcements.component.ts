import { CommonModule } from '@angular/common';
import {
  ChangeDetectorRef,
  Component,
  DestroyRef,
  inject,
  OnInit,
  ViewChild,
} from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatDividerModule } from '@angular/material/divider';
import { MatIconModule } from '@angular/material/icon';
import { MatSelectModule } from '@angular/material/select';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MatInputModule } from '@angular/material/input';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatMenuModule } from '@angular/material/menu';
import { MatSortModule, Sort } from '@angular/material/sort';
import { Router, RouterModule } from '@angular/router';
import { MatDialog } from '@angular/material/dialog';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { tap } from 'rxjs';
import { GridColumnModel } from '../shared/interfaces/settings/grid-settings.model';
import { UserItemEntity } from '../shared/interfaces/users/users-item.model';
import { EnumsModel } from '../shared/interfaces/enums/enum.model';
import { PaginationSortModel } from '../shared/interfaces/paginator/pagination-sort.model';
import { GridSetting } from '../shared/constants/grid-settings';
import { UsersService } from '../core/services/user.service';
import { AuthService } from '../core/services/auth.service';
import { PlatformVariablesItemEntity } from '../shared/interfaces/platform-variables/platform-variables-item.model';
import { PlatformVariablesService } from '../core/services/platform-variables.service';
import { MatRadioChange, MatRadioModule } from '@angular/material/radio';
import { MessageService } from '../core/services/message.service';

@Component({
  selector: 'app-announcements',
  standalone: true,
  imports: [
        CommonModule,
        MatDividerModule,
        MatSelectModule,
        MatIconModule,
        MatButtonModule,
        MatCardModule,
        MatTableModule,
        MatCheckboxModule,
        ReactiveFormsModule,
        TranslateModule,
        MatInputModule,
        MatDatepickerModule,
        FormsModule,
        MatPaginatorModule,
        MatPaginator,
        MatTableModule,
        MatMenuModule,
        MatSortModule,
        RouterModule,
        MatButtonModule,
        MatRadioModule
  ],
  templateUrl: './announcements.component.html',
  styleUrl: './announcements.component.css'
})
export class AnnouncementsComponent {
  private destroyRef = inject(DestroyRef);
  @ViewChild(MatPaginator) paginator!: MatPaginator | null;
  announcementsFilterForm: FormGroup;

  protected gridColumns: GridColumnModel[] = [];
  protected gridColors: [] = [];

  displayedColumns: string[] = [
    'id',
    'name',
    'value',
    'type',
    'createdBy',
    'createdDate',
    'lastModifiedBy',
    'lastModifiedDate',
    'deletedBy',
    'deletedDate',
    'actions',
  ];

  protected dataSource: MatTableDataSource<PlatformVariablesItemEntity> =
    new MatTableDataSource<PlatformVariablesItemEntity>([]);

  protected totalCount = 0;
  private isDoubleClick = false; // Flag to track if double-click occurred
  private clickTimeout: any; // To manage single-click timeout

  protected paginationSort: PaginationSortModel = {
    pageNumber: GridSetting.defaultPageNumber,
    pageSize: GridSetting.defaultPageSize,
    sortColumn: GridSetting.defaultSortColumn,
    sortDirection: GridSetting.defaultSortDirection,
  };

  constructor(
    private fb: FormBuilder,
    private translate: TranslateService,
    private router: Router,
    public dialog: MatDialog,
    private platformVariablesService: PlatformVariablesService,
    private messageService: MessageService
  ) {
    this.announcementsFilterForm = this.fb.group({
      name: [''],
    });
  }

  ngOnInit(): void {
    this.getPlatformVariablesData();
  }

  private getPlatformVariablesData(paginationSort = this.paginationSort): void {
    /*

    let name = this.announcementsFilterForm.value.name;
    let isDeleted = this.announcementsFilterForm.value.isDeleted;

    console.log(isDeleted);

    let { pageNumber, pageSize, sortColumn, sortDirection } = paginationSort;
    this.platformVariablesService
      .getPlatformVariables(
        name,
        isDeleted,
        sortColumn,
        sortDirection,
        pageNumber,
        pageSize
      )
      .pipe(
        tap((data) => {
          this.dataSource = new MatTableDataSource<PlatformVariablesItemEntity>(data.items);
          this.totalCount = data.totalCount;
        }),
        takeUntilDestroyed(this.destroyRef)
      )
      .subscribe();
      */
  }
  
  protected onPageChange(event: any) {
    const pageIndex = event.pageIndex + 1; // Paginator index starts from 0, while your API starts from 1
    const pageSize = event.pageSize;
    this.paginationSort = {
      ...this.paginationSort,
      pageSize: pageSize,
      pageNumber: pageIndex,
    };

    this.getPlatformVariablesData();
  }

  protected onSortChange(sortState: Sort) {
    this.paginationSort = {
      ...this.paginationSort,
      sortColumn: sortState.active,
      sortDirection: sortState.direction,
    };
    this.getPlatformVariablesData();
  }

  private resetPaginatorSort() {
    if (this.paginator) {
      this.paginator.firstPage();
    }
    this.paginationSort = {
      pageNumber: GridSetting.defaultPageNumber,
      pageSize: GridSetting.defaultPageSize,
      sortColumn: GridSetting.defaultSortColumn,
      sortDirection: GridSetting.defaultSortDirection,
    };
  }


  protected openAddAnnouncementsDialog(): void {
    /*
    // Navigate to repairAdd route
    this.router.navigate(['/repair/repair-add']);
    */
  }


  protected editElement(element: any): void {
    /*
      // Navigate to repairEdit route
      this.router.navigate(['/repair/repair-edit', element.id], {
      });
      */
    }

  protected deleteElement(element: any): void {
    /*
    const dialogRef = this.dialog.open(DeleteRepairComponent, {
      width: '530px',
      data: { id: element.id, content: element.content }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.getRepairsData();
      }
    });
    */
  }

  protected viewDetails(element: any): void {
    /*
      this.repairService.getRepairById(element.id).subscribe({
        next: (data) => {
          const mergedElement = { ...element, ...data };
    
          const dialogRef = this.dialog.open(DetailsRepairComponent, {
            height: '600px',
            width: '530px',
            data: {data: mergedElement}
          });
    
          dialogRef.afterClosed().subscribe(() => {
            console.log('Dialog closed');
          });
        },
        error: (error) => {
          console.error('Error fetching details:', error);
        },
        complete: () => {
          console.log('Request completed.');
        }
      });
      */
    }


  onRowDoubleClick(event: MouseEvent, row: any): void {
    // Check if the event target is a button or inside a specific column
    const target = event.target as HTMLElement;
  
    // Prevent row double-click if the target is a button, icon, or menu
    if (
      target.closest('button') ||
      target.closest('mat-menu') ||
      target.closest('.actions-header')
    ) {
      return;
    }
  
    // Set double-click flag to true
    this.isDoubleClick = true;

    // Clear the single-click timeout to prevent its execution
    clearTimeout(this.clickTimeout);

    // Trigger double-click action
    this.editElement(row);
  }

  onSearch() {
    this.resetPaginatorSort();
    this.getPlatformVariablesData();
  }
}
