import { CommonModule } from '@angular/common';
import {
  Component,
  DestroyRef,
  inject,
  OnInit,
  ViewChild,
} from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatDividerModule } from '@angular/material/divider';
import { MatIconModule } from '@angular/material/icon';
import { MatSelectModule } from '@angular/material/select';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MatInputModule } from '@angular/material/input';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatMenuModule } from '@angular/material/menu';
import { MatSortModule, Sort } from '@angular/material/sort';
import { Router, RouterModule } from '@angular/router';
import { MatDialog } from '@angular/material/dialog';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { tap } from 'rxjs';
import { GridColumnModel } from '../shared/interfaces/settings/grid-settings.model';
import { UserItemEntity } from '../shared/interfaces/users/users-item.model';
import { EnumsModel } from '../shared/interfaces/enums/enum.model';
import { PaginationSortModel } from '../shared/interfaces/paginator/pagination-sort.model';
import { GridSetting } from '../shared/constants/grid-settings';
import { UsersService } from '../core/services/user.service';
import { AuthService } from '../core/services/auth.service';
import { CreateUsersComponent } from './create-users/create-users.component';
import { DeleteUsersComponent } from './delete-users/delete-users.component';
import { ResetPasswordUsersComponent } from './reset-password-users/reset-password-users.component';
import { EditUsersComponent } from './edit-users/edit-users.component';
import { DetailsUsersComponent } from './details-users/details-users.component';




@Component({
  selector: 'app-users',
  standalone: true,
  imports: [
    CommonModule,
    MatDividerModule,
    MatSelectModule,
    MatIconModule,
    MatButtonModule,
    MatCardModule,
    MatTableModule,
    MatCheckboxModule,
    ReactiveFormsModule,
    TranslateModule,
    MatInputModule,
    MatDatepickerModule,
    FormsModule,
    MatPaginatorModule,
    MatPaginator,
    MatTableModule,
    MatMenuModule,
    MatSortModule,
    RouterModule,
  ],
  templateUrl: './users.component.html',
  styleUrl: './users.component.css'
})
export class UsersComponent implements OnInit {
  private destroyRef = inject(DestroyRef);
  @ViewChild(MatPaginator) paginator!: MatPaginator | null;
  usersFilterForm: FormGroup;

  protected gridColumns: GridColumnModel[] = [];
  protected gridColors: [] = [];

  displayedColumns: string[] = [
    'id',
    'email',
    'firstName',
    'lastName',
    'role',
    'actions',
  ];

  protected dataSource: MatTableDataSource<UserItemEntity> =
    new MatTableDataSource<UserItemEntity>([]);

  protected userRoles!: EnumsModel; // Initialize as an empty array or as needed
  protected roles: string[] = []; // Initialize as an empty array or as needed
  protected totalCount = 0;
  protected role: string = '';
  private isDoubleClick = false; // Flag to track if double-click occurred
  private clickTimeout: any; // To manage single-click timeout

  protected paginationSort: PaginationSortModel = {
    pageNumber: GridSetting.defaultPageNumber,
    pageSize: GridSetting.defaultPageSize,
    sortColumn: GridSetting.defaultSortColumn,
    sortDirection: GridSetting.defaultSortDirection,
  };

  constructor(
    private fb: FormBuilder,
    private translate: TranslateService,
    private router: Router,
    public dialog: MatDialog,
    private usersService: UsersService,
    private authService: AuthService,
  ) {
    this.usersFilterForm = this.fb.group({
      search: [''],
      role: [''],
    });
  }

  ngOnInit(): void {
    this.getUsersData();
    this.getUsersRoles();
    this.role = this.authService.getRole() ?? '';
  }

  private getUsersData(paginationSort = this.paginationSort): void {
    let token = this.authService.getToken();

    let search = this.usersFilterForm.value.search;
    let role = this.usersFilterForm.value.role;
    let { pageNumber, pageSize, sortColumn, sortDirection } = paginationSort;
    this.usersService
      .getUsers(
        search,
        role,
        sortColumn,
        sortDirection,
        pageNumber,
        pageSize
      )
      .pipe(
        tap((data) => {
          this.dataSource = new MatTableDataSource<UserItemEntity>(data.items);
          this.totalCount = data.totalCount;
        }),
        takeUntilDestroyed(this.destroyRef)
      )
      .subscribe();
  }
  protected onPageChange(event: any) {
    const pageIndex = event.pageIndex + 1; // Paginator index starts from 0, while your API starts from 1
    const pageSize = event.pageSize;
    this.paginationSort = {
      ...this.paginationSort,
      pageSize: pageSize,
      pageNumber: pageIndex,
    };

    this.getUsersData();
  }

  // todo after backend is ready
  protected onSortChange(sortState: Sort) {
    this.paginationSort = {
      ...this.paginationSort,
      sortColumn: sortState.active,
      sortDirection: sortState.direction,
    };
    this.getUsersData();
  }

  private resetPaginatorSort() {
    if (this.paginator) {
      this.paginator.firstPage();
    }
    this.paginationSort = {
      pageNumber: GridSetting.defaultPageNumber,
      pageSize: GridSetting.defaultPageSize,
      sortColumn: GridSetting.defaultSortColumn,
      sortDirection: GridSetting.defaultSortDirection,
    };
  }

  private getUsersRoles(): void {
    this.usersService
      .getRolesTypes()
      .pipe(
        tap((data: EnumsModel) => {
          this.userRoles = data;
          this.roles = Object.values(this.userRoles);
        }),
        takeUntilDestroyed(this.destroyRef)
      )
      .subscribe();
  }

  editElement(element: any): void {

    const dialogRef = this.dialog.open(EditUsersComponent, {
      width: '700px',
      data: {
        id: element.id,
        firstName: element.firstName,
        lastName: element.lastName,
        role: element.role,
        profilePicture: element.profilePicture,
        roles: this.userRoles,
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.getUsersData();
      }
    });
  }

  resetPassword(element: any): void {
    const dialogRef = this.dialog.open(ResetPasswordUsersComponent, {
      width: '530px',
      data: {
        id: element.id,
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      
    });
  }

  deleteElement(element: any): void {
    const dialogRef = this.dialog.open(DeleteUsersComponent, {
      width: '530px',
      data: { id: element.id, role: element.role },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.getUsersData();
      }
    });
  }

  viewDetails(element: any): void {
    if (!element?.id) return; // Ensure ID exists
  
    this.usersService.getUsersById(element.id).subscribe({
      next: (response) => {
        if (response) {
          //MOCK PLATES FOR NOW

          response.vehiclePlates = ['У2534ВА', 'У5833АС', 'У4595ВК']

          const dialogRef = this.dialog.open(DetailsUsersComponent, {
            width: '530px',
            data: response, // Pass fetched data
          });
  
          dialogRef.afterClosed().subscribe((result) => {
            // Handle modal close event if needed
          });
        }
      },
      error: (error) => {
        console.error('Error fetching user details:', error);
        // Optionally display an error message
      }
    });
  }
  

  openAddUserDialog(): void {
    const dialogRef = this.dialog.open(CreateUsersComponent, {
      width: '693px',
      data: {
        roles: this.userRoles
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        
        this.getUsersData();
      }
    });
  }

  onRowDoubleClick(event: MouseEvent, row: any): void {
    // Check if the event target is a button or inside a specific column
    const target = event.target as HTMLElement;
  
    // Prevent row double-click if the target is a button, icon, or menu
    if (
      target.closest('button') ||
      target.closest('mat-menu') ||
      target.closest('.actions-header')
    ) {
      return;
    }
  
    // Set double-click flag to true
    this.isDoubleClick = true;

    // Clear the single-click timeout to prevent its execution
    clearTimeout(this.clickTimeout);

    // Trigger double-click action
    this.editElement(row);
  }


  onSearch() {
    this.resetPaginatorSort();
    this.getUsersData();
  }

}