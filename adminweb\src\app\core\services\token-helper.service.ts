import { Injectable } from '@angular/core';
import { jwtDecode } from 'jwt-decode';


@Injectable({
  providedIn: 'root',
})
export class TokenHelperService {

  decodeJWT(token: string): any {
    try {
      return jwtDecode(token);
    } catch (e) {
      console.error('Failed to decode token', e);
      return null;
    }
  }

  getRoleFromToken(token: string): string | null {
    const decodedToken = this.decodeJWT(token);
    if (!decodedToken) return null;

    // Check for role with both the full namespace and simplified key
    return decodedToken["http://schemas.microsoft.com/ws/2008/06/identity/claims/role"] || decodedToken.role || null;
  }

  getUserFullNameFromToken(token: string): string | null {
    const decodedToken = this.decodeJWT(token);
    if (!decodedToken) return null;

    // Adjust the key to match the exact structure in the token
    return decodedToken.userFullName || null;
  }

}