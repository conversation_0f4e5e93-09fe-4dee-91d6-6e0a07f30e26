import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { ReportsService } from '../../core/services/reports.service';
import { MessageService } from '../../core/services/message.service';


@Component({
  selector: 'app-reports-delete',
  standalone: true,
  imports: [
    TranslateModule,
    MatIconModule,
    MatDialogModule
  ],
  templateUrl: './reports-delete.component.html',
  styleUrl: './reports-delete.component.css'
})
export class ReportsDeleteComponent {
  constructor(
      public dialogRef: MatDialogRef<ReportsDeleteComponent>,
      private reportsService: ReportsService,
      private messageService: MessageService,
      @Inject(MAT_DIALOG_DATA) public data: any
    ) { }
  
    onNoClick(): void {
      this.dialogRef.close();
    }
  
    onYesClick(): void {
      this.reportsService.deleteReport(this.data.id).subscribe(
        {
          next: (response) => {
            this.messageService.showMessage(["DeleteSuccessfully"], 'success');
            this.dialogRef.close(true);
          },
          error: (error: any) => {
            console.error('Error deleting report:', error);
            // Handle error as per your application's requirement
          }
        }
      );
    }
}
