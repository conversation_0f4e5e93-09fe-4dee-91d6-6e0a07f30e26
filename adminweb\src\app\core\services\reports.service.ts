import { Injectable } from '@angular/core';
import { Observable, delay, of } from 'rxjs';
import { HttpClient, HttpParams } from '@angular/common/http';
import { environment } from '../../../environments/enviroment';
import { InterestPointsGetModel } from '../../shared/interfaces/interest-points/interest-points-get.model';
import { InterestPointsGetByIdModel } from '../../shared/interfaces/interest-points/interest-points-get-by-id.model';
import { ReportsGetModel } from '../../shared/interfaces/reports/reports-get-model';
import { EnumsModel } from '../../shared/interfaces/enums/enum.model';
import { ReportsGetByIdModel } from '../../shared/interfaces/reports/reports-get-by-id.model';

@Injectable({
  providedIn: 'root'
})
export class ReportsService {

  constructor(private http: HttpClient) {}

  getReports(
      search:           string | null,
      firstName:        string | null,
      lastName:         string | null,
      type:             string | null,
      fromDate:         string | null,
      toDate:           string | null,
      sortColumn:       string | null,
      sortDirection:    string | null,
      pageNumber:       number,
      pageSize:         number,
    ): Observable<ReportsGetModel> {
      return this.http.get<ReportsGetModel>(`${environment.apiUrl}/Reports`, {
        params: new HttpParams()
        .set('search', search ? search : '')
        .set('firstName', firstName ? firstName : '')
        .set('lastName', lastName ? lastName : '')
        .set('type', type ? type : '')
        .set('fromDate', fromDate ? fromDate : '')
        .set('toDate', toDate ? toDate : '')
        .set('sortColumn', sortColumn ? sortColumn : '')
        .set('sortDirection', sortDirection ? sortDirection : '')
        .set('pageNumber', pageNumber.toString())
        .set('pageSize', pageSize.toString())
      });
    }

  getReportsById(reportId: number): Observable<ReportsGetByIdModel> {
      return this.http.get<ReportsGetByIdModel>(`${environment.apiUrl}/Reports/${reportId}`);
  }

  deleteReport(reportId: number) {
    return this.http.delete<number>(`${environment.apiUrl}/Reports/${reportId}`)
  }

  getReportTypes(): Observable<EnumsModel> {
      return this.http.get<EnumsModel>(`${environment.apiUrl}/Enums/ReportTypes`);
    }

}