import { Inject, Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { AuthService } from './auth.service';
import { environment } from '../../../environments/enviroment';
import { EnumsModel } from '../../shared/interfaces/enums/enum.model';
import { PlatformVariablesGetModel } from '../../shared/interfaces/platform-variables/platform-variables-get.model';
import { PlatformVariablesCreateModel } from '../../shared/interfaces/platform-variables/platform-variables-create.model';
import { PlatformVariablesEditModel } from '../../shared/interfaces/platform-variables/platform-variable-edit.model';


@Injectable({
  providedIn: 'root'
})
export class PlatformVariablesService {

  private apiUrl = 'https://localhost:44347/Manufacturers'; // Replace with your actual backend URL

  constructor(private http: HttpClient, private authService: AuthService) {}

  
  getPlatformVariables(
    name:             string | null,
    isDeleted:        string | null,
    sortColumn:       string | null,
    sortDirection:    string | null,
    pageNumber:       number,
    pageSize:         number,
  ): Observable<PlatformVariablesGetModel> {
    return this.http.get<PlatformVariablesGetModel>(`${environment.apiUrl}/PlatformVariables`, {
      params: new HttpParams()
      .set('name', name ? name : '')
      .set('isDeleted', isDeleted ? isDeleted : '')
      .set('sortColumn', sortColumn ? sortColumn : '')
      .set('sortDirection', sortDirection ? sortDirection : '')
      .set('pageNumber', pageNumber.toString())
      .set('pageSize', pageSize.toString())
    });
  }

  getPlatformVariablesTypeList(): Observable<EnumsModel> {
    return this.http.get<EnumsModel>(`${environment.apiUrl}/PlatformVariables/Types`);
  }

  addPlatformVariable(platformVariable: PlatformVariablesCreateModel): Observable<string> {
    return this.http.post(`${environment.apiUrl}/PlatformVariables`, platformVariable, { responseType: 'text' });
  }

  editPlatformVariable(platformVariable: PlatformVariablesEditModel) {
    return this.http.put(`${environment.apiUrl}/PlatformVariables/${platformVariable.id}`, platformVariable);
  }

  deletePlatformVariable(platformVariableId: number) {
    return this.http.delete<number>(`${environment.apiUrl}/PlatformVariables/${platformVariableId}`)
  }

  restorePlatformVariable(id: number) {
    return this.http.put(`${environment.apiUrl}/PlatformVariables/${id}/Restore`, []);
  }

}