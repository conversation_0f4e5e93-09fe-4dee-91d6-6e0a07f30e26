import { Injectable } from '@angular/core';
import { Observable, delay, of } from 'rxjs';
import { HttpClient, HttpParams } from '@angular/common/http';
import { environment } from '../../../environments/enviroment';
import { InterestPointsGetModel } from '../../shared/interfaces/interest-points/interest-points-get.model';
import { InterestPointsGetByIdModel } from '../../shared/interfaces/interest-points/interest-points-get-by-id.model';

@Injectable({
  providedIn: 'root'
})
export class InterestPointsService {

  constructor(private http: HttpClient) {}

  getInterestPoints(
      search:           string | null,
      isVerified:       boolean | null,
      category:         string | null,
      sortColumn:       string | null,
      sortDirection:    string | null,
      pageNumber:       number,
      pageSize:         number,
    ): Observable<InterestPointsGetModel> {
      return this.http.get<InterestPointsGetModel>(`${environment.apiUrl}/InterestPoints/Admin`, {
        params: new HttpParams()
        .set('search', search ? search : '')
        .set('isVerified', isVerified ? isVerified : '')
        .set('category', category ? category : '')
        .set('sortColumn', sortColumn ? sortColumn : '')
        .set('sortDirection', sortDirection ? sortDirection : '')
        .set('pageNumber', pageNumber.toString())
        .set('pageSize', pageSize.toString())
      });
    }

  getInterestPointById(interestPointId: number): Observable<InterestPointsGetByIdModel> {
      return this.http.get<InterestPointsGetByIdModel>(`${environment.apiUrl}/InterestPoints/${interestPointId}`);
  }
  

  addInterestPoint(formData: FormData) {
    return this.http.post(`${environment.apiUrl}/InterestPoints`, formData);
  }

  editInterestPoint(formData: FormData) {
    return this.http.put(`${environment.apiUrl}/InterestPoints/${formData.get('id')}`, formData);
  }

  deleteInterestPoint(interestPointId: number) {
    return this.http.delete<number>(`${environment.apiUrl}/InterestPoints/${interestPointId}`)
  }

  verifyInterestPoint(interestPointId: number) {
    return this.http.put(`${environment.apiUrl}/InterestPoints/Verify/${interestPointId}`, {});
  }

  importInterestPoints(category: string, excelFile: File): Observable<any> {
    const formData = new FormData();
    formData.append('Category', category);
    formData.append('ExcelFile', excelFile);
    
    return this.http.post(`${environment.apiUrl}/InterestPoints/Import`, formData);
  }
 
}