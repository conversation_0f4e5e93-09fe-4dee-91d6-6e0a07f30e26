import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ReusableDetailsComponentComponent } from './reusable-details-component.component';

describe('ReusableDetailsComponentComponent', () => {
  let component: ReusableDetailsComponentComponent;
  let fixture: ComponentFixture<ReusableDetailsComponentComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ReusableDetailsComponentComponent]
    })
    .compileComponents();

    fixture = TestBed.createComponent(ReusableDetailsComponentComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
