<div class="contentPanel" fxLayout="column" xLayoutAlign="flex-start">
    <div fxLayout="row"
         fxFlex="0 1 100%"
         fxFlex.lt-lg="0 1 100%"
         fxFlex.lt-md="0 1 100%"
         class="panelHead">
          <span class="h1">{{ data.published === 'published' 
            ? ('UnpublishCultureEvent' | translate) 
            : ('PublishCultureEvent' | translate) }}</span>
          <button mat-button class="close-icon" [mat-dialog-close]="false">
            <mat-icon>close</mat-icon>
          </button>
    </div>
  </div>
  
    <p class="publish-content">{{ data.published === 'published' 
        ? ('DoYouWantToUnpublish?' | translate) 
        : ('DoYouWantToPublish?' | translate) }}</p>
    <mat-dialog-actions align="center" class="mat-dialog-content-wrapper">
      <button type="submit" mat-raised-button tabindex="1" class="button-yes">
        <span>{{ 'Yes' | translate }}</span>
      </button>
      <button mat-raised-button mat-dialog-close tabindex="-1" class="button-no">{{ 'No' | translate }}</button>
    </mat-dialog-actions>