<div class="contentPanel" fxLayout="column" xLayoutAlign="flex-start">
    <div fxLayout="row" fxFlex="0 1 100%" fxFlex.lt-lg="0 1 100%" fxFlex.lt-md="0 1 100%" class="panelHead">
      <span class="h1">{{ 'Details' | translate }}</span>
      <button mat-button class="close-icon" [mat-dialog-close]="false">
        <mat-icon>close</mat-icon>
      </button>
    </div>
  </div>
  
  <div mat-dialog-content>
    <!-- Image Slider -->
    <div class="image-slider-container">
      <div class="slider-wrapper">
        <div class="slider" #slider>
          <div *ngFor="let image of data.data.images; let i = index" class="slide">
            <img [src]="image.preSignedUrl" [alt]="'Image ' + (i + 1)">
          </div>
        </div>
      </div>
      <div class="slider-controls">
        <button mat-icon-button class="nav-btn prev-btn" (click)="prevSlide()">
          <mat-icon>chevron_left</mat-icon>
        </button>
        <div class="slide-indicators">
          <span *ngFor="let image of data.data.images; let i = index" 
                [ngClass]="{'active': currentSlide === i}"
                (click)="goToSlide(i)">
          </span>
        </div>
        <button mat-icon-button class="nav-btn next-btn" (click)="nextSlide()">
          <mat-icon>chevron_right</mat-icon>
        </button>
      </div>
    </div>
  
    <div class="details-report-content-row">
      <strong>{{ 'ID' | translate}}</strong>
      <p>{{data.data.id}}</p>
    </div>
    <div class="details-report-content-row">
      <strong>{{ 'name' | translate}}</strong>
      <p>{{data.data.name}}</p>
    </div>
    <div class="details-report-content-row">
      <strong>{{ 'date' | translate}}</strong>
      <p>{{data.data.startDate | date: 'dd/MM/yyyy'}}</p>
    </div>
    <div class="details-report-content-row">
      <strong>{{ 'createdBy' | translate}}</strong>
      <p>{{data.data.createdBy | translate}}</p>
    </div>
    <div class="details-report-content-row">
      <strong>{{ 'lastModifiedBy' | translate}}</strong>
      <p>{{data.data.lastModifiedBy}}</p>
    </div>
    <div class="details-report-content-row">
      <strong>{{ 'lastModifiedDate' | translate}}</strong>
      <p>{{data.data.lastModifiedDate | date: 'dd/MM/yyyy'}}</p>
    </div>
  </div>
