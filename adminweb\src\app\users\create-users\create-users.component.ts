import { Component, Inject } from '@angular/core';
import { <PERSON><PERSON><PERSON><PERSON>, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { UsersService } from '../../core/services/user.service';
import { MessageService } from '../../core/services/message.service';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { Validators } from 'ngx-editor';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatSelectModule } from '@angular/material/select';
import { MatOptionModule } from '@angular/material/core';
import { AuthService } from '../../core/services/auth.service';

@Component({
  selector: 'app-create-users',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    MatIconModule,
    MatDialogModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatDatepickerModule,
    MatSelectModule,
    MatOptionModule
  ],
  templateUrl: './create-users.component.html',
  styleUrl: './create-users.component.css'
})
export class CreateUsersComponent {
  userForm!: FormGroup;

  protected roles: string[] = []; // Initialize as an empty array or as needed
  protected filteredRoles: string[] = []; // Initialize as an empty array or as needed

  constructor(private translate: TranslateService, private fb: FormBuilder, private usersService: UsersService, 
    private messageService: MessageService, private authService: AuthService,
    private dialogRef: MatDialogRef<CreateUsersComponent>, @Inject(MAT_DIALOG_DATA) public data: any,) {
    this.userForm = this.fb.group({
      email: ['', Validators.required],
      password: ['', Validators.required],
      confirmPassword: ['', Validators.required],
      firstName: ['', Validators.required],
      lastName: ['', Validators.required],
      role: ['', Validators.required],
    });
  }

  ngOnInit(): void {
    this.roles = this.data.roles
    const userRole = this.authService.getRole()
    this.filteredRoles = this.roles.filter(role => userRole === 'AdminPlatform' || role !== 'AdminPlatform');
  }

  get form() {
    return this.userForm.controls;
  }

  onSubmit() {
    if (this.userForm.valid) {
      const usersData = this.userForm.value;
      this.usersService.addUsers(usersData).subscribe({
        next: (response) => {
          this.messageService.showMessage(["CreateSuccessfully"], 'success');
          
          // Handle successful response, e.g., show a success message or navigate to another page
           // Close the dialog after successful addition
           this.dialogRef.close(usersData);
        },
        error: (error) => {
          if (error.error instanceof ErrorEvent) {
            // Client-side error
            console.error('Client-side error:', error.error.message);
          } else {
            // Server-side error
            console.error(`Server returned code: ${error.status}, body was: ${error.error}`);
          }
        },
        complete: () => {
          
          // Handle completion of the observable if needed
        }
      });
    } else {
      
    }
  }
}
