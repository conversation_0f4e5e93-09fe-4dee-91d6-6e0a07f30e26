{"version": 3, "sources": ["../../../../../../node_modules/@angular/material/fesm2022/toolbar.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Directive, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, Input, ContentChildren, NgModule } from '@angular/core';\nimport { MatCommonModule } from '@angular/material/core';\nimport * as i1 from '@angular/cdk/platform';\nimport { DOCUMENT } from '@angular/common';\nconst _c0 = [\"*\", [[\"mat-toolbar-row\"]]];\nconst _c1 = [\"*\", \"mat-toolbar-row\"];\nclass MatToolbarRow {\n  static {\n    this.ɵfac = function MatToolbarRow_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatToolbarRow)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatToolbarRow,\n      selectors: [[\"mat-toolbar-row\"]],\n      hostAttrs: [1, \"mat-toolbar-row\"],\n      exportAs: [\"matToolbarRow\"],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatToolbarRow, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-toolbar-row',\n      exportAs: 'matToolbarRow',\n      host: {\n        'class': 'mat-toolbar-row'\n      },\n      standalone: true\n    }]\n  }], null, null);\n})();\nclass MatToolbar {\n  constructor(_elementRef, _platform, document) {\n    this._elementRef = _elementRef;\n    this._platform = _platform;\n    // TODO: make the document a required param when doing breaking changes.\n    this._document = document;\n  }\n  ngAfterViewInit() {\n    if (this._platform.isBrowser) {\n      this._checkToolbarMixedModes();\n      this._toolbarRows.changes.subscribe(() => this._checkToolbarMixedModes());\n    }\n  }\n  /**\n   * Throws an exception when developers are attempting to combine the different toolbar row modes.\n   */\n  _checkToolbarMixedModes() {\n    if (this._toolbarRows.length && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      // Check if there are any other DOM nodes that can display content but aren't inside of\n      // a <mat-toolbar-row> element.\n      const isCombinedUsage = Array.from(this._elementRef.nativeElement.childNodes).filter(node => !(node.classList && node.classList.contains('mat-toolbar-row'))).filter(node => node.nodeType !== (this._document ? this._document.COMMENT_NODE : 8)).some(node => !!(node.textContent && node.textContent.trim()));\n      if (isCombinedUsage) {\n        throwToolbarMixedModesError();\n      }\n    }\n  }\n  static {\n    this.ɵfac = function MatToolbar_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatToolbar)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.Platform), i0.ɵɵdirectiveInject(DOCUMENT));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatToolbar,\n      selectors: [[\"mat-toolbar\"]],\n      contentQueries: function MatToolbar_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, MatToolbarRow, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._toolbarRows = _t);\n        }\n      },\n      hostAttrs: [1, \"mat-toolbar\"],\n      hostVars: 6,\n      hostBindings: function MatToolbar_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassMap(ctx.color ? \"mat-\" + ctx.color : \"\");\n          i0.ɵɵclassProp(\"mat-toolbar-multiple-rows\", ctx._toolbarRows.length > 0)(\"mat-toolbar-single-row\", ctx._toolbarRows.length === 0);\n        }\n      },\n      inputs: {\n        color: \"color\"\n      },\n      exportAs: [\"matToolbar\"],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c1,\n      decls: 2,\n      vars: 0,\n      template: function MatToolbar_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef(_c0);\n          i0.ɵɵprojection(0);\n          i0.ɵɵprojection(1, 1);\n        }\n      },\n      styles: [\".mat-toolbar{background:var(--mat-toolbar-container-background-color, var(--mat-app-surface));color:var(--mat-toolbar-container-text-color, var(--mat-app-on-surface))}.mat-toolbar,.mat-toolbar h1,.mat-toolbar h2,.mat-toolbar h3,.mat-toolbar h4,.mat-toolbar h5,.mat-toolbar h6{font-family:var(--mat-toolbar-title-text-font, var(--mat-app-title-large-font));font-size:var(--mat-toolbar-title-text-size, var(--mat-app-title-large-size));line-height:var(--mat-toolbar-title-text-line-height, var(--mat-app-title-large-line-height));font-weight:var(--mat-toolbar-title-text-weight, var(--mat-app-title-large-weight));letter-spacing:var(--mat-toolbar-title-text-tracking, var(--mat-app-title-large-tracking));margin:0}.cdk-high-contrast-active .mat-toolbar{outline:solid 1px}.mat-toolbar .mat-form-field-underline,.mat-toolbar .mat-form-field-ripple,.mat-toolbar .mat-focused .mat-form-field-ripple{background-color:currentColor}.mat-toolbar .mat-form-field-label,.mat-toolbar .mat-focused .mat-form-field-label,.mat-toolbar .mat-select-value,.mat-toolbar .mat-select-arrow,.mat-toolbar .mat-form-field.mat-focused .mat-select-arrow{color:inherit}.mat-toolbar .mat-input-element{caret-color:currentColor}.mat-toolbar .mat-mdc-button-base.mat-mdc-button-base.mat-unthemed{--mdc-text-button-label-text-color:var(--mat-toolbar-container-text-color, var(--mat-app-on-surface));--mdc-outlined-button-label-text-color:var(--mat-toolbar-container-text-color, var(--mat-app-on-surface))}.mat-toolbar-row,.mat-toolbar-single-row{display:flex;box-sizing:border-box;padding:0 16px;width:100%;flex-direction:row;align-items:center;white-space:nowrap;height:var(--mat-toolbar-standard-height)}@media(max-width: 599px){.mat-toolbar-row,.mat-toolbar-single-row{height:var(--mat-toolbar-mobile-height)}}.mat-toolbar-multiple-rows{display:flex;box-sizing:border-box;flex-direction:column;width:100%;min-height:var(--mat-toolbar-standard-height)}@media(max-width: 599px){.mat-toolbar-multiple-rows{min-height:var(--mat-toolbar-mobile-height)}}\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatToolbar, [{\n    type: Component,\n    args: [{\n      selector: 'mat-toolbar',\n      exportAs: 'matToolbar',\n      host: {\n        'class': 'mat-toolbar',\n        '[class]': 'color ? \"mat-\" + color : \"\"',\n        '[class.mat-toolbar-multiple-rows]': '_toolbarRows.length > 0',\n        '[class.mat-toolbar-single-row]': '_toolbarRows.length === 0'\n      },\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      standalone: true,\n      template: \"<ng-content></ng-content>\\n<ng-content select=\\\"mat-toolbar-row\\\"></ng-content>\\n\",\n      styles: [\".mat-toolbar{background:var(--mat-toolbar-container-background-color, var(--mat-app-surface));color:var(--mat-toolbar-container-text-color, var(--mat-app-on-surface))}.mat-toolbar,.mat-toolbar h1,.mat-toolbar h2,.mat-toolbar h3,.mat-toolbar h4,.mat-toolbar h5,.mat-toolbar h6{font-family:var(--mat-toolbar-title-text-font, var(--mat-app-title-large-font));font-size:var(--mat-toolbar-title-text-size, var(--mat-app-title-large-size));line-height:var(--mat-toolbar-title-text-line-height, var(--mat-app-title-large-line-height));font-weight:var(--mat-toolbar-title-text-weight, var(--mat-app-title-large-weight));letter-spacing:var(--mat-toolbar-title-text-tracking, var(--mat-app-title-large-tracking));margin:0}.cdk-high-contrast-active .mat-toolbar{outline:solid 1px}.mat-toolbar .mat-form-field-underline,.mat-toolbar .mat-form-field-ripple,.mat-toolbar .mat-focused .mat-form-field-ripple{background-color:currentColor}.mat-toolbar .mat-form-field-label,.mat-toolbar .mat-focused .mat-form-field-label,.mat-toolbar .mat-select-value,.mat-toolbar .mat-select-arrow,.mat-toolbar .mat-form-field.mat-focused .mat-select-arrow{color:inherit}.mat-toolbar .mat-input-element{caret-color:currentColor}.mat-toolbar .mat-mdc-button-base.mat-mdc-button-base.mat-unthemed{--mdc-text-button-label-text-color:var(--mat-toolbar-container-text-color, var(--mat-app-on-surface));--mdc-outlined-button-label-text-color:var(--mat-toolbar-container-text-color, var(--mat-app-on-surface))}.mat-toolbar-row,.mat-toolbar-single-row{display:flex;box-sizing:border-box;padding:0 16px;width:100%;flex-direction:row;align-items:center;white-space:nowrap;height:var(--mat-toolbar-standard-height)}@media(max-width: 599px){.mat-toolbar-row,.mat-toolbar-single-row{height:var(--mat-toolbar-mobile-height)}}.mat-toolbar-multiple-rows{display:flex;box-sizing:border-box;flex-direction:column;width:100%;min-height:var(--mat-toolbar-standard-height)}@media(max-width: 599px){.mat-toolbar-multiple-rows{min-height:var(--mat-toolbar-mobile-height)}}\"]\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: i1.Platform\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }], {\n    color: [{\n      type: Input\n    }],\n    _toolbarRows: [{\n      type: ContentChildren,\n      args: [MatToolbarRow, {\n        descendants: true\n      }]\n    }]\n  });\n})();\n/**\n * Throws an exception when attempting to combine the different toolbar row modes.\n * @docs-private\n */\nfunction throwToolbarMixedModesError() {\n  throw Error('MatToolbar: Attempting to combine different toolbar modes. ' + 'Either specify multiple `<mat-toolbar-row>` elements explicitly or just place content ' + 'inside of a `<mat-toolbar>` for a single row.');\n}\nclass MatToolbarModule {\n  static {\n    this.ɵfac = function MatToolbarModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatToolbarModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatToolbarModule,\n      imports: [MatCommonModule, MatToolbar, MatToolbarRow],\n      exports: [MatToolbar, MatToolbarRow, MatCommonModule]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [MatCommonModule, MatCommonModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatToolbarModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCommonModule, MatToolbar, MatToolbarRow],\n      exports: [MatToolbar, MatToolbarRow, MatCommonModule]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MatToolbar, MatToolbarModule, MatToolbarRow, throwToolbarMixedModesError };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKA,IAAM,MAAM,CAAC,KAAK,CAAC,CAAC,iBAAiB,CAAC,CAAC;AACvC,IAAM,MAAM,CAAC,KAAK,iBAAiB;AACnC,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,OAAO;AACL,SAAK,OAAO,SAAS,sBAAsB,mBAAmB;AAC5D,aAAO,KAAK,qBAAqB,gBAAe;AAAA,IAClD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,iBAAiB,CAAC;AAAA,MAC/B,WAAW,CAAC,GAAG,iBAAiB;AAAA,MAChC,UAAU,CAAC,eAAe;AAAA,MAC1B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,MACX;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,aAAN,MAAM,YAAW;AAAA,EACf,YAAY,aAAa,WAAW,UAAU;AAC5C,SAAK,cAAc;AACnB,SAAK,YAAY;AAEjB,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,kBAAkB;AAChB,QAAI,KAAK,UAAU,WAAW;AAC5B,WAAK,wBAAwB;AAC7B,WAAK,aAAa,QAAQ,UAAU,MAAM,KAAK,wBAAwB,CAAC;AAAA,IAC1E;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,0BAA0B;AACxB,QAAI,KAAK,aAAa,WAAW,OAAO,cAAc,eAAe,YAAY;AAG/E,YAAM,kBAAkB,MAAM,KAAK,KAAK,YAAY,cAAc,UAAU,EAAE,OAAO,UAAQ,EAAE,KAAK,aAAa,KAAK,UAAU,SAAS,iBAAiB,EAAE,EAAE,OAAO,UAAQ,KAAK,cAAc,KAAK,YAAY,KAAK,UAAU,eAAe,EAAE,EAAE,KAAK,UAAQ,CAAC,EAAE,KAAK,eAAe,KAAK,YAAY,KAAK,EAAE;AAC/S,UAAI,iBAAiB;AACnB,oCAA4B;AAAA,MAC9B;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,mBAAmB,mBAAmB;AACzD,aAAO,KAAK,qBAAqB,aAAe,kBAAqB,UAAU,GAAM,kBAAqB,QAAQ,GAAM,kBAAkB,QAAQ,CAAC;AAAA,IACrJ;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,aAAa,CAAC;AAAA,MAC3B,gBAAgB,SAAS,0BAA0B,IAAI,KAAK,UAAU;AACpE,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,UAAU,eAAe,CAAC;AAAA,QAC9C;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,eAAe;AAAA,QAClE;AAAA,MACF;AAAA,MACA,WAAW,CAAC,GAAG,aAAa;AAAA,MAC5B,UAAU;AAAA,MACV,cAAc,SAAS,wBAAwB,IAAI,KAAK;AACtD,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,IAAI,QAAQ,SAAS,IAAI,QAAQ,EAAE;AACjD,UAAG,YAAY,6BAA6B,IAAI,aAAa,SAAS,CAAC,EAAE,0BAA0B,IAAI,aAAa,WAAW,CAAC;AAAA,QAClI;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,OAAO;AAAA,MACT;AAAA,MACA,UAAU,CAAC,YAAY;AAAA,MACvB,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB;AAAA,MACjC,oBAAoB;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,UAAU,SAAS,oBAAoB,IAAI,KAAK;AAC9C,YAAI,KAAK,GAAG;AACV,UAAG,gBAAgB,GAAG;AACtB,UAAG,aAAa,CAAC;AACjB,UAAG,aAAa,GAAG,CAAC;AAAA,QACtB;AAAA,MACF;AAAA,MACA,QAAQ,CAAC,q+DAAq+D;AAAA,MAC9+D,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,YAAY,CAAC;AAAA,IACnF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,WAAW;AAAA,QACX,qCAAqC;AAAA,QACrC,kCAAkC;AAAA,MACpC;AAAA,MACA,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,oBAAkB;AAAA,MACjC,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,QAAQ,CAAC,q+DAAq+D;AAAA,IACh/D,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC,eAAe;AAAA,QACpB,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAKH,SAAS,8BAA8B;AACrC,QAAM,MAAM,gMAA0M;AACxN;AACA,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,OAAO;AACL,SAAK,OAAO,SAAS,yBAAyB,mBAAmB;AAC/D,aAAO,KAAK,qBAAqB,mBAAkB;AAAA,IACrD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,iBAAiB,YAAY,aAAa;AAAA,MACpD,SAAS,CAAC,YAAY,eAAe,eAAe;AAAA,IACtD,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,SAAS,CAAC,iBAAiB,eAAe;AAAA,IAC5C,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,iBAAiB,YAAY,aAAa;AAAA,MACpD,SAAS,CAAC,YAAY,eAAe,eAAe;AAAA,IACtD,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}