import { InterestPointsImageModel } from "./interest-points-images.model";
import { InterestPointsTranslationModel } from "./interest-points-translations.model";

export interface InterestPointsGetByIdModel {
    name: string;
    description: string;
    category: string;
    latitude: number;
    longitude: number;
    id: number;
    website: string;
    phoneNumber: string;
    translations: InterestPointsTranslationModel[];
    images: InterestPointsImageModel[];
  }
  