import { Injectable } from '@angular/core';
import { MatSnackBar, MatSnackBarConfig, MatSnackBarRef } from '@angular/material/snack-bar';
import { BehaviorSubject, Observable } from 'rxjs';
import { SnackBarComponent } from '../../shared/snack-bar/snack-bar.component';


export interface Message {
  type: 'success' | 'error';
  text: string;
}

@Injectable({
  providedIn: 'root',
})
export class MessageService {
  private snack: MatSnackBarRef<any> | undefined;

  constructor(public snackBar: MatSnackBar) {}

  showMessage(messages: string[], type?: string,  seconds = 5, refreshPage = false): void {

    const snackBarConfig = new MatSnackBarConfig();
    snackBarConfig.data = messages;
    // Set SnackBar display duration
    snackBarConfig.duration = seconds * 1000;

    // Add classes to the SnackBar (used for custom styling)
    snackBarConfig.panelClass = type;

    // Show the SnackBar
    this.snack = this.snackBar.openFromComponent(SnackBarComponent, snackBarConfig);

      //message, actionLabel, snackBarConfig);

    if (refreshPage) {
      this.snack.onAction().subscribe(() => {
        location.reload();
      });
    }
  }

}