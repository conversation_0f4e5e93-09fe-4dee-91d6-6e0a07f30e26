<div class="contentPanel" fxLayout="column" xLayoutAlign="flex-start">
    <div fxLayout="row"
         fxFlex="0 1 100%"
         fxFlex.lt-lg="0 1 100%"
         fxFlex.lt-md="0 1 100%"
         class="panelHead">
          <span class="h1">{{ 'EditVariables' | translate }}</span>
          <button mat-button class="close-icon" [mat-dialog-close]="false">
            <mat-icon>close</mat-icon>
          </button>
    </div>
</div>
<div class="form-container">
  <form [formGroup]="platformVariablesEditForm" (ngSubmit)="onSubmit()">
    <mat-dialog-content class="mat-dialog-content-wrapper">
      <div fxLayout="column" fxLayoutAlign="flex-start">
        <div class="tabContent" fxLayout="row wrap" fxLayoutAlign="flex-start">
          <div class="tabColumn" fxLayout="row wrap">
             
             <!-- ID -->
             <mat-form-field appearance="outline" class="tabColumn-3-fields disabled-field">
                <mat-label>{{ 'ID' | translate }}</mat-label>
                <input matInput formControlName="id" readonly>
              </mat-form-field>

               <!-- name -->
               <mat-form-field appearance="outline" class="tabColumn-3-fields">
                <mat-label>{{'name' | translate}}</mat-label>
                <input matInput formControlName="name">
            </mat-form-field>

             <!-- value -->
             <mat-form-field appearance="outline" class="tabColumn-3-fields">
                <mat-label>{{'value' | translate}}</mat-label>
                <input matInput formControlName="value">
            </mat-form-field>

            <!-- type -->
            <mat-form-field appearance="outline" class="tabColumn-3-fields">
                <mat-label>{{'type' | translate}}</mat-label>
                <mat-select formControlName="type">
                  <mat-option *ngFor="let type of platformVariablesTypes" [value]="type">
                    {{ type | translate }}
                </mat-option>
                </mat-select>
              </mat-form-field>

          </div>
        </div>
      </div>
    </mat-dialog-content>
    <mat-dialog-actions align="center" class="mat-dialog-content-wrapper">
      <button type="submit" mat-raised-button tabindex="1" class="save">
        <mat-icon>save</mat-icon>
        <span>{{ 'Save' | translate }}</span>
      </button>
      <button mat-raised-button mat-dialog-close tabindex="-1" class="cancel">{{ 'Cancel' | translate }}</button>
    </mat-dialog-actions>
  </form>
</div>