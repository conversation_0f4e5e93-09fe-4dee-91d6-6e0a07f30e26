import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Child, importProvidersFrom } from '@angular/core';
import { MatSidenav, MatSidenavModule } from '@angular/material/sidenav';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatIconModule } from '@angular/material/icon';
import { MatListModule } from '@angular/material/list';
import { MatButtonModule } from '@angular/material/button';
import { Router, RouterLink, RouterOutlet } from '@angular/router';
import { CommonModule } from '@angular/common';
import { BreakpointObserver, Breakpoints } from '@angular/cdk/layout';
import { MatSelectModule } from '@angular/material/select';
import { MatFormFieldModule } from '@angular/material/form-field';
import { RouterModule } from '@angular/router';
import { MatMenuModule } from '@angular/material/menu';
import { MatExpansionModule } from '@angular/material/expansion';
import { TranslateModule, TranslateService } from '@ngx-translate/core';

import { Subscription } from 'rxjs';
import { MatTooltip } from '@angular/material/tooltip';
import { AuthService } from '../core/services/auth.service';

@Component({
  selector: 'app-navigation',
  standalone: true,
  imports: [
    CommonModule,
    MatSidenavModule,
    MatToolbarModule,
    MatIconModule,
    MatListModule,
    MatButtonModule,
    RouterOutlet,
    RouterLink,
    MatSelectModule,
    MatFormFieldModule,
    RouterModule,
    MatMenuModule,
    MatExpansionModule,
    TranslateModule,
    MatTooltip
  ],
  templateUrl: './navigation.component.html',
  styleUrl: './navigation.component.css'
})
export class NavigationComponent {
  @ViewChild('sidenav') sidenav!: MatSidenav;
  private isCollapsed: boolean = false;
  protected isMobile: boolean = false;
  protected isLoggedIn: boolean = true;
  protected isSubMenuEvents = false;
  protected isSubMenuBusiness = false;
  protected isSubMenuVenues = false;
  protected isSubMenuAccommodation = false;
  protected isSubMenuFinance = false;
  protected isSubMenuEcology = false;
  protected isSubMenuCulture = false;
  protected isSubMenuTourism = false;
  protected isSubMenuWorkAndTraining = false;
  protected isSubMenuEducation = false;
  protected isSubMenuHealth = false;
  protected isSubMenuSport = false;
  protected isLanguageChosen = false;
  selectedLanguage: string = 'bg';
  protected role: string | null = '';
  private roleSubscription: Subscription | undefined;
  private municipalityNameSubscription: Subscription | undefined;
  private userFullNameSubscription: Subscription | undefined;
  private isLoggedInSubscription: Subscription | undefined;
  protected roleLabel: string | null = '';
  protected userFullName: string | null = '';

  constructor(private breakpointObserver: BreakpointObserver,private router: Router, private translate: TranslateService, private authService: AuthService) {
  }

  ngOnInit() {
    //keeps track if the app is in mobile view
    this.breakpointObserver.observe([
      Breakpoints.XSmall,
      Breakpoints.Small,
      Breakpoints.Medium // Add more breakpoints as needed
    ]).subscribe(result => {
      this.isMobile = result.matches;
    });
  
    
    this.roleSubscription = this.authService.roleNav$.subscribe((role) => {
      this.role = role;
      if(this.role === 'AdminPlatform') {
        this.roleLabel = 'AdminPlatform';
      } 
    });

    this.userFullNameSubscription = this.authService.userFullNameNav$.subscribe((userFullName) => {
     this.userFullName = userFullName;
    })

    this.isLoggedInSubscription = this.authService.getLoggedIn().subscribe((isLoggedIn) => {
      this.isLoggedIn = isLoggedIn;
      console.log('Login status changed:', isLoggedIn);
    });
    
    

  }

  protected toggleSidebar() {
    this.isCollapsed = !this.isCollapsed;
    // if (this.isCollapsed) {
    //   this.sidenav.toggle();
    // } else {
    //   this.sidenav.open();
    // }
  }

  protected toggleLanguage() {
    this.isLanguageChosen = !this.isLanguageChosen;
    this.selectedLanguage = this.selectedLanguage === 'bg' ? 'en' : 'bg';
    this.translate.use(this.selectedLanguage)
  }


  //the navigation should be collapsed IF the sidebar is collapsed manually OR the app is in mobile view
  protected get shouldCollapseNavbar(){
    return this.isCollapsed || this.isMobile;
  }

  // Check if the current path is the path of your login component, if it is login, change the text after the logo in the navigation bar
  isLoginPage(): boolean {
    return this.router.url === '/login';

  }
 
  
  onLogOut() {
    
    this.role = "";
    this.authService.logout(); // Clear tokens and auth state
    this.router.navigate(['/login']); // Redirect to login page
    
  }
  

  ngOnDestroy(): void {
    this.roleSubscription?.unsubscribe();
    this.municipalityNameSubscription?.unsubscribe();
    this.userFullNameSubscription?.unsubscribe();
  }
}
