<form [formGroup]="editNewsForm">
  <div class="wrapper">
    <div class="header">
      <button mat-raised-button (click)="onBack()">
        <mat-icon>arrow_back</mat-icon>
        <span>{{ 'Back' | translate }}</span>
      </button>
      <h1 class="h1">{{"EditNews" | translate}}</h1>
      <div style="width: 150px;"></div>
    </div>
    <mat-divider></mat-divider>
    <div class="main-content">
      <div class="form-container">
        <div class="filter-container">
           
          <!-- ID -->
          <mat-form-field appearance="outline" class="id-field disabled-field">
            <mat-label>{{ 'ID' | translate }}</mat-label>
            <input matInput formControlName="id" readonly>
          </mat-form-field>
          
          <!-- heading -->
          <mat-form-field appearance="outline" class="tabColumn-1-fields heading">
            <mat-label>{{'heading' | translate}}</mat-label>
            <input matInput formControlName="heading" maxlength="60">
            <mat-hint align="end">{{ 60 - (editNewsForm.controls['heading'].value?.length || 0) }} {{ 'charactersRemaining' | translate }}</mat-hint>
        </mat-form-field>

        <div class="add-button-container">
          <!-- Photo Upload -->
        <button mat-raised-button class="add-button" (click)="openFileDialog()">
          <mat-icon>file_download_outline</mat-icon>
          <span>{{ 'AddPhotos' | translate }}</span>
        </button>

        <input type="file" #fileInput accept="image/*" (change)="onFileSelected($event)" style="display: none;" />

        </div>

        <div class="image-container" *ngIf="imageSrc">
          <h3>{{"Photos:" | translate}}</h3>
          <img [src]="imageSrc" alt="Photo" class="uploaded-image">
          <button mat-icon-button class="remove-icon" (click)="removePhoto()">
            <mat-icon>close</mat-icon>
          </button>
        </div>

        <div class="example-card">
          <mat-card>
          <mat-card-header>
            <mat-card-title class="card-content-title">{{ 'Content' | translate }}</mat-card-title>
          </mat-card-header>
          <mat-card-content class="card-content">
            <app-editor (focusout)="onChangeContent()" [(content)]="content" [disabled]="false"></app-editor>
          </mat-card-content>
          </mat-card>
          </div>

         
          <div class="crud-buttons">
          <button mat-raised-button tabindex="1" class="save-button" (click)="onSubmit()">
            <span>{{ 'Save' | translate }}</span>
        </button>
        <button mat-raised-button tabindex="1" class="cancel-button" (click)="onBack()">
            <span>{{ 'Cancel' | translate }}</span>
        </button>
        </div>

        </div>
      </div>

    </div>
  </div>
</form>