{"events": "Events", "cultureEvents": "Culture", "sportEvents": "Sport", "parking": "Parking", "PAGINATOR": {"ITEMS_PER_PAGE": "Items per page", "NEXT_PAGE": "Next", "PREVIOUS_PAGE": "Previous", "FIRST_PAGE": "First page", "LAST_PAGE": "Last page"}, "FromDate": "From date", "ToDate": "To date", "AddNewCultureEvent": "Add new culture event", "AddCultureEvent": "Adding culture event", "Save": "Save", "Cancel": "Cancel", "EditCultureEvent": "Edit culture event", "Details": "Details", "DeleteCultureEvent": "Delete culture event", "DoYouWantToDelete": "Are you sure you want to delete the record?", "repair": "Repair", "landmarks": "Landmarks", "business": "Business", "cameras": "Cameras", "polls": "Polls", "Login": "<PERSON><PERSON>", "Enter": "Enter", "LoginText": "Administrative Panel for City Data Management", "Email": "Email", "Password": "Password", "NewsAdministration": "News Administration", "LandmarksOverview": "Landmarks Overview", "BusinessRecords": "Business Records", "SportManagement": "Sport Management", "News": "News", "actions": "Actions", "date": "Date", "organizator": "Organizators", "contact": "Contacts", "culturalEvent": "Cultural event", "PhonePreview": "Phone Preview", "heading": "Heading", "director": "Director", "scenography": "Scenography", "cast": "Cast", "place": "Place", "startingHour": "Starting hour", "description": "Description", "AddNews": "Add news", "AddingNews": "Adding news", "EditNews": "Edit news", "DeleteNews": "Delete news", "AddLandmarks": "Add landmark", "AddingLandmarks": "Adding landmark", "EditLandmarks": "Edit landmark", "DeleteLandmark": "Delete landmark", "Content": "Content", "isRequired": "is required", "Back": "Back", "AddPhotos": "Add photos", "Photos:": "Images:", "published": "Published", "unpublished": "Unpublished", "PublishLandmark": "Publish Landmark", "UnpublishLandmark": "Unpublish Landmark", "DoYouWantToUnpublish?": "Are you sure you want to unpublish this record?", "DoYouWantToPublish?": "Are you sure you want to publish this record?", "Yes": "Yes", "No": "No", "PublishNews": "Publish News", "UnpublishNews": "Unpublish News", "transport": "Transport", "airQuality": "Air (quality)", "weather": "Weather", "weddings": "Weddings", "health": "Health", "garbage": "Garbage", "sport": "Sport", "hoReKa": "Venues", "restaurants": "Restaurants", "hotels": "Hotels", "cafes": "Cafes", "AddNewSportEvent": "Add new sport event", "AddSportEvent": "Adding sport event", "EditSportEvent": "Edit sport event", "DeleteSportEvent": "Delete sport event", "PublishSportEvent": "Publish sport event", "UnpublishSportEvent": "Unpublish sport event", "PublishCultureEvent": "Publish Culture Event", "UnpublishCultureEvent": "Unpublish Culture Event", "Parking": "Parking", "latitude": "Latitude", "longitude": "Longitude", "ParkingZoneCorrecting": "Correcting parking zone", "AddParking": "Add parking", "lat": "Latitude", "lng": "Longitude", "AddingParkingZone": "Adding parking zone", "DeleteParking": "Delete parking", "Date": "Date", "charactersRemaining": "Characters remaining", "bars": "Bars", "pastryShop": "Pastry Shops", "gasStation": "Gas Stations", "shops": "Shops", "Accommodation": "Accommodation", "guestHouses": "Guest houses", "Finance": "Finance", "banks": "Banks", "currencyChange": "Currency exchange", "insuranceCompanies": "Insurers", "ATMs": "ATMs", "Ecology": "Ecology", "bioShops": "Bio Shops", "farms": "Farms", "recyclingCenter": "Recycling", "ecologyInitiatives": "Eco initiative", "Culture": "Culture", "museums": "Museums", "theaters": "Theaters", "galleries": "Galleries", "tourism": "Tourism", "legendsAndMyths": "Myths and legends", "cultureAndArtisticPlaces": "Culture Places", "RoutesAndActivities": "Routes and activ.", "FamilyFun": "Family fun", "NightLife": "Night life", "travelAgencies": "Travel agencies", "workAndTraining": "Work and training", "work": "Work", "internshipsAndPrograms": "Internships & Prog.", "Education": "Education", "kindergardens": "Kindergardens", "nursery": "Nursery", "childNutritionCenter": "Child nutr. center", "schools": "Schools", "universities": "Universities", "developmentCenters": "Development centers", "pharmacies": "Pharmacies", "medicalEstablishments": "Medical establishments", "doctorsOffices": "Doctors offices", "medicalLabs": "Medical labs", "veterinaries": "Veterinaries", "celebrations": "Celebrations", "sportClubs": "Sport clubs", "sportFacilities": "Sport facilities", "AddRepair": "Add repair", "repairType": "Repair type", "AddingRepair": "Adding repair", "EditingRepair": "Editing repair", "DeleteRepair": "Delete repair", "TranslatedHeading:": "Translated heading: ", "TranslatedContent:": "Translated content: ", "SelectLanguage": "Select language", "Translate": "Translate", "TranslatedHeading": "Translated heading", "DoYouWantToDeleteTranslation": "Are you sure you want to delete the translation?", "DeleteTranslation": "Delete translation", "Bulgarian": "Bulgarian", "Spanish": "Spanish", "English": "English", "German": "German", "Italian": "Italian", "Turkish": "Turkish", "Greek": "Greek", "Serbian": "Serbian", "Romanian": "Romanian", "LOGIN_FAILED_MESSAGE": "<PERSON><PERSON> failed. Please check your credentials.", "CLOSE": "Close", "AdminPlatform": "Admin platform", "Admin": "Admin", "User": "User", "Session expired. Please login again.": "Session expired. Please login again.", "SearchFilter": "Search...", "role": "Role", "AddUser": "Add User", "users": "Users", "email": "Email", "names": "Names", "UserMunicipality": "User municipality", "RepeatPassword": "Repeat password", "AddingUser": "Adding user", "firstName": "First name", "lastName": "Last name", "Unauthorized access. Please login again.": "Unauthorized access. Please login again.", "DeleteUser": "Delete user", "CreateSuccessfully": "Record has been successfully created!", "UpdateSuccessfully": "Record has been successfully updated!", "DeleteSuccessfully": "Record has been successfully deleted!", "HeadingAndContentCannotBeEmpty": "Heading and content cannot be empty!", "PasswordReset": "Password reset", "GeneratePassword": "Generate password", "GenerateASecurePassword": "Generate secure password", "PasswordGeneratedSuccessfully": "Password generated successfully!", "EditUser": "Edit user", "vehiclePlates": "Vehicle plates", "Ukrainian": "Ukrainian", "French": "French", "PleaseCompleteTheForm": "Please complete the form!", "verified": "Verified", "showOnlyVerified": "Show only verified", "name": "Name", "latestVerification": "Latest verification", "phoneNumber": "Phone number", "website": "Website", "hideImages": "Hide images", "showImages": "Show images", "Verify": "Verify", "DoYouWantToVerify": "Are you sure you want to verify?", "verifiedBy": "Verified by", "VerificationSuccessfully": "Verification is successfull!", "Cover": "Cover", "AddLegendsAndMyths": "Add myth or a legend", "EditLegendAndMyths": "Edit legends and myths", "DeleteLegendAndMyth": "Delete legends and myths", "AddCultureAndArtisticPlaces": "Add culture places", "EditCultureAndArtisticPlaces": "Edit culture places", "DeleteCultureAndArtisticPlaces": "Delete culture places", "routesAndActivities": "Routes and activities", "AddRoutesAndActivities": "Add routes and activities", "EditRoutesAndActivities": "Edit routes and activities", "DeleteRoutesAndActivities": "Delete routes and activities", "familyFun": "Family fun", "AddFamilyFun": "Add family fun", "EditFamilyFun": "Edit family fun", "DeleteFamilyFun": "Delete family fun", "nightLife": "Night life", "AddNightLife": "Add night life", "EditNightLife": "Edit night life", "DeleteNightLife": "Delete night life", "AddTransport": "Add transport", "EditTransport": "Edit transport", "DeleteTransport": "Delete transport", "AddTravelAgencies": "Add Travel Agency", "EditTravelAgencies": "Edit Travel Agency", "DeleteTravelAgencies": "Delete Travel Agency", "AddKindergarden": "Add kindergarden", "EditKindergarden": "<PERSON> kindergarden", "DeleteKindergarden": "Delete kindergarden", "AddNursery": "Add nursery", "EditNursery": "Edit nursery", "DeleteNursery": "Delete nursery", "AddChildNutritionCenter": "Add Child Nutrition Center", "EditChildNutritionCenter": "Edit Child Nutrition Center", "DeleteChildNutritionCenter": "Delete Child Nutrition Center", "AddSchool": "Add School", "EditSchool": "Edit School", "DeleteSchool": "Delete School", "AddUniversity": "Add University", "EditUniversity": "Edit University", "DeleteUniversity": "Delete University", "AddDevelopmentCenter": "Add Personal Development Center", "EditDevelopmentCenter": "Edit Personal Development Center", "DeleteDevelopmentCenter": "Delete Personal Development Center", "developmentCenter": "Personal development center", "variables": "Variables", "AddVariables": "Add variables", "value": "Value", "type": "Type", "createdBy": "Created By", "createdDate": "Created Date", "lastModifiedBy": "Last Modified By", "lastModifiedDate": "Last Modified Date", "deletedBy": "Deleted By", "deletedDate": "Deleted Date", "deleted": "Deleted", "showOnlyDeleted": "Show only deleted", "notDeleted": "Active", "All": "All", "EditVariables": "Edit variables", "DeleteVariable": "Delete variables", "RecoredRestoredSuccessfully": "Record was restores successfully!", "AddSportFacility": "Add Sport Facility", "EditSportFacility": "Edit Sport Facility", "DeleteSportFacility": "Delete Sport Facility", "AddPharmacy": "Add Pharmacy", "EditPharmacy": "Edit Pharmacy", "DeletePharmacy": "Delete Pharmacy", "AddMedicalEstablishment": "Add Medical Establishment", "EditMedicalEstablishment": "Edit Medical Establishment", "DeleteMedicalEstablishment": "Delete Medical Establishment", "AddDoctorsOffice": "Add Doctor's Office", "EditDoctorsOffice": "Edit Doctor's Office", "DeleteDoctorsOffice": "Delete Doctor's Office", "AddMedicalLaboratory": "Add Laboratory", "EditMedicalLaboratory": "Edit Laboratory", "DeleteMedicalLaboratory": "Delete Laboratory", "AddVeterinaryClinic": "Add Veterinary Clinic", "EditVeterinaryClinic": "Edit Veterinary Clinic", "DeleteVeterinaryClinic": "Delete Veterinary Clinic", "AddRestaurant": "Add Restaurant", "EditRestaurant": "Edit Restaurant", "DeleteRestaurant": "Delete Restaurant", "AddCoffee": "Add Coffee", "EditCoffee": "Edit Coffee", "DeleteCoffee": "Delete Coffee", "AddBar": "Add Bar", "EditBar": "Edit Bar", "DeleteBar": "Delete Bar", "AddPastryShop": "Add Pastry Shop", "EditPastryShop": "Edit Pastry Shop", "DeletePastryShop": "Delete Pastry Shop", "AddGasStation": "Add Gas Station", "EditGasStation": "Edit Gas Station", "DeleteGasStation": "Delete Gas Station", "AddShop": "Add Trade", "EditShop": "Edit Trade", "DeleteShop": "Delete Trade", "AddHotel": "Add Hotel", "EditHotel": "Edit Hotel", "DeleteHotel": "Delete Hotel", "AddGuestHouse": "Add Guest House", "EditGuestHouse": "Edit Guest House", "DeleteGuestHouse": "Delete Guest House", "AddBank": "Add Bank", "EditBank": "Edit Bank", "DeleteBank": "Delete Bank", "AddCurrencyChange": "Add Currency Change", "EditCurrencyChange": "Edit Currency Change", "DeleteCurrencyChange": "Delete Currency Change", "AddInsuranceCompany": "Add Insurance Company", "EditInsuranceCompany": "Edit Insurance Company", "DeleteInsuranceCompany": "Delete Insurance Company", "AddATM": "Add ATM", "EditATM": "Edit ATM", "DeleteATM": "Delete ATM", "AddBioShop": "Add Bio Shop", "EditBioShop": "Edit Bio Shop", "DeleteBioShop": "Delete Bio Shop", "AddFarm": "Add Farm", "EditFarm": "Edit Farm", "DeleteFarm": "Delete Farm", "AddRecycleCenter": "Add Recycle Center", "EditRecycleCenter": "Edit Recycle Center", "DeleteRecycleCenter": "Delete Recycle Center", "AddEcologyInitiative": "Add Eco Initiative", "EditEcologyInitiative": "Edit Eco Initiative", "DeleteEcologyInitiative": "Delete Eco Initiative", "AddMuseum": "Add Museum", "EditMuseum": "Edit Museum", "DeleteMuseum": "Delete Museum", "AddTheater": "Add Theater", "EditTheater": "Edit Theater", "DeleteTheater": "Delete Theater", "AddGallery": "Add Gallery", "EditGallery": "Edit Gallery", "DeleteGallery": "Delete Gallery", "AddCelebration": "Add celebration", "EditCelebration": "Edit celebration", "DeleteCelebration": "Delete celebration", "reports": "Reports", "DangerousBuildingOrSite": "Dangerous Building or Site", "StreetLight": "Street Light", "Trash": "Trash", "DangerousPlayground": "Dangerous Playground", "Pothole": "Pothole", "BrokenOrMissingSign": "Broken or Missing Sign", "IllegalParking": "Illegal Parking", "HangingCables": "Hanging Cables", "WaterLeakage": "Water Leakage", "FallenTree": "Fallen Tree", "CollapsedRoad": "Collapsed Road", "IllegalBuilding": "Illegal Building", "TrafficLightNotWorking": "Traffic Light Not Working", "StrayDogs": "Stray Dogs", "PublicOrder": "Public Order", "AbandonedCar": "Abandoned Car", "Other": "Other", "typeOfReport": "Type of report", "title": "Title", "contactName": "Contact Name", "address": "Address", "reportedAt": "Reported At", "DeleteReport": "Delete Report", "PleaseAddAreaPoints": "Please add area points!", "enterName": "Enter name", "Import": "Import", "Importing": "Importing...", "ImportRestaurants": "Import restaurants", "ImportSuccessfully": "Import successfully!", "ImportFailed": "Import failed!", "ImportItems": "Import", "PleaseSelectValidExcelFile": "Please select valid Excel file!", "announcements": "Announcements", "AddAnnouncement": "Add announcement", "AddingAnnouncement": "Adding announcement", "EditAnnouncement": "Edit announcement", "DeleteAnnouncement": "Delete announcement"}