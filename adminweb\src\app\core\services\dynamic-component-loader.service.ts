import { Injectable, Injector, ComponentRef, ViewContainerRef, Type } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class DynamicComponentLoaderService {
  constructor(private injector: Injector) {}

  public loadComponent(component: Type<any>, container: HTMLElement, viewContainerRef: ViewContainerRef, inputs: any = {}): ComponentRef<any> {
    // Clear existing views to avoid attaching multiple views to the same container
    viewContainerRef.clear();

    const componentRef = viewContainerRef.createComponent(component);

    Object.assign(componentRef.instance, inputs);

    const domElem = (componentRef.hostView as any).rootNodes[0] as HTMLElement;
    container.appendChild(domElem);

    return componentRef;
  }

  public unloadComponent(componentRef: ComponentRef<any>): void {
    componentRef.destroy();
  }
}
