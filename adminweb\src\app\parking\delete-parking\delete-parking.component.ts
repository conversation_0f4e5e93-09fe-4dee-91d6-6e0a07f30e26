import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { ParkingService } from '../../core/services/parking.service';
import { MessageService } from '../../core/services/message.service';

@Component({
  selector: 'app-delete-parking',
  standalone: true,
  imports: [
  TranslateModule,
  MatIconModule,
  MatDialogModule
  ],
  templateUrl: './delete-parking.component.html',
  styleUrl: './delete-parking.component.css'
})
export class DeleteParkingComponent {
  constructor(
    public dialogRef: MatDialogRef<DeleteParkingComponent>,
    private parkingService: ParkingService,
    private messageService: MessageService,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) { }

  onNoClick(): void {
    this.dialogRef.close();
  }

  onYesClick(): void {
     this.parkingService.deleteParking(this.data.id).subscribe(
      {
        next: (response) => {
          this.messageService.showMessage(["DeleteSuccessfully"], 'success');
          this.dialogRef.close(true);
        },
        error: (error: any) => {
          console.error('Error deleting zone:', error);
          // Handle error as per your application's requirement
        }
      }
    );
  }
}
