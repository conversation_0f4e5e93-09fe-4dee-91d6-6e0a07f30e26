import { Component } from '@angular/core';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatFormFieldModule } from '@angular/material/form-field';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { Router, RouterModule } from '@angular/router';
import { MatSnackBar } from '@angular/material/snack-bar';
import { AuthService } from '../core/services/auth.service';
import { TokenHelperService } from '../core/services/token-helper.service';
import { MessageService } from '../core/services/message.service';

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [
    ReactiveFormsModule, // Import ReactiveFormsModule here
    MatIconModule,
    MatInputModule,
    MatButtonModule,
    MatCheckboxModule,
    MatFormFieldModule,
    TranslateModule,
    RouterModule
  ],
  templateUrl: './login.component.html',
  styleUrl: './login.component.css'
})
export class LoginComponent {
  loginForm: FormGroup;

  constructor(private fb: FormBuilder, private authService: AuthService, private router: Router, private translate: TranslateService, private snackBar: MatSnackBar, private tokenHelper: TokenHelperService,
  private messageService: MessageService
  ) {
    this.loginForm = this.fb.group({
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required, Validators.minLength(6)]],
      rememberMe: [false]
    });
  }

  onSubmit() {

    if (this.loginForm.valid) {
 
      const email = this.loginForm.get('email')?.value;
      const password = this.loginForm.get('password')?.value;

      this.authService.login(email, password).subscribe({
        next: (response) => {
         
          const role = this.tokenHelper.getRoleFromToken(response.accessToken);

          if(!role) {
            this.messageService.showMessage(['Unauthorized access. Please login again.'], 'error')
            this.authService.logout();
            this.router.navigate(['/login']); 
            return;
          }

      
          if (response.isSuccessful === true) {
            this.authService.setToken(response.accessToken);
            this.router.navigate(['/news']); // Redirect to home or another page on successful login
          } else {
            this.translate.get('LOGIN_FAILED_MESSAGE').subscribe((translatedMessage: string) => {
              this.snackBar.open(translatedMessage, this.translate.instant('CLOSE'), {
                duration: 3000,
                panelClass: ['error-snackbar']
              });
            });
          }
        },
        error: (err) => {
          console.error('Login error', err);
          this.translate.get('LOGIN_FAILED_MESSAGE').subscribe((translatedMessage: string) => {
            this.snackBar.open(translatedMessage, this.translate.instant('CLOSE'), {
              duration: 3000,
              panelClass: ['error-snackbar']
            });
          });
        }
      });
    }
  }
}
