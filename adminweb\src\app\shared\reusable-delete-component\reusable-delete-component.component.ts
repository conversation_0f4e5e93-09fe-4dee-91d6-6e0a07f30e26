import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { InterestPointsService } from '../../core/services/interest-points.service';
import { MessageService } from '../../core/services/message.service';

@Component({
  selector: 'app-reusable-delete-component',
  standalone: true,
  imports: [
  TranslateModule,
  MatIconModule,
  MatDialogModule
  ],
  templateUrl: './reusable-delete-component.component.html',
  styleUrl: './reusable-delete-component.component.css'
})
export class ReusableDeleteComponentComponent {
  constructor(
    public dialogRef: MatDialogRef<ReusableDeleteComponentComponent>,
    private interestPointsService: InterestPointsService,
    private messageService: MessageService,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) { }

  onNoClick(): void {
    this.dialogRef.close();
  }

  onYesClick(): void {
    this.interestPointsService.deleteInterestPoint(this.data.id).subscribe(
      {
        next: (response) => {
          this.messageService.showMessage(["DeleteSuccessfully"], 'success');
          this.dialogRef.close(true);
        },
        error: (error: any) => {
          console.error('Error deleting interest point:', error);
          // Handle error as per your application's requirement
        }
      }
    );
  }
}
