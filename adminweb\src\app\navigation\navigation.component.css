.sidebar {
    transition: width 0.3s ease;
    margin-top: -8px;
    overflow: hidden;
    height: 93%;
  }
  
  /*Collapse/Expand dynamic resize logic*/
  .sidebar.collapsed {
    width: 120px;
  }
  
  .sidebar.expanded {
    width: 283px;
  }
  
  .mat-drawer-inner-container {
    width: 100%;
    height: 90%;
    overflow: auto;
  }
  
  .content {
    margin-left: 270px !important;  /* very important to keep this margin the same as in sidebar.expanded;  used for dynamic resize */
    transition: margin-left 0.3s ease;
  }
  
  .content-collapsed {
   margin-left: 120px !important; /* very important to keep this margin the same as in sidebar.collapsed; used for dynamic resize */
  }
  /************************************/
  
  .menu-selection-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px;
  }
  
  
  /* Increase the font size for menu items */
  .menu-selection-item .text {
    size: 20px;  /* Change this value to adjust the font size */
    font-weight: 700;
    line-height: 16px;
    color: var(--secondary-color); /* Set the color to a pale black (gray) */
  }
  
  .menu-selection-item mat-icon {
    border: 2px;
    color: var(--secondary-color); /* Default icon color */
  }
  
  
  mat-sidenav-container {
    height: 100%;
  }
  
  .mat-toolbar-row {
    display: flex;
    align-items: center;
  }
  
  .mat-toolbar h1 {
    margin-left: -8px;
    font-size: 30px;
    color: #000;
  }
  
  .mat-toolbar button {
    margin-right: 12px;
  }
  
  
  .toolbar-title {
    flex-grow: 1; /* Ensures the title takes up remaining space */
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: white;
  }
  
  .navigation-bar-left{
    display: flex;
    align-items: center;
  }
  
  .navigation-bar-right{
    display: flex;
  }
  
  .admin-text {
    font-size: 1em; /* Adjust font size */
    margin-left: 2px; /* Adjust spacing */
    line-height: 1em; /* Match line height to height of the divider */
    color: #000;
  }
  
  .logo-container {
    position: relative; /* Ensure the logo is positioned relative to this container */
    width: 100%; /* Make it responsive across the screen */
    height: auto;
    display: flex;
    justify-content: flex-end; /* Aligns the logo to the right */
  }
  
  .logo{
    margin-right: 10px;
    width: 43px;
    display: flex;
    justify-content: space-between;
  }
  
  
  .tool-bar{
  background-color: #fff;
  display: flex;
  position: relative;
  align-items: center;
  padding: 0 16px; /* Adjust padding as needed */
  border-bottom: 2px solid #ccc;
  }
  
  .tool-bar button mat-icon {
    color: #000; /* Adjust this color as needed */
  }
  
  .toolbar-separator {
    margin: 0 14px; /* Adjust margin as per your design */
    color: #000; /* Adjust color as needed */
  }
  
  .language-dropdown {
    position: relative;
    display: inline-block;
    margin-left: auto;
  }
  
  .selected-language {
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    color: #000;
    size: 20px;
    transition: opacity 0.5s ease-in-out;
  }
  
  .fade-out {
    opacity: 0;
  }
  
  .fade-in {
    opacity: 1;
  }
  
  .toolbar-line {
    position: absolute;
    bottom: 1;
    left: 0;
    width: 100%;
    height: 1px; /* Adjust line thickness as needed */
    background-color: var(--primary-color); /* Adjust line color as needed */
    margin-top: 49px;
  }
  
  .is-active {
    background-color: #D9D9D9;
    .menu-selection-item .text{
    color: var(--primary-color);
    }
  
    .menu-selection-item mat-icon{
    color: var(--primary-color);
    }
  
    .submenu-selection-item .text{
      color: var(--primary-color);
    }
  
    .submenu-selection-item mat-icon{
      color: var(--primary-color);;
    }
  }
  
  .submenu-selection-item{
  size: 20px;  /* Change this value to adjust the font size */
  font-weight: 700;
  line-height: 16px;
  color: var(--secondary-color); /* Set the color to a pale black (gray) */
  display: flex;
  gap: 10px;
  }
  
  .sub-menu mat-icon{
  margin-left: 20px;
  margin-top: -2px;
  }
  
  .sub-menu .text{
  margin-top: 2.5px;
  }
  
  .events-sub-menu mat-icon{
    margin-left: 30px;
    margin-top: -2px;
  }
  
  .business-sub-sub-menu mat-icon{
    margin-left: 50px;
    margin-top: -2px;
  }

  .nested-single-menus mat-icon{
  padding-right: 20px;
  margin-left: 20px;
  }
  
  .expandable-menu-selection-item {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 5px;
  size: 20px;  /* Change this value to adjust the font size */
  font-weight: 700;
  line-height: 16px;
  color: var(--secondary-color); /* Set the color to a pale black (gray) */
  }
  
  .administration-menu-icon{
  padding-right: 25px;
  }
  
  .menu-item{
  border-bottom: 1px solid var(--color-gray-border) !important;
  }
  
  .show-submenu {
  display: block;
  }
  
  .menu-button.rotated {
  transform: rotate(180deg);
  }
  
  .menu-button {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  transition: 300ms ease-in-out;
  transform: rotate(0deg);
  margin-left: auto;
  }
  
  
  .language-button{
  transition: 300ms ease-in-out;
  transform: rotate(0deg);
  }
  
  .language-button.rotated {
  transform: rotate(180deg);
  }
  
  .nestedmenus .expandable-menu-selection-item  {
  padding-right: 20px;
  margin-left: 15px;
  }
  
  .navigation-login-icon{
    padding-top: 5px;
  }
  
  .navigation-login-icon a{
    color: #000;
    margin-right: 30px;
    padding-top: 5px;
  }
  
  
  
  /* Mobile responsive adjustments */
  @media (max-width: 768px) {
    .sidebar.expanded {
        width: 100%;
    }
  
    .content {
        margin-left: 0 !important;
    }
  
    .content-collapsed {
        margin-left: 120px !important;
    }
  
    .menu-selection-item .text {
        font-size: 16px;
    }
  
    .mat-toolbar h1 {
      font-size: 24px; /* Example adjustment for smaller screens */
      color: #000;
  }
  
  .language-dropdown {
  margin-left: auto;
  }
  
  .admin-text {
      font-size: 16px; /* Example adjustment for smaller screens */
  }
  
   .toolbar-title {
      display: flex;
      justify-content: space-between;
      flex-grow: 1;
      font-size: 0.8em;
      /* Other properties like align-items and color can remain as per your design */
  }
  
  
  
  .mat-sidenav-container {
    height: 100%; /* Ensures the sidebar and content area take full height */
    overflow: auto; /* Enables scrolling if content exceeds viewport height */
  }
  
    .expandable-menu-selection-item {
        font-size: 16px;
    }
  }
  