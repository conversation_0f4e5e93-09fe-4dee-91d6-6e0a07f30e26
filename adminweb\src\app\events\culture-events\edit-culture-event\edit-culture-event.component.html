<form [formGroup]="editCultureEventForm">
    <div class="wrapper">
      <div class="header">
        <button mat-raised-button (click)="onBack()">
          <mat-icon>arrow_back</mat-icon>
          <span>{{ 'Back' | translate }}</span>
        </button>
        <h1 class="h1">{{"EditCultureEvent" | translate}}</h1>
        <div style="width: 100px;"></div>
      </div>
      <mat-divider></mat-divider>
      <div class="main-content">
        <div class="form-container">
          <div class="filter-container">
             
            <!-- ID -->
            <mat-form-field appearance="outline" class="id-field disabled-field">
              <mat-label>{{ 'ID' | translate }}</mat-label>
              <input matInput formControlName="id" readonly>
            </mat-form-field>
            
            <!-- heading -->
          <mat-form-field appearance="outline" class="tabColumn-1-fields heading">
            <mat-label>{{'heading' | translate}}</mat-label>
            <input matInput formControlName="heading" maxlength="60">
            <mat-hint align="end">{{ 60 - (editCultureEventForm.controls['heading'].value?.length || 0) }} {{ 'charactersRemaining' | translate }}</mat-hint>
        </mat-form-field>

         <!-- Date and Time Container -->
        <div class="date-time-container">
          <!-- date -->
          <mat-form-field appearance="outline" class="tabColumn-1-fields date">
            <mat-label>{{'Date' | translate}}</mat-label>
            <input matInput formControlName="date" [matDatepicker]="toPicker">
            <mat-datepicker-toggle matSuffix [for]="toPicker"></mat-datepicker-toggle>
            <mat-datepicker #toPicker></mat-datepicker>
          </mat-form-field>

          <!-- time -->
          <mat-form-field appearance="outline" class="tabColumn-1-fields time">
            <mat-label>{{'Time' | translate}}</mat-label>
            <input matInput 
                   formControlName="time" 
                   placeholder="HH:MM"
                   maxlength="5"
                   (input)="onTimeInput($event)"
                   (keydown)="onTimeKeydown($event)"
                   (click)="onTimeClick($event)">
            <mat-icon matSuffix>access_time</mat-icon>
          </mat-form-field>
        </div>


          <!-- Translated Languages -->
          <div class="translated-languages" *ngIf="translatedLanguages.length > 0">
            <div class="language-buttons">
              <div *ngFor="let translation of translatedLanguages" class="language-button">
                <button mat-raised-button (click)="openTranslationModal(translation.languageShort)">
                  {{ translation.languageFullName | translate }}
                </button>
                <button mat-icon-button (click)="removeTranslation(translation.languageShort)">
                  <mat-icon>close</mat-icon>
                </button>
              </div>
            </div>
          </div>
            
            <div class="add-button-container">
              <!-- Photo Upload -->
              <button mat-raised-button class="add-button" (click)="openFileDialog()">
                <mat-icon>file_download_outline</mat-icon>
                <span>{{ 'AddPhotos' | translate }}</span>
              </button>
  
               <!-- Translation -->
          <button mat-raised-button class="translate-button" (click)="openTranslationModal()">
            <mat-icon>translate</mat-icon>
            <span>{{ 'Translate' | translate }}</span>
          </button>
  
          <!-- Slide Toggle to Show/Hide Images -->
          <mat-slide-toggle class="mat-slide-button" [(ngModel)]="showImages" [ngModelOptions]="{standalone: true}" aria-label="Toggle Images">
            {{ showImages ? ('hideImages' | translate) : ('showImages' | translate) }}
          </mat-slide-toggle>
              
              <input type="file" #fileInput accept="image/*" (change)="onFileSelected($event)" style="display: none;" multiple />
            </div>
            
           <!-- Display uploaded images with remove buttons and simplified thumbnail selection -->
            <div class="image-container" *ngIf="showImages && imageSrcs.length > 0">
              <h3>{{ "Photos:" | translate }}</h3>
              <div class="uploaded-images">
                <div *ngFor="let imageSrc of imageSrcs; let i = index" class="image-wrapper">
                  <div class="image-box" 
                      [class.selected-thumbnail]="selectedThumbnailIndex === i"
                      [attr.data-label]="'Cover' | translate"
                      (click)="selectThumbnail(i)">
                    <img [src]="imageSrc.content" alt="Uploaded Photo" class="uploaded-image">
                    <button mat-icon-button class="remove-icon" (click)="removePhoto(i); $event.stopPropagation()">
                      <mat-icon>close</mat-icon>
                    </button>
                  </div>
                </div>
              </div>
            </div>
  
          <div class="example-card">
            <mat-card>
            <mat-card-header>
              <mat-card-title class="card-content-title">{{ 'Content' | translate }}</mat-card-title>
            </mat-card-header>
            <mat-card-content class="card-content">
              <app-editor (focusout)="onChangeContent()" [(content)]="content" [disabled]="false"></app-editor>
            </mat-card-content>
            </mat-card>
            </div>
  
           
            <div class="crud-buttons">
            <button mat-raised-button tabindex="1" class="save-button" (click)="onSubmit()">
              <span>{{ 'Save' | translate }}</span>
          </button>
          <button mat-raised-button tabindex="1" class="cancel-button" (click)="onBack()">
              <span>{{ 'Cancel' | translate }}</span>
          </button>
          </div>
  
          </div>
        </div>
  
      </div>
    </div>
  </form>