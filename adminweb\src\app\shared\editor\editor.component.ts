import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, OnChanges, OnDestroy, OnInit, Output } from '@angular/core';
import { FormControl, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { MatError } from '@angular/material/form-field';
import { TranslateModule } from '@ngx-translate/core';
import { Editor, NgxEditorModule, toHTML, Toolbar, Validators } from 'ngx-editor';

@Component({
  selector: 'app-editor',
  standalone: true,
  imports: [
    ReactiveFormsModule,
    NgxEditorModule,
    MatError,
    TranslateModule,
    CommonModule
  ],
  templateUrl: './editor.component.html',
  styleUrl: './editor.component.css'
})
export class EditorComponent implements OnInit, OnDestroy, OnChanges {
  editor!: Editor;
  form: FormGroup;
  html = '';
  count = 0;
  @Input() disabled = false;
  @Input() content = '';
  @Output() contentChange: EventEmitter<string> = new EventEmitter<string>();
  toolbar: Toolbar = [
    ["bold", "italic"],
    ["code", "blockquote"],
    ["underline", "strike"],
    ["ordered_list", "bullet_list"],
    [{ heading: ["h1", "h2", "h3", "h4", "h5", "h6"] }],
    ["link", "image"],
    ["text_color", "background_color"],
    ["align_left", "align_center", "align_right", "align_justify"],
  ];

  constructor() {
    this.form = new FormGroup({
        editorContent: new FormControl('<p></p>', [Validators.required()])
    });
  }

  ngOnInit(): void {
    this.editor = new Editor();
    this.form.setValue({editorContent: this.content});
  }

  ngOnChanges() {
    if (this.disabled) {
        this.form.setValue({editorContent: ''});
        this.contentChange.emit('');
        this.form.get('editorContent')?.disable();
    } else {
        this.form.get('editorContent')?.enable();
    }
  }

  onFocusoutChange(content: object) {
    const type = this.form.get('editorContent')!.value?.type;
    if (type !== 'doc' || type === undefined) {
        this.content = this.form!.get('editorContent')!.value;
    } else {
        this.content = toHTML(this.form.get('editorContent')!.value);
    }
    this.contentChange.emit(this.content);
  }

  ngOnDestroy() {
    this.editor?.destroy();
  }
}
