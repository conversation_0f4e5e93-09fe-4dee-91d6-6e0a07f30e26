import { TextFieldModule } from '@angular/cdk/text-field';
import { CommonModule } from '@angular/common';
import { Component, DestroyRef, inject, Inject, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatOptionModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { TranslateModule } from '@ngx-translate/core';
import { UsersService } from '../../core/services/user.service';
import { MatTooltipModule } from '@angular/material/tooltip';
import { tap } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { MessageService } from '../../core/services/message.service';
import { AuthService } from '../../core/services/auth.service';

@Component({
  selector: 'app-reset-password-users',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    MatIconModule,
    MatDialogModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatDatepickerModule,
    MatSelectModule,
    MatOptionModule,
    TextFieldModule,
    MatTooltipModule
  ],
  templateUrl: './reset-password-users.component.html',
  styleUrl: './reset-password-users.component.css'
})
export class ResetPasswordUsersComponent {
  private destroyRef = inject(DestroyRef);

  resetPasswordForm: FormGroup;
  

  protected passwordVisible = false;

  constructor(
    public dialogRef: MatDialogRef<ResetPasswordUsersComponent>,
    private fb: FormBuilder, private usersService: UsersService,
    private messageService: MessageService,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.resetPasswordForm = this.fb.group({
    newPassword:     [''],
    confirmPassword: [''],
    id:              [this.data.id]
    });
  }

  onClose(): void {
    this.dialogRef.close();
  }

  onSubmit(): void {
    if (this.resetPasswordForm.valid) {
      const changePassword = this.resetPasswordForm.value;
      this.usersService.resetPassword(changePassword, this.data.id).subscribe({
        next: (response) => {
          // Handle the response if needed
          this.dialogRef.close(changePassword);
        },
        error: (error) => {
          // Handle error
          console.error('Error reseting password:', error);
          // Optionally, display an error message to the user
        }
      });
    }
  }

  generatePassword() {
   this.usersService.getGeneratedPassword()
         .pipe(
           tap((data) => {
           if(data) {
            this.resetPasswordForm.patchValue({
              newPassword: data.password,
              confirmPassword: data.password
            });

            this.messageService.showMessage(["PasswordGeneratedSuccessfully"], 'success');
           }
           }),
           takeUntilDestroyed(this.destroyRef)
         )
         .subscribe();
  }

  togglePasswordVisibility() {
    this.passwordVisible = !this.passwordVisible;
  }
}
