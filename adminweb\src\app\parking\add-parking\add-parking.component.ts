import { CommonModule } from '@angular/common';
import { ChangeDetector<PERSON><PERSON>, <PERSON>mponent, <PERSON>ement<PERSON>ef, On<PERSON>estroy, OnInit, QueryList, ViewChild, ViewChildren, ViewContainerRef } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatDialogModule } from '@angular/material/dialog';
import { MatDividerModule } from '@angular/material/divider';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { GoogleMapsModule } from '@angular/google-maps';
import { ActivatedRoute, Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { GoogleMapsService } from '../../core/services/google.maps.service';
import { MapGetModel } from '../../shared/interfaces/map/map-get.model';
import { environment } from '../../../environments/enviroment';
import { ParkingService } from '../../core/services/parking.service';
import { ParkngSimpleBorderPoint } from '../../shared/interfaces/parking/parking-simple-border-point.model';
import { CreateParkingModel } from '../../shared/interfaces/parking/parking-create-model';
import { MessageService } from '../../core/services/message.service';
import { MatFormFieldModule } from '@angular/material/form-field';
import { FormsModule } from '@angular/forms';
import { MatInputModule } from '@angular/material/input';

@Component({
  selector: 'app-add-parking',
  standalone: true,
  imports: [
  TranslateModule,
  MatIconModule,
  MatDialogModule,
  GoogleMapsModule,
  CommonModule,
  MatDividerModule,
  MatButtonModule,
  MatFormFieldModule,
  FormsModule,
  MatInputModule
  ],
  templateUrl: './add-parking.component.html',
  styleUrl: './add-parking.component.css'
})
export class AddParkingComponent {
  @ViewChild('mapContainer') mapContainer!: ElementRef;
  @ViewChild('markerContainer', { read: ViewContainerRef, static: true }) markerContainer!: ViewContainerRef;
  @ViewChildren('borderPointRow') borderPointRows!: QueryList<ElementRef>;

  constructor(
    private googleMapsService: GoogleMapsService,
    private route: ActivatedRoute,
    private router: Router,
    private parkingService: ParkingService,
    private changeDetectorRef: ChangeDetectorRef,
    private messageService: MessageService
  ) {}

  protected data: { name: string, radius: number | string | null, borderPoints: any[] } = { name: '', radius: null, borderPoints: [] };
  protected mapHeight: number = 0;
  protected mapWidth: number = 0;
  protected center!: google.maps.LatLngLiteral;
  protected zoom = 13;
  private map: google.maps.Map | undefined;
  private dayMapId = environment.dayMapId;
  //private aggregatedGroupsData: AggregatedGroupModel[] = [];
  private marker: google.maps.marker.AdvancedMarkerElement | undefined;
  private currentMarker: any = null;
  protected clickedCoordinates: { latitude: number, longitude: number, index: number}[] = [];
  private currentMarkers: google.maps.marker.AdvancedMarkerElement[] = [];
  private polyline: google.maps.Polyline | undefined;
  private markerPositions: { latitude: number; longitude: number }[] = [];
  protected parkingId!: number;
  protected highlightedIndex: number | null = null;
  protected parkingRadius!: number;
  protected firstMarkerIcon = {
    url: 'assets/images/end-point-marker.svg',
  };
  protected lastMarkerIcon = {
    url: 'assets/images/start-point-marker.svg',
  };
  private isFirstTime = true;

  private parkingDataSubscription!: Subscription;

 async ngOnInit() {
   this.initializeGoogleMaps();
  
  }

  private updatePolylinePath() {
    if (this.polyline) {
      const path = this.clickedCoordinates.map(coord => new google.maps.LatLng(coord.latitude, coord.longitude));
      this.polyline.setPath(path);
    }
  }

  private scrollToHighlighted() {
    
    if (this.highlightedIndex !== null) {
      const element = this.borderPointRows.toArray()[this.highlightedIndex].nativeElement;
      element.scrollIntoView({ behavior: 'smooth', block: 'nearest', inline: 'start' });
      this.changeDetectorRef.detectChanges();
    }
      
  }

  addBorderPoints() {
    const midpoint = this.googleMapsService.calculateMidpoint(
      this.clickedCoordinates.map(coord => ({ lat: coord.latitude, lng: coord.longitude }))
  );

   
      const borderPoints: ParkngSimpleBorderPoint[] = this.clickedCoordinates.map(coord => ({
        index: coord.index,
        latitude: coord.latitude,
        longitude: coord.longitude
      }));

      const createParkingModel: CreateParkingModel = {
        name: this.data.name,
        borderPoints: borderPoints
      };

    this.parkingService.addParking(createParkingModel).subscribe({
      next: () => {
        this.messageService.showMessage(["CreateSuccessfully"], 'success');
        this.router.navigate(['/parking']);
      },
      error: (err) => {
        console.error('Error adding border points:', err);
      }
    });
    
  }

  deleteBorderPoint(index: number) {
    console.log(this.clickedCoordinates)
    const removedPoint = this.clickedCoordinates.splice(index, 1)[0];
    console.log(this.clickedCoordinates)

    const markerIndex = this.markerPositions.findIndex(position =>
      position.latitude === removedPoint.latitude && position.longitude === removedPoint.longitude
    );

    if (markerIndex > -1) {
      this.currentMarkers[markerIndex].map = null;  
      this.currentMarkers.splice(markerIndex, 1);
      this.markerPositions.splice(markerIndex, 1);
    }

    // Update indexes of remaining points and markers
    this.clickedCoordinates.forEach((coord, i) => {
      coord.index = i; // Realign indexes
    });


    if (this.highlightedIndex === index) {
      this.highlightedIndex = null;
    } else if (this.highlightedIndex !== null && this.highlightedIndex > index) {
      this.highlightedIndex--;
    }

    this.changeDetectorRef.detectChanges();

    this.updatePolylinePath();
  }

  async initializeGoogleMaps() {
    try {
      await this.googleMapsService.loadLibraries();

      this.center = {
        lat: 42.482798, 
        lng: 26.503206 

      };

      this.map = await this.googleMapsService.initializeMap(
        this.mapContainer.nativeElement,
        this.center,
        this.zoom,
        this.dayMapId,
        'day'
      );

      this.polyline = new google.maps.Polyline({
        map: this.map,
        path: [],
        strokeColor: '#800080',
        strokeOpacity: 1.0,
        strokeWeight: 2,
      });

      this.clickedCoordinates.forEach((points: { latitude: number; longitude: number }, index: number) => {
        let pointsGoogle: google.maps.LatLngLiteral = { lat: points.latitude, lng: points.longitude };

        let iconUrl: string | undefined = undefined;
        if (index === 0) {
          iconUrl = this.firstMarkerIcon.url;
        } else if (index === this.clickedCoordinates.length - 1 && index !== 0) {
          iconUrl = this.lastMarkerIcon.url;
        }

        const newMarker = this.googleMapsService.addAdvancedMarker(
          pointsGoogle,
          index,
          undefined,
          undefined,
          iconUrl,
          undefined,
          true
        );

        newMarker.addListener('click', () => {
          this.onMarkerClick(points.latitude, points.longitude);
        });

        this.currentMarkers.unshift(newMarker); // Using push here
        this.markerPositions.unshift({ latitude: points.latitude, longitude: points.longitude });
      });

      this.updatePolylinePath();

      this.map.addListener('click', (event: google.maps.MapMouseEvent) => {
        if (!event.latLng) return; // Ensure the click event has coordinates
      
        const clickedPosition: google.maps.LatLngLiteral = {
          lat: event.latLng.lat(),
          lng: event.latLng.lng(),
        };
      
        if (this.currentMarkers.length > 0) {
          if (this.isFirstTime) {
            // First time: Remove the last marker
            const lastMarker = this.currentMarkers[this.currentMarkers.length - 1];
            this.removeMarker(lastMarker);
            this.isFirstTime = false;
          } else {
            // Subsequent times: Remove the first marker
            const firstMarker = this.currentMarkers[0];
            this.removeMarker(firstMarker);
          }
        }
      
        // Create new marker at clicked position
        this.currentMarker = this.createMarker(clickedPosition, this.firstMarkerIcon.url);
      
        // Store marker data
        this.currentMarkers.unshift(this.currentMarker);
        this.markerPositions.unshift({ latitude: clickedPosition.lat, longitude: clickedPosition.lng });
      
        this.clickedCoordinates.unshift({
          latitude: clickedPosition.lat,
          longitude: clickedPosition.lng,
          index: 0
        });
      
        // Update indexes
        this.clickedCoordinates.forEach((coord, i) => {
          coord.index = i;
        });
  
        this.changeDetectorRef.detectChanges();
      
        // Add click listener to marker
        this.currentMarker.addListener('click', () => {
          this.onMarkerClick(clickedPosition.lat, clickedPosition.lng);
        });
      
        // Update polyline if applicable
        this.updatePolylinePath();
      });
      
    } catch (error) {
      console.error('Error loading Google Maps:', error);
    }
  }

  onMarkerClick(latitude: number, longitude: number) {
    const index = this.clickedCoordinates.findIndex(point =>
        point.latitude === latitude && point.longitude === longitude
    );

    if (index !== -1) {
        this.highlightedIndex = index;
        this.scrollToHighlighted();
    }
  }


  async ngAfterViewInit(): Promise<void> {
    this.googleMapsService.setViewContainerRef(this.markerContainer);
  }

  protected onBack() {
    this.router.navigate(['/parking']);
  } 
  
  
  private createMarker(position: google.maps.LatLngLiteral, iconUrl: string): google.maps.marker.AdvancedMarkerElement {

    const newMarker = this.googleMapsService.addAdvancedMarker(
      position,
      2,
      undefined,
      undefined,
      iconUrl, 
      undefined,
      true
    );
  
    return newMarker;
  }
    

  private removeMarker(marker: google.maps.marker.AdvancedMarkerElement): void {
    const position = marker.position as google.maps.LatLngLiteral; 

    marker.map = null; 

    const markerIndex = this.currentMarkers.indexOf(marker);
    if (markerIndex > -1) {
      this.currentMarkers.splice(markerIndex, 1);  
      this.markerPositions.splice(markerIndex, 1); 
    }

    const newMarker = this.googleMapsService.addAdvancedMarker(
      position,
      2,
    );
  
    this.currentMarkers.splice(markerIndex, 0, newMarker);

    // Insert the position at the same index in markerPositions array
    this.markerPositions.splice(markerIndex, 0, { latitude: position.lat, longitude: position.lng });

    newMarker.addListener('click', () => {
    this.onMarkerClick(position.lat, position.lng);
  });
    
  }

  ngOnDestroy(): void {
    if (this.parkingDataSubscription) {
      this.parkingDataSubscription.unsubscribe();
    }
  }
}
