import { ApplicationConfig, importProviders<PERSON>rom, LOCALE_ID, provideZoneChangeDetection } from '@angular/core';
import { provideRouter } from '@angular/router';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';
import { routes } from './app.routes';
import { HTTP_INTERCEPTORS, HttpClient, HttpClientModule, provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { TranslateLoader, TranslateModule } from '@ngx-translate/core';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import { BrowserModule } from '@angular/platform-browser';
import { MatTableDataSource } from '@angular/material/table';
import { DatePipe } from '@angular/common';
import { MatPaginatorIntl } from '@angular/material/paginator';
import { DateAdapter, MAT_DATE_FORMATS, MAT_NATIVE_DATE_FORMATS, NativeDateAdapter } from '@angular/material/core';
import { LoaderInterceptor } from './https-interceptors/loader-interceptor';
import { AuthInterceptor } from './core/services/auth-interceptor.service';
import { CustomPaginatorIntl } from './core/services/custom-paginator-intl.service';
import { ngxEditorConfig } from './shared/ngx-editor.config';
import { NgxEditorModule } from 'ngx-editor';
import { JwtModule } from '@auth0/angular-jwt';

export function HttpLoaderFactory(http: HttpClient) {
  return new TranslateHttpLoader(http, './assets/i18n/', '.json');
}

export function tokenGetter() {
  return localStorage.getItem("token");
}

export const appConfig: ApplicationConfig = {
  providers: [
    provideAnimationsAsync(),
    provideRouter(routes),
    provideHttpClient(),
    
    // Use importProvidersFrom for NgxEditorModule
    importProvidersFrom(
      NgxEditorModule.forRoot(ngxEditorConfig)
    ),
    
    { provide: MatPaginatorIntl, useClass: CustomPaginatorIntl },
    { provide: DateAdapter, useClass: NativeDateAdapter },
    { provide: HTTP_INTERCEPTORS, useClass: AuthInterceptor, multi: true },
    { provide: MAT_DATE_FORMATS, useValue: MAT_NATIVE_DATE_FORMATS },
    { provide: HTTP_INTERCEPTORS, useClass: LoaderInterceptor, multi: true },
    { provide: LOCALE_ID, useValue: 'bg' },
    
    // Add BrowserModule and other services
    BrowserModule,
    MatTableDataSource,
    DatePipe,
     
    // TranslateModule configuration
    importProvidersFrom(
      TranslateModule.forRoot({
        defaultLanguage: 'bg',
        loader: {
          provide: TranslateLoader,
          useFactory: HttpLoaderFactory,
          deps: [HttpClient],
        },
      })
    ),

    importProvidersFrom(
      JwtModule.forRoot({
          config: {
              tokenGetter: tokenGetter,
              allowedDomains: ["localhost:44347", "smart-yambol.codeatelier.io"],
              disallowedRoutes: ["http://example.com/examplebadroute/"],
          },
      }),
  ),

  provideHttpClient(
    withInterceptorsFromDi()
  ),

  ],
};
