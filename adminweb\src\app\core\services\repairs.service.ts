import { Inject, Injectable } from '@angular/core';
import { BehaviorSubject, Observable, of } from 'rxjs';
import { HttpClient, HttpParams } from '@angular/common/http';
import { environment } from '../../../environments/enviroment';
import { RepairGetModel } from '../../shared/interfaces/repairs/repair-get.model';
import { RepairGetModelGetByIdModel } from '../../shared/interfaces/repairs/repair-get-by-id.model';

@Injectable({
  providedIn: 'root'
})
export class RepairService {
  private parkingDataSubject = new BehaviorSubject<any>(null);
  parkingData$ = this.parkingDataSubject.asObservable();

  private apiUrl = 'https://localhost:44347/Manufacturers'; // Replace with your actual backend URL

  constructor(private http: HttpClient) {}

   getRepairs(
    search:           string | null,
    fromDate:        string | null,
    toDate:          string | null,
    sortColumn:       string | null,
    sortDirection:    string | null,
    pageNumber:       number,
    pageSize:         number,
  ): Observable<RepairGetModel> {
    return this.http.get<RepairGetModel>(`${environment.apiUrl}/Repairs/Admin`, {
      params: new HttpParams()
      .set('search', search ? search : '')
      .set('fromDate', fromDate ? fromDate : '')
      .set('toDate', toDate ? toDate : '')
      .set('sortColumn', sortColumn ? sortColumn : '')
      .set('sortDirection', sortDirection ? sortDirection : '')
      .set('pageNumber', pageNumber.toString())
      .set('pageSize', pageSize.toString())
    });
  }

 addRepair(formData: FormData) {
    return this.http.post(`${environment.apiUrl}/Repairs`, formData);
  }

  getRepairById(eventId: number): Observable<RepairGetModelGetByIdModel> {
        return this.http.get<RepairGetModelGetByIdModel>(`${environment.apiUrl}/Repairs/${eventId}`);
    }
  

  editRepair(formData: FormData) {
    return this.http.put(`${environment.apiUrl}/Repairs/${formData.get('id')}`, formData);
  }

  deleteRepair(interestPointId: number) {
    return this.http.delete<number>(`${environment.apiUrl}/Repairs/${interestPointId}`)
  }


}