<div class="contentPanel" fxLayout="column" xLayoutAlign="flex-start">
    <div fxLayout="row"
         fxFlex="0 1 100%"
         fxFlex.lt-lg="0 1 100%"
         fxFlex.lt-md="0 1 100%"
         class="panelHead">
          <span class="h1">{{ 'AddingUser' | translate }}</span>
          <button mat-button class="close-icon" [mat-dialog-close]="false">
            <mat-icon>close</mat-icon>
          </button>
    </div>
</div>
<div class="form-container">
  <form [formGroup]="userForm" (ngSubmit)="onSubmit()">
    <mat-dialog-content class="mat-dialog-content-wrapper">
      <div fxLayout="column" fxLayoutAlign="flex-start">
        <div class="tabContent" fxLayout="row wrap" fxLayoutAlign="flex-start">
          <div class="tabColumn" fxLayout="row wrap">

               <!-- email -->
               <mat-form-field appearance="outline" class="tabColumn-2-fields">
                <mat-label>{{'email' | translate}}</mat-label>
                <input matInput formControlName="email">
            </mat-form-field>

             <!-- password -->
             <mat-form-field appearance="outline" class="tabColumn-2-fields">
                <mat-label>{{'Password' | translate}}</mat-label>
                <input matInput formControlName="password" type="password">
            </mat-form-field>

             <!-- confirmPassword -->
             <mat-form-field appearance="outline" class="tabColumn-2-fields">
                <mat-label>{{'RepeatPassword' | translate}}</mat-label>
                <input matInput formControlName="confirmPassword" type="password">
            </mat-form-field>

             <!-- firstName -->
             <mat-form-field appearance="outline" class="tabColumn-3-fields">
                <mat-label>{{'firstName' | translate}}</mat-label>
                <input matInput formControlName="firstName">
            </mat-form-field>

             <!-- lastName -->
             <mat-form-field appearance="outline" class="tabColumn-3-fields">
                <mat-label>{{'lastName' | translate}}</mat-label>
                <input matInput formControlName="lastName">
            </mat-form-field>

            <!-- role -->
            <mat-form-field appearance="outline" class="tabColumn-3-fields">
                <mat-label>{{'role' | translate}}</mat-label>
                <mat-select formControlName="role">
                  <mat-option *ngFor="let role of filteredRoles" [value]="role">
                    {{ role | translate }}
                </mat-option>
                </mat-select>
              </mat-form-field>

          </div>
        </div>
      </div>
    </mat-dialog-content>
    <mat-dialog-actions align="center" class="mat-dialog-content-wrapper">
      <button type="submit" mat-raised-button tabindex="1" class="save">
        <mat-icon>save</mat-icon>
        <span>{{ 'Save' | translate }}</span>
      </button>
      <button mat-raised-button mat-dialog-close tabindex="-1" class="cancel">{{ 'Cancel' | translate }}</button>
    </mat-dialog-actions>
  </form>
</div>