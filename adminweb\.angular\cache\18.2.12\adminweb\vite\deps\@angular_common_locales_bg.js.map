{"version": 3, "sources": ["../../../../../../node_modules/@angular/common/locales/bg.mjs"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n  const n = val;\n  if (n === 1) return 1;\n  return 5;\n}\nexport default [\"bg\", [[\"am\", \"pm\"], u, [\"пр.об.\", \"сл.об.\"]], [[\"am\", \"pm\"], u, u], [[\"н\", \"п\", \"в\", \"с\", \"ч\", \"п\", \"с\"], [\"нд\", \"пн\", \"вт\", \"ср\", \"чт\", \"пт\", \"сб\"], [\"неделя\", \"понеделник\", \"вторник\", \"сряда\", \"четвъртък\", \"петък\", \"събота\"], [\"нд\", \"пн\", \"вт\", \"ср\", \"чт\", \"пт\", \"сб\"]], u, [[\"я\", \"ф\", \"м\", \"а\", \"м\", \"ю\", \"ю\", \"а\", \"с\", \"о\", \"н\", \"д\"], [\"яну\", \"фев\", \"март\", \"апр\", \"май\", \"юни\", \"юли\", \"авг\", \"сеп\", \"окт\", \"ное\", \"дек\"], [\"януари\", \"февруари\", \"март\", \"април\", \"май\", \"юни\", \"юли\", \"август\", \"септември\", \"октомври\", \"ноември\", \"декември\"]], u, [[\"пр.Хр.\", \"сл.Хр.\"], u, [\"преди Христа\", \"след Христа\"]], 1, [6, 0], [\"d.MM.yy 'г'.\", \"d.MM.y 'г'.\", \"d MMMM y 'г'.\", \"EEEE, d MMMM y 'г'.\"], [\"H:mm 'ч'.\", \"H:mm:ss 'ч'.\", \"H:mm:ss 'ч'. z\", \"H:mm:ss 'ч'. zzzz\"], [\"{1}, {0}\", u, u, u], [\",\", \" \", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0%\", \"0.00 ¤\", \"#E0\"], \"BGN\", \"лв.\", \"Български лев\", {\n  \"AFN\": [u, \"Af\"],\n  \"AMD\": [],\n  \"ARS\": [],\n  \"AUD\": [],\n  \"AZN\": [],\n  \"BBD\": [],\n  \"BDT\": [],\n  \"BGN\": [\"лв.\"],\n  \"BMD\": [],\n  \"BND\": [],\n  \"BRL\": [],\n  \"BSD\": [],\n  \"BZD\": [],\n  \"CAD\": [],\n  \"CLP\": [],\n  \"CNY\": [],\n  \"COP\": [],\n  \"CRC\": [],\n  \"CUP\": [],\n  \"DOP\": [],\n  \"FJD\": [],\n  \"FKP\": [],\n  \"GBP\": [u, \"£\"],\n  \"GHS\": [],\n  \"GIP\": [],\n  \"GYD\": [],\n  \"HKD\": [],\n  \"ILS\": [],\n  \"INR\": [],\n  \"JMD\": [],\n  \"JPY\": [u, \"¥\"],\n  \"KHR\": [],\n  \"KRW\": [],\n  \"KYD\": [],\n  \"KZT\": [],\n  \"LAK\": [],\n  \"LRD\": [],\n  \"MNT\": [],\n  \"MXN\": [],\n  \"NAD\": [],\n  \"NGN\": [],\n  \"NZD\": [],\n  \"PHP\": [],\n  \"PYG\": [],\n  \"RON\": [],\n  \"SBD\": [],\n  \"SGD\": [],\n  \"SRD\": [],\n  \"SSP\": [],\n  \"TRY\": [],\n  \"TTD\": [],\n  \"TWD\": [],\n  \"UAH\": [],\n  \"USD\": [\"щ.д.\", \"$\"],\n  \"UYU\": [],\n  \"VND\": [],\n  \"XCD\": [u, \"$\"]\n}, \"ltr\", plural];\n"], "mappings": ";;;AAQA,IAAM,IAAI;AACV,SAAS,OAAO,KAAK;AACnB,QAAM,IAAI;AACV,MAAI,MAAM,EAAG,QAAO;AACpB,SAAO;AACT;AACA,IAAO,aAAQ,CAAC,MAAM,CAAC,CAAC,MAAM,IAAI,GAAG,GAAG,CAAC,UAAU,QAAQ,CAAC,GAAG,CAAC,CAAC,MAAM,IAAI,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,GAAG,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI,GAAG,CAAC,UAAU,cAAc,WAAW,SAAS,aAAa,SAAS,QAAQ,GAAG,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,GAAG,CAAC,OAAO,OAAO,QAAQ,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK,GAAG,CAAC,UAAU,YAAY,QAAQ,SAAS,OAAO,OAAO,OAAO,UAAU,aAAa,YAAY,WAAW,UAAU,CAAC,GAAG,GAAG,CAAC,CAAC,UAAU,QAAQ,GAAG,GAAG,CAAC,gBAAgB,aAAa,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,gBAAgB,eAAe,iBAAiB,qBAAqB,GAAG,CAAC,aAAa,gBAAgB,kBAAkB,mBAAmB,GAAG,CAAC,YAAY,GAAG,GAAG,CAAC,GAAG,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,OAAO,GAAG,GAAG,CAAC,aAAa,UAAU,UAAU,KAAK,GAAG,OAAO,OAAO,iBAAiB;AAAA,EAC36B,OAAO,CAAC,GAAG,IAAI;AAAA,EACf,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC,KAAK;AAAA,EACb,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC,QAAQ,GAAG;AAAA,EACnB,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC,GAAG,GAAG;AAChB,GAAG,OAAO,MAAM;", "names": []}