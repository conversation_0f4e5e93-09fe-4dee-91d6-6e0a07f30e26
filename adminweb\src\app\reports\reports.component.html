<form [formGroup]="reportsFilterForm">
    <div class="wrapper">
      <div class="header">
          <div class="filter-container">
  
          <!-- search-->
          <mat-form-field appearance="outline" class="mat-form-field">
            <mat-label>{{'SearchFilter' | translate}}</mat-label>
            <input matInput formControlName="search">
        </mat-form-field>

         <!-- First Name -->
          <mat-form-field appearance="outline" class="mat-form-field">
            <mat-label>{{'firstName' | translate}}</mat-label>
            <input matInput formControlName="firstName">
          </mat-form-field>

          <!-- Last Name -->
          <mat-form-field appearance="outline" class="mat-form-field">
            <mat-label>{{'lastName' | translate}}</mat-label>
            <input matInput formControlName="lastName">
          </mat-form-field>

          <!-- Type -->
          <mat-form-field appearance="outline" class="mat-form-field">
            <mat-label>{{'typeOfReport' | translate}}</mat-label>
            <mat-select formControlName="type">
               <mat-option></mat-option>
                    <mat-option *ngFor="let type of types" [value]="type">
                        {{ type | translate }}
                    </mat-option>
            </mat-select>
          </mat-form-field>

          <!-- From Date -->
          <mat-form-field appearance="outline" class="mat-form-field">
            <mat-label>{{'FromDate' | translate}}</mat-label>
            <input matInput [matDatepicker]="fromDatePicker" formControlName="fromDate">
            <mat-datepicker-toggle matSuffix [for]="fromDatePicker"></mat-datepicker-toggle>
            <mat-datepicker #fromDatePicker></mat-datepicker>
          </mat-form-field>

          <!-- To Date -->
          <mat-form-field appearance="outline" class="mat-form-field">
            <mat-label>{{'ToDate' | translate}}</mat-label>
            <input matInput [matDatepicker]="toDatePicker" formControlName="toDate">
            <mat-datepicker-toggle matSuffix [for]="toDatePicker"></mat-datepicker-toggle>
            <mat-datepicker #toDatePicker></mat-datepicker>
          </mat-form-field>

  
              <button mat-icon-button type="submit" class="search-button" (click)="onSearch()">
                  <mat-icon>search</mat-icon>
              </button>
          </div>
      </div>
      <mat-divider></mat-divider>
  
      <div class="main-content">
        <div class="title">
            <span class="left-big-border"></span>
            <h1 class="h1">{{ 'reports' | translate }}</h1>
        </div>
      </div>
  
      <div class="card-column">
        <mat-card class="custom-card table-container">
            <div class="mat-elevation-z8 custom-table">
              <table mat-table [dataSource]="dataSource" matSort (matSortChange)="onSortChange($event)">
  
               <!-- ID Column -->
                <ng-container matColumnDef="id">
                <th mat-header-cell *matHeaderCellDef mat-sort-header class="custom-header"> ID </th>
                <td mat-cell *matCellDef="let element" class="custom-cell"> {{element.id}} </td>
                </ng-container>

                <!-- Title Column -->
                <ng-container matColumnDef="title">
                <th mat-header-cell *matHeaderCellDef mat-sort-header class="custom-header"> {{"title" | translate}} </th>
                <td mat-cell *matCellDef="let element" class="custom-cell"> {{element.title}} </td>
                </ng-container>

                <!-- Type Column -->
                <ng-container matColumnDef="type">
                <th mat-header-cell *matHeaderCellDef mat-sort-header class="custom-header"> {{'typeOfReport' | translate}} </th>
                <td mat-cell *matCellDef="let element" class="custom-cell"> {{element.type | translate}} </td>
                </ng-container>

                <!-- Contact Name Column -->
                <ng-container matColumnDef="contactName">
                <th mat-header-cell *matHeaderCellDef mat-sort-header class="custom-header"> {{'contactName' | translate}} </th>
                <td mat-cell *matCellDef="let element" class="custom-cell"> {{element.contactName}} </td>
                </ng-container>

                <!-- Email Column -->
                <ng-container matColumnDef="email">
                <th mat-header-cell *matHeaderCellDef mat-sort-header class="custom-header"> {{'email' | translate}} </th>
                <td mat-cell *matCellDef="let element" class="custom-cell"> {{element.email}} </td>
                </ng-container>

                <!-- Phone Number Column -->
                <ng-container matColumnDef="phoneNumber">
                <th mat-header-cell *matHeaderCellDef mat-sort-header class="custom-header"> {{'phoneNumber' | translate}} </th>
                <td mat-cell *matCellDef="let element" class="custom-cell"> {{element.phoneNumber}} </td>
                </ng-container>

                <!-- Address Column -->
                <ng-container matColumnDef="address">
                <th mat-header-cell *matHeaderCellDef mat-sort-header class="custom-header"> {{'address' | translate}} </th>
                <td mat-cell *matCellDef="let element" class="custom-cell"> {{element.address}} </td>
                </ng-container>

                <!-- Reported At Column -->
                <ng-container matColumnDef="reportedAt">
                <th mat-header-cell *matHeaderCellDef mat-sort-header class="custom-header"> {{'reportedAt' | translate}} </th>
                <td mat-cell *matCellDef="let element" class="custom-cell">
                    {{element.reportedAt | date:'short'}}
                </td>
                </ng-container>
                
                <!-- Actions Column -->
                <ng-container matColumnDef="actions">
                  <th mat-header-cell *matHeaderCellDef class="custom-header actions-header"> {{'actions' | translate}} </th>
                  <td mat-cell *matCellDef="let element" class="custom-cell">
                    <button mat-icon-button [matMenuTriggerFor]="menu">
                      <mat-icon>more_horizontal</mat-icon>
                    </button>
                    <mat-menu #menu="matMenu">
                      <div class="menu-actions">
                        <button mat-menu-item (click)="viewDetails(element)" class="details-button">
                          <mat-icon>info</mat-icon>
                        </button>
                        <button mat-menu-item (click)="deleteElement(element)" class="delete-button">
                          <mat-icon>delete</mat-icon>
                        </button>
                      </div>
                    </mat-menu>
                  </td>
                </ng-container>
              
                <tr mat-header-row *matHeaderRowDef="displayedColumns; sticky: true" class="custom-row"></tr>
                <tr mat-row *matRowDef="let row; columns: displayedColumns;" class="custom-row" (click)="onRowClick($event, row)" 
                (dblclick)="onRowDoubleClick($event, row)"></tr>
              
              </table>
  
  
            </div>
        </mat-card>
        <div class="mat-paginator-sticky table-footer">
          <div class="buttons table-footer-left">
           
           </div>
           <div>
            <mat-paginator 
              [length]="totalCount"
              [pageSize]="paginationSort.pageSize"
              [pageSizeOptions]="[25, 50, 100]"
              showFirstLastButtons
              (page)="onPageChange($event)"
              aria-label="Select page of periodic elements">
            </mat-paginator>
           </div>
        </div>
      </div>
  
    </div>
  </form>