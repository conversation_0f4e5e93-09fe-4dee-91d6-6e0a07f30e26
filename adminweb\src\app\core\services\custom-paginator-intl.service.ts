import { Injectable } from '@angular/core';
import { MatPaginatorIntl } from '@angular/material/paginator';
import { TranslateService } from '@ngx-translate/core';
import { Subject } from 'rxjs';

@Injectable()
export class CustomPaginatorIntl extends MatPaginatorIntl {
  // This is needed to trigger change detection
  override changes = new Subject<void>();

  constructor(private translate: TranslateService) {
    super();

    // Load translations initially
    this.getAndInitTranslations();

    // Whenever the language changes, update the paginator labels
    this.translate.onLangChange.subscribe(() => {
      this.getAndInitTranslations();
    });
  }

  getAndInitTranslations() {
    this.translate.get([
      'PAGINATOR.ITEMS_PER_PAGE',
      'PAGINATOR.NEXT_PAGE',
      'PAGINATOR.PREVIOUS_PAGE',
      'PAGINATOR.FIRST_PAGE',
      'PAGINATOR.LAST_PAGE',
      'PAGINATOR.RANGE_LABEL'
    ]).subscribe(translations => {
      this.itemsPerPageLabel = translations['PAGINATOR.ITEMS_PER_PAGE'];
      this.nextPageLabel = translations['PAGINATOR.NEXT_PAGE'];
      this.previousPageLabel = translations['PAGINATOR.PREVIOUS_PAGE'];
      this.firstPageLabel = translations['PAGINATOR.FIRST_PAGE'];
      this.lastPageLabel = translations['PAGINATOR.LAST_PAGE'];
      this.getRangeLabel = (page: number, pageSize: number, length: number) => {
        if (length === 0 || pageSize === 0) {
          return `0 of ${length}`;
        }
        const startIndex = page * pageSize;
        const endIndex = startIndex < length ?
          Math.min(startIndex + pageSize, length) :
          startIndex + pageSize;
        return `${startIndex + 1} - ${endIndex} of ${length}`;
      };

      // Trigger change detection
      this.changes.next();
    });
  }
}