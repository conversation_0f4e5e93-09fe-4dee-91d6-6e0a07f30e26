import { CommonModule, DatePipe } from '@angular/common';
import { Component, DestroyRef, inject, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatDividerModule } from '@angular/material/divider';
import { MatSelectModule } from '@angular/material/select';
import { MatCardModule } from '@angular/material/card';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatInputModule } from '@angular/material/input';
import { MatMenuModule } from '@angular/material/menu';
import { MatSortModule, Sort } from '@angular/material/sort';
import { RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { MatPseudoCheckboxModule } from '@angular/material/core';
import { MatDialog } from '@angular/material/dialog';
import { GridColumnModel } from '../shared/interfaces/settings/grid-settings.model';
import { PaginationSortModel } from '../shared/interfaces/paginator/pagination-sort.model';
import { GridSetting } from '../shared/constants/grid-settings';
import { tap } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ReportsItemEntity } from '../shared/interfaces/reports/reports-item.model';
import { ReportsService } from '../core/services/reports.service';
import { EnumsModel } from '../shared/interfaces/enums/enum.model';
import { ReportsDeleteComponent } from './reports-delete/reports-delete.component';
import { ReportsDetailsComponent } from './reports-details/reports-details.component';

@Component({
  selector: 'app-reports',
  standalone: true,
  imports: [
    CommonModule,
    MatDividerModule,
    MatSelectModule,
    MatIconModule,
    MatButtonModule,
    MatCardModule,
    MatTableModule,
    MatPseudoCheckboxModule,
    ReactiveFormsModule,
    TranslateModule,
    MatInputModule,
    MatDatepickerModule,
    FormsModule,
    MatPaginatorModule,
    MatPaginator,
    MatTableModule,
    MatMenuModule,
    MatSortModule,
    RouterModule
  ],
  templateUrl: './reports.component.html',
  styleUrl: './reports.component.css'
})
export class ReportsComponent {
  @ViewChild(MatPaginator) paginator!: MatPaginator | null;
  private destroyRef = inject(DestroyRef);

  reportsFilterForm!: FormGroup;
  
  private clickTimeout: any; // To manage single-click timeout
  private clickDelay = 300; // Time in milliseconds to distinguish single from double click
  private isDoubleClick = false; // Flag to track if double-click occurred
  protected today = new Date();
  protected firstDayOfMonth = new Date(this.today.getFullYear(), this.today.getMonth(), 1);
  protected lastDayOfMonth = new Date(this.today.getFullYear(), this.today.getMonth() + 1, 0);
  protected gridColumns: GridColumnModel[] = [];
  protected gridColors: [] = []
  protected totalCount = 0;
  protected reportTypes!: EnumsModel; // Initialize as an empty array or as needed
  protected types: string[] = []; // Initialize as an empty array or as needed

  displayedColumns: string[] = [
  'id',
  'title',
  'type',
  'contactName',
  'email',
  'phoneNumber',
  'address',
  'reportedAt',
  'actions'
];

  protected dataSource: MatTableDataSource<ReportsItemEntity> =
        new MatTableDataSource<ReportsItemEntity>([]);

  protected paginationSort: PaginationSortModel = {
      pageNumber: GridSetting.defaultPageNumber,
      pageSize: GridSetting.defaultPageSize,
      sortColumn: GridSetting.defaultSortColumn,
      sortDirection: GridSetting.defaultSortDirection,
  };

 ngOnInit() {
  this.getReportsData();
  this.getReportTypes();
  }

  constructor(
    private fb: FormBuilder,
    public dialog: MatDialog,
    private datePipe: DatePipe,
    private reportsService: ReportsService
    ) {
    this.reportsFilterForm = this.fb.group({
     search: [''],
     firstName: [''],
     lastName: [''],
     type: [''],
     fromDate: [this.firstDayOfMonth],
     toDate:   [this.lastDayOfMonth]
    });
  }

  private getReportsData(paginationSort = this.paginationSort): void {
  let { pageNumber, pageSize, sortColumn, sortDirection } = paginationSort;
  
  // Get form values
  const formValues = this.reportsFilterForm.value;
  
  // Format dates if they exist
  const fromDate = formValues.fromDate ? this.datePipe.transform(formValues.fromDate, 'yyyy-MM-dd') : null;
  const toDate = formValues.toDate ? this.datePipe.transform(formValues.toDate, 'yyyy-MM-dd') : null;
  
  this.reportsService
    .getReports(
      formValues.search || null, // search parameter
      formValues.firstName || null, // firstName parameter
      formValues.lastName || null, // lastName parameter
      formValues.type || null, // type parameter
      fromDate, // formatted fromDate
      toDate, // formatted toDate,
      sortColumn,
      sortDirection,
      pageNumber,
      pageSize,
    )
    .pipe(
      tap((data) => {
        this.dataSource = new MatTableDataSource<ReportsItemEntity>(data.items);
        this.totalCount = data.totalCount;
      }),
      takeUntilDestroyed(this.destroyRef)
    )
    .subscribe();
  } 

  private getReportTypes(): void {
    this.reportsService
      .getReportTypes()
      .pipe(
        tap((data: EnumsModel) => {
          this.reportTypes = data;
          this.types = Object.values(this.reportTypes);
        }),
        takeUntilDestroyed(this.destroyRef)
      )
      .subscribe();
  }

  protected onPageChange(event: any) {
        const pageIndex = event.pageIndex + 1; // Paginator index starts from 0, while your API starts from 1
        const pageSize = event.pageSize;
        this.paginationSort = {
          ...this.paginationSort,
          pageSize: pageSize,
          pageNumber: pageIndex,
        };
    
        this.getReportsData();
      }
    
      protected onSortChange(sortState: Sort) {
        this.paginationSort = {
          ...this.paginationSort,
          sortColumn: sortState.active,
          sortDirection: sortState.direction,
        };
        this.getReportsData();
      }
    
      private resetPaginatorSort() {
        if (this.paginator) {
          this.paginator.firstPage();
        }
        this.paginationSort = {
          pageNumber: GridSetting.defaultPageNumber,
          pageSize: GridSetting.defaultPageSize,
          sortColumn: GridSetting.defaultSortColumn,
          sortDirection: GridSetting.defaultSortDirection,
        };
      }


  deleteElement(element: any): void {
    const dialogRef = this.dialog.open(ReportsDeleteComponent, {
      width: '530px',
      data: { id: element.id, content: element.content }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.getReportsData();
      }
    });
  }

  viewDetails(element: any): void {
      this.reportsService.getReportsById(element.id).subscribe({
        next: (data) => {
          const mergedElement = { ...element, ...data };
    
          const dialogRef = this.dialog.open(ReportsDetailsComponent, {
            height: '600px',
            width: '530px',
            data: {data: mergedElement}
          });
    
          dialogRef.afterClosed().subscribe(() => {
            console.log('Dialog closed');
          });
        },
        error: (error) => {
          console.error('Error fetching details:', error);
        },
        complete: () => {
          console.log('Request completed.');
        }
      });
    }

  onRowClick(event: MouseEvent, row: any): void {
    // Check if the event target is a button or inside a specific column
    const target = event.target as HTMLElement;
  
    // Prevent row click if the target is a button, icon, or menu
    if (
      target.closest('button') ||
      target.closest('mat-menu') ||
      target.closest('.actions-header')
    ) {
      return;
    }
  
    // Reset double-click flag
    this.isDoubleClick = false;

    // Set a timeout for single-click action
    this.clickTimeout = setTimeout(() => {
      if (!this.isDoubleClick) {
      }
    }, this.clickDelay);
  }
  
  onRowDoubleClick(event: MouseEvent, row: any): void {
    // Check if the event target is a button or inside a specific column
    const target = event.target as HTMLElement;
  
    // Prevent row double-click if the target is a button, icon, or menu
    if (
      target.closest('button') ||
      target.closest('mat-menu') ||
      target.closest('.actions-header')
    ) {
      return;
    }
  
    // Set double-click flag to true
    this.isDoubleClick = true;

    // Clear the single-click timeout to prevent its execution
    clearTimeout(this.clickTimeout);

  }


  onSearch() {
    this.resetPaginatorSort();
    this.getReportsData();
  }

  changeSticky(element: string) {
    return this.gridColumns.find(column => column.columnName === element)?.fixed;
  }
}
