import { CommonModule } from '@angular/common';
import {
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  OnDestroy,
  OnInit,
} from '@angular/core';
import { Router, RouterLink, RouterOutlet } from '@angular/router';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { Subscription } from 'rxjs';
import { LoaderComponent } from './shared/loader/loader.component';
import { TokenHelperService } from './core/services/token-helper.service';
import { AuthService } from './core/services/auth.service';
import { NavigationComponent } from './navigation/navigation.component';
import { MessageService } from './core/services/message.service';

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [
    CommonModule,
    RouterOutlet,
    NavigationComponent,
    LoaderComponent,
    TranslateModule,],
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.css']
})
export class AppComponent {
  showNav: boolean = true;
  authTimeOut: number = 0;
  sentRequest: boolean = false;

  private refreshTokensSubscription!: Subscription;
  private isAuthenticatedSubscription!: Subscription;

  protected isLoggedIn: boolean = false;
  private refreshTimerId?: number;
  private logIntervalId?: number; // Add this line to store the interval ID

  constructor(
    private translate: TranslateService,
    private router: Router,
    private authService: AuthService,
    private changeDetectorRef: ChangeDetectorRef,
    private tokenHelper: TokenHelperService,
    private messageService: MessageService
  ) {
    
    this.translate.addLangs(['bg']);
    this.translate.setDefaultLang('bg');
    
  }
  
  
  ngOnInit() {
    
    this.isAuthenticatedSubscription = this.authService
      .getLoggedIn()
      .subscribe((data: any) => {
        this.isLoggedIn = data;
        this.setupTokenRefreshTimer(this.isLoggedIn);

        if(this.isLoggedIn) {
        const token = localStorage.getItem('token')
         
          if(token) {
            const role = this.tokenHelper.getRoleFromToken(token);
            const userFullName = this.tokenHelper.getUserFullNameFromToken(token);
           

            // Set role in localStorage
            if (role) {
              localStorage.setItem('role', role);
              this.authService.setNavigationRole(role); // Set role in BehaviorSubject
            }

            if(userFullName) {
            localStorage.setItem('userFullName', userFullName);  
            this.authService.setNavigationUserFullName(userFullName); // Set role in BehaviorSubject
            }

  
          }
        }
      });

    
  }

  ngAfterViewInit() {
    this.changeDetectorRef.detectChanges();
  }

  ngOnDestroy() {
    
    // Clear both timers and unsubscribe to avoid memory leaks
    if (this.refreshTimerId) {
      clearTimeout(this.refreshTimerId);
    }
    if (this.logIntervalId) {
      clearInterval(this.logIntervalId); // Add this line to clear the interval
    }
    if (this.refreshTokensSubscription) {
      this.refreshTokensSubscription.unsubscribe();
    }
    if (this.isAuthenticatedSubscription) {
      this.isAuthenticatedSubscription.unsubscribe();
    }
      
  }

  private setupTokenRefreshTimer(isLogged: boolean) {
    
    if (isLogged) {
      const tokenExpirationDate = this.authService.getTokenExpirationDate();
      if (tokenExpirationDate) {
        const timeUntilExpiration = tokenExpirationDate.valueOf() - Date.now();
        const refreshTime = timeUntilExpiration - 5 * 60 * 1000; // 5 minutes in milliseconds

        if (refreshTime > 0) {
          this.refreshTimerId = window.setTimeout(() => {
            this.refreshTokens();
          }, refreshTime);

          if (this.logIntervalId) {
            clearInterval(this.logIntervalId);
          }

          this.logIntervalId = window.setInterval(() => {
            const remainingTimeInMilliseconds =
              tokenExpirationDate.valueOf() - Date.now();
            const remainingTimeInSeconds = Math.max(
              Math.floor(remainingTimeInMilliseconds / 1000),
              0
            );

            // If the remaining time reaches zero or less, log out the user immediately
            if (remainingTimeInSeconds <= 0) {
              clearTimeout(this.refreshTimerId);
              clearInterval(this.logIntervalId);
              console.warn('Token has expired. Logging out.');
              this.authService.logout();
            } else {
            }
          }, 1000);
        } else {
          console.warn(
            'Token is either expired or about to expire in less than 4 minutes.'
          );
          this.refreshTokens();
        }
      } else {
        console.error('Token expiration date is invalid or missing.');
        this.authService.logout();
      }
    } else {
      clearTimeout(this.refreshTimerId);
      clearInterval(this.logIntervalId);
    }
      
  }

  private refreshTokens() {
    
    this.sentRequest = true;

    // Prevent multiple refresh requests
    if (this.refreshTokensSubscription) {
      this.refreshTokensSubscription.unsubscribe();
    }

    this.refreshTokensSubscription = this.authService.refreshToken().subscribe({
      next: (data) => {
        if (data && data.isAuthSuccessful) {
          localStorage.setItem('token', data.accessToken);
          localStorage.setItem('refreshToken', data.refreshToken);
          clearTimeout(this.refreshTimerId);
          clearInterval(this.logIntervalId); // Clear the interval here as well
          this.setupTokenRefreshTimer(this.isLoggedIn);
          this.sentRequest = false;
        } else {
          clearTimeout(this.refreshTimerId);
          clearInterval(this.logIntervalId); // Clear the interval here as well
          this.authService.logout();
        }
      },
      error: () => {
        clearTimeout(this.refreshTimerId);
        clearInterval(this.logIntervalId); // Clear the interval here as well
        this.authService.logout();
      },
    });
    
  }
    

  title = 'smart-Yambol-web';
}
