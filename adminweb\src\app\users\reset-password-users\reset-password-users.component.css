.contentPanel{
    padding: 2px;
    width: 100%;
    border-top: 8px solid var(--primary-color);
    justify-content: space-between;
    border-bottom: 1px solid #ccc;
  }
  
  .panelHead{
    margin: 20px;
    display: flex;
    align-items: center;
  }
  
  .h1{
    text-align: center;
    color: var(--color-main);
    width: 100%;
    border-bottom: 1px solid var(--color-gray-border) !important;
    margin: 0;
    font-family: 'Roboto', sans-serif;
    font-size: 20px;
    font-weight: 700;
    line-height: 16px;
  }

  .renew-button {
    margin-bottom: 30%;
  }
  
  .close-icon{
      cursor: pointer;
      font-size: 35px;
      border: none;
      background-color: transparent;
  }
  
  .save{
    background-color: var(--secondary-color);
    color: white;
  }
  
  .cancel {
    background-color: white;
    color: var(--secondary-color);
  }
  
  
  .tabColumn{
    width: 100%;
    margin-left: 2.5px;
  }
  
  .tabColumn-1-fields{
    width: 100%;
    /* margin: 0 5px; */
  }
  
  .tabColumn-2-fields{
    width: 49%;
    margin: 0 2.5px;
  }
  
  .tabColumn-3-fields{
    width: 32.5%;
    margin: 0 2.5px;
  }
  
  .tabColumn-4-fields{
    width: 24%;
    margin: 0 2.5px;
  }
  
  :ng-deep .mdc-dialog .mdc-dialog__content {
    padding: 20px !important;
  }
  
  ::ng-deep .mat-mdc-dialog-container-with-actions .mat-mdc-dialog-content {
    padding: 20px;
  }
  
  :host ::ng-deep .mat-mdc-text-field-wrapper {
    height: 44px;
  }
  
  .disabled-field input {
    pointer-events: stroke;
    color: #9e9e9e; /* Светъл цвят на текста за индикация */
  }
  
  .disabled-field input:hover {
    cursor: not-allowed; /* Забранителен знак при hover */
  }

  /* Hide the default browser password reveal eye icon */
input[type="password"]::-ms-reveal,
input[type="password"]::-ms-clear {
  display: none;
}

input[type="password"] {
  -webkit-text-security: disc;
}