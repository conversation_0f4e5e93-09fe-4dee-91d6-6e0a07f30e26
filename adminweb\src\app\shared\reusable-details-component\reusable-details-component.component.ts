import { CommonModule } from '@angular/common';
import { AfterViewInit, Component, ElementRef, Inject, OnDestroy, OnInit, ViewChild, ViewContainerRef } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { GoogleMapsService } from '../../core/services/google.maps.service';
import { environment } from '../../../environments/enviroment';

@Component({
  selector: 'app-reusable-details-component',
  standalone: true,
  imports: [
  TranslateModule,
  MatIconModule,
  MatDialogModule,
  CommonModule
  ],
  templateUrl: './reusable-details-component.component.html',
  styleUrl: './reusable-details-component.component.css'
})
export class ReusableDetailsComponentComponent {
  @ViewChild('mapContainer') mapContainer!: ElementRef;
  @ViewChild('markerContainer', { read: ViewContainerRef, static: true }) markerContainer!: ViewContainerRef;
  @ViewChild('slider') slider!: ElementRef;

  currentSlide = 0;

  constructor(
    public dialogRef: MatDialogRef<ReusableDetailsComponentComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any, private googleMapsService: GoogleMapsService) {}

  protected mapHeight: number = 0;
  protected mapWidth: number = 0;
  protected center: google.maps.LatLngLiteral = {lat: this.data.data.latitude, lng: this.data.data.longitude};
  protected zoom = 17;
  private map: google.maps.Map | undefined;
  private dayMapId = environment.dayMapId;
  private currentMarker: any = null;
  protected markerIcon: string = '';

  async ngOnInit() {
    this.markerIcon = this.data.markerIcon
    await this.initializeGoogleMaps()
  }


  private async initializeGoogleMaps() {
    try {
      // Load necessary libraries
      await this.googleMapsService.loadLibraries();

      // Initialize the map
      this.map = await this.googleMapsService.initializeMap(
        this.mapContainer.nativeElement,
        this.center,
        this.zoom,
        this.dayMapId,
        'day'
      );

      // Add a new marker at the clicked position and store the reference
      this.googleMapsService.addAdvancedMarker(
        this.center,
        2,
        undefined,
        undefined,
        this.markerIcon,
        undefined,
        true);


    } catch (error) {
      console.error('Error loading Google Maps:', error);
    }
  }

  async ngAfterViewInit(): Promise<void> {
    this.googleMapsService.setViewContainerRef(this.markerContainer);
    this.updateSliderPosition();
  }

  // Slider navigation methods
  nextSlide(): void {
    if (this.currentSlide < this.data.data.images.length - 1) {
      this.currentSlide++;
    } else {
      this.currentSlide = 0; // Loop back to the first slide
    }
    this.updateSliderPosition();
  }

  prevSlide(): void {
    if (this.currentSlide > 0) {
      this.currentSlide--;
    } else {
      this.currentSlide = this.data.data.images.length - 1; // Loop to the last slide
    }
    this.updateSliderPosition();
  }

  goToSlide(index: number): void {
    this.currentSlide = index;
    this.updateSliderPosition();
  }

  updateSliderPosition(): void {
    if (this.slider && this.slider.nativeElement) {
      const translateX = -this.currentSlide * 100;
      this.slider.nativeElement.style.transform = `translateX(${translateX}%)`;
    }
  }

  onClose(): void {
    this.dialogRef.close();
  }

  ngOnDestroy(): void {
    this.googleMapsService.unloadMap();
  }
}
