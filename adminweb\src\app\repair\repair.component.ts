import { CommonModule, DatePipe } from '@angular/common';
import { Component, DestroyRef, inject, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatDividerModule } from '@angular/material/divider';
import { MatSelectModule } from '@angular/material/select';
import { MatCardModule } from '@angular/material/card';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatInputModule } from '@angular/material/input';
import { MatMenuModule } from '@angular/material/menu';
import { MatSortModule, Sort } from '@angular/material/sort';
import { Router, RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { MatPseudoCheckboxModule } from '@angular/material/core';
import { MatDialog } from '@angular/material/dialog';
import { MatTooltip } from '@angular/material/tooltip';
import { GridColumnModel } from '../shared/interfaces/settings/grid-settings.model';
import { PhoneEventsPreviewComponent } from '../shared/phone-events-preview/phone-events-preview.component';
import { DetailsRepairComponent } from './details-repair/details-repair.component';
import { DeleteRepairComponent } from './delete-repair/delete-repair.component';
import { RepairItemEntity } from '../shared/interfaces/repairs/repair-get-item.model';
import { PaginationSortModel } from '../shared/interfaces/paginator/pagination-sort.model';
import { GridSetting } from '../shared/constants/grid-settings';
import { RepairService } from '../core/services/repairs.service';
import { tap } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

@Component({
  selector: 'app-repair',
  standalone: true,
  imports: [
  CommonModule,
  MatDividerModule,
  MatSelectModule,
  MatIconModule,
  MatButtonModule,
  MatCardModule,
  MatTableModule,
  MatPseudoCheckboxModule,
  ReactiveFormsModule,
  TranslateModule,
  MatInputModule,
  MatDatepickerModule,
  FormsModule,
  MatPaginatorModule,
  MatPaginator,
  MatTableModule,
  MatMenuModule,
  MatSortModule,
  RouterModule,
  MatTooltip
  ],
  templateUrl: './repair.component.html',
  styleUrl: './repair.component.css'
})
export class RepairComponent {
  @ViewChild(MatPaginator) paginator!: MatPaginator | null;
  private destroyRef = inject(DestroyRef);

  cultureEventsFilterForm!: FormGroup;
  
  private clickTimeout: any; // To manage single-click timeout
  private clickDelay = 300; // Time in milliseconds to distinguish single from double click
  private isDoubleClick = false; // Flag to track if double-click occurred
  protected today = new Date();
  protected firstDayOfMonth = new Date(this.today.getFullYear(), this.today.getMonth(), 1);
  protected lastDayOfMonth = new Date(this.today.getFullYear(), this.today.getMonth() + 1, 0);
  protected gridColumns: GridColumnModel[] = [];
  protected gridColors: [] = []
   protected totalCount = 0;

  displayedColumns: string[] = [
  'id', 
  'name', 
  'latitude', 
  'longitude', 
  'startDate', 
  'endDate', 
  'createdBy', 
  'createdDate', 
  'lastModifiedBy', 
  'lastModifiedDate', 
  'actions'
];
  
    protected dataSource: MatTableDataSource<RepairItemEntity> =
          new MatTableDataSource<RepairItemEntity>([]);
  
    protected paginationSort: PaginationSortModel = {
          pageNumber: GridSetting.defaultPageNumber,
          pageSize: GridSetting.defaultPageSize,
          sortColumn: GridSetting.defaultSortColumn,
          sortDirection: GridSetting.defaultSortDirection,
      };

 ngOnInit() {
  this.getRepairsData();
 }

  constructor(
    private fb: FormBuilder,
    public dialog: MatDialog,
    private datePipe: DatePipe,
    private router: Router,
    private repairService: RepairService
    ) {
    this.cultureEventsFilterForm = this.fb.group({
     fromDate: [this.firstDayOfMonth],
     toDate:   [this.lastDayOfMonth],
     search: ['']
    });
  }

   private getRepairsData(paginationSort = this.paginationSort): void {
        
            let search = this.cultureEventsFilterForm.value.search;
            let fromDate = this.cultureEventsFilterForm.value.fromDate;
            let toDate = this.cultureEventsFilterForm.value.toDate;
    
            let formattedFromDate = fromDate ? this.datePipe.transform(fromDate, 'yyyy-MM-dd') : null;
            let formattedToDate = toDate ? this.datePipe.transform(toDate, 'yyyy-MM-dd') : null;
           
            let category = 'Culture';
            let { pageNumber, pageSize, sortColumn, sortDirection } = paginationSort;
            this.repairService
              .getRepairs(
                search,
                formattedFromDate,
                formattedToDate,
                sortColumn,
                sortDirection,
                pageNumber,
                pageSize
              )
              .pipe(
                tap((data) => {
                  this.dataSource = new MatTableDataSource<RepairItemEntity>(data.items);
                  this.totalCount = data.totalCount;
                }),
                takeUntilDestroyed(this.destroyRef)
              )
              .subscribe();
      }

   protected onPageChange(event: any) {
            const pageIndex = event.pageIndex + 1; // Paginator index starts from 0, while your API starts from 1
            const pageSize = event.pageSize;
            this.paginationSort = {
              ...this.paginationSort,
              pageSize: pageSize,
              pageNumber: pageIndex,
            };
        
            this.getRepairsData();
          }
        
          protected onSortChange(sortState: Sort) {
            this.paginationSort = {
              ...this.paginationSort,
              sortColumn: sortState.active,
              sortDirection: sortState.direction,
            };
            this.getRepairsData();
          }
        
          private resetPaginatorSort() {
            if (this.paginator) {
              this.paginator.firstPage();
            }
            this.paginationSort = {
              pageNumber: GridSetting.defaultPageNumber,
              pageSize: GridSetting.defaultPageSize,
              sortColumn: GridSetting.defaultSortColumn,
              sortDirection: GridSetting.defaultSortDirection,
            };
          }

  openAddRepairDialog(): void {
    // Navigate to repairAdd route
    this.router.navigate(['/repair/repair-add']);
  }


  editElement(element: any): void {
      // Navigate to repairEdit route
      this.router.navigate(['/repair/repair-edit', element.id], {
      });
    }

  deleteElement(element: any): void {
    const dialogRef = this.dialog.open(DeleteRepairComponent, {
      width: '530px',
      data: { id: element.id, content: element.content }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.getRepairsData();
      }
    });
  }

  viewDetails(element: any): void {
      this.repairService.getRepairById(element.id).subscribe({
        next: (data) => {
          const mergedElement = { ...element, ...data };
    
          const dialogRef = this.dialog.open(DetailsRepairComponent, {
            height: '600px',
            width: '530px',
            data: {data: mergedElement}
          });
    
          dialogRef.afterClosed().subscribe(() => {
            console.log('Dialog closed');
          });
        },
        error: (error) => {
          console.error('Error fetching details:', error);
        },
        complete: () => {
          console.log('Request completed.');
        }
      });
    }

  onRowClick(event: MouseEvent, row: any): void {
    // Check if the event target is a button or inside a specific column
    const target = event.target as HTMLElement;
  
    // Prevent row click if the target is a button, icon, or menu
    if (
      target.closest('button') ||
      target.closest('mat-menu') ||
      target.closest('.actions-header')
    ) {
      return;
    }
  
    // Reset double-click flag
    this.isDoubleClick = false;

    // Set a timeout for single-click action
    this.clickTimeout = setTimeout(() => {
      if (!this.isDoubleClick) {
        this.viewDetails(row); // Trigger single-click action
      }
    }, this.clickDelay);
  }
  
  onRowDoubleClick(event: MouseEvent, row: any): void {
    // Check if the event target is a button or inside a specific column
    const target = event.target as HTMLElement;
  
    // Prevent row double-click if the target is a button, icon, or menu
    if (
      target.closest('button') ||
      target.closest('mat-menu') ||
      target.closest('.actions-header')
    ) {
      return;
    }
  
    // Set double-click flag to true
    this.isDoubleClick = true;

    // Clear the single-click timeout to prevent its execution
    clearTimeout(this.clickTimeout);

    // Trigger double-click action
    this.editElement(row);
  }


  onSearch() {
      this.resetPaginatorSort();
      this.getRepairsData();
    }

  changeSticky(element: string) {
    return this.gridColumns.find(column => column.columnName === element)?.fixed;
  }
}
