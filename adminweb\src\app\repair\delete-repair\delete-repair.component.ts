import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { RepairService } from '../../core/services/repairs.service';
import { MessageService } from '../../core/services/message.service';

@Component({
  selector: 'app-delete-repair',
  standalone: true,
  imports: [
  TranslateModule,
  MatIconModule,
  MatDialogModule
  ],
  templateUrl: './delete-repair.component.html',
  styleUrl: './delete-repair.component.css'
})
export class DeleteRepairComponent {
  constructor(
        public dialogRef: MatDialogRef<DeleteRepairComponent>,
        private repairService: RepairService,
        private messageService: MessageService,
        @Inject(MAT_DIALOG_DATA) public data: any
      ) { }
    
      onNoClick(): void {
        this.dialogRef.close();
      }
    
      onYesClick(): void {
        this.repairService.deleteRepair(this.data.id).subscribe(
          {
            next: (response) => {
              this.messageService.showMessage(["DeleteSuccessfully"], 'success');
              this.dialogRef.close(true);
            },
            error: (error: any) => {
              console.error('Error deleting interest point:', error);
              // Handle error as per your application's requirement
            }
          }
        );
      }
}
