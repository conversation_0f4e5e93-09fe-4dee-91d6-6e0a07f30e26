import {  CommonModule, DatePipe } from '@angular/common';
import { Component, DestroyRef, inject, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatDividerModule } from '@angular/material/divider';
import { MatSelectModule } from '@angular/material/select';
import { MatCardModule } from '@angular/material/card';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatInputModule } from '@angular/material/input';
import { MatMenuModule } from '@angular/material/menu';
import { MatSortModule } from '@angular/material/sort';
import { Router, RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { MatPseudoCheckboxModule } from '@angular/material/core';
import { MatDialog } from '@angular/material/dialog';
import { MatTooltip } from '@angular/material/tooltip';
import { GridColumnModel } from '../shared/interfaces/settings/grid-settings.model';
import { PhoneEventsPreviewComponent } from '../shared/phone-events-preview/phone-events-preview.component';
import { AddNewsComponent } from './add-news/add-news.component';
import { EditNewsComponent } from './edit-news/edit-news.component';
import { DetailsNewsComponent } from './details-news/details-news.component';
import { DeleteNewsComponent } from './delete-news/delete-news.component';
import { PublishStatusNewsComponent } from './publish-status-news/publish-status-news.component';

@Component({
  selector: 'app-news',
  standalone: true,
  imports: [
        CommonModule,
        MatDividerModule,
        MatSelectModule,
        MatIconModule,
        MatButtonModule,
        MatCardModule,
        MatTableModule,
        MatPseudoCheckboxModule,
        ReactiveFormsModule,
        TranslateModule,
        MatInputModule,
        MatDatepickerModule,
        FormsModule,
        MatPaginatorModule,
        MatPaginator,
        MatTableModule,
        MatMenuModule,
        MatSortModule,
        RouterModule,
        MatTooltip
  ],
  templateUrl: './news.component.html',
  styleUrl: './news.component.css'
})
export class NewsComponent {
  private destroyRef = inject(DestroyRef);

  cultureEventsFilterForm!: FormGroup;
  
  private clickTimeout: any; // To manage single-click timeout
  private clickDelay = 300; // Time in milliseconds to distinguish single from double click
  private isDoubleClick = false; // Flag to track if double-click occurred
  protected today = new Date();
  protected firstDayOfMonth = new Date(this.today.getFullYear(), this.today.getMonth(), 1);
  protected gridColumns: GridColumnModel[] = [];
  protected gridColors: [] = []


  displayedColumns: string[] = ['id', 'published', 'heading', 'content', 'actions'];
  protected dataSource = [
  {
    id: 1,
    published: 'published',
    heading: 'Над 650 са случаите на варицела в област Ямбол за година',
    content: "През изминалата 2024 година в област Ямбол са регистрирани над 650 случая на варицела, съобщи д-р Радостина Калчева, директор на Регионалната здравна инспекция (РЗИ). По нейни думи, заболяването е типично за зимните месеци, когато предаването по въздушно-капков път е по-лесно.За декември в областта са отчетени 32 случая на варицела, докато през ноември броят им е бил 14. Д-р Калчева подчерта, че съществува ваксина срещу варицела, която е препоръчителна, но не задължителна. Родителите, които желаят да ваксинират децата си, могат да се консултират с личните лекари и да заявят ваксината в аптеките.През предходната 2023 година случаите на варицела в региона са били приблизително същия брой – 620. Според здравните власти заболяването е циклично, като пик се наблюдава на всеки три-четири години, когато има натрупване на неболедували и неимунизирани лица.",
    contentImage: '../../../assets/images/news-only-photo.png',
    img: '../../../assets/images/empty-page.png'
  },
  {
    id: 2,
    published: 'unpublished',
    heading: 'Екипите на „Синя зона“ – Ямбол следят за нарушения',
    content: 'Екипите на „Синя зона“ – Ямбол продължават с проверките на територията на „Синя зона“, които ще бъдат насочени към водачите на автомобили с инвалиден стикер.',
    contentImage: '../../../assets/images/news-only-photo.png',
    img: '../../../assets/images/empty-page.png'
  },
  {
    id: 3,
    published: 'unpublished',
    heading: 'Ямбол отбелязва Атанасовден',
    content: 'На 18 януари Българската православна църква отбелязва празника на Свети Атанасий Велики, известен в българския празничен календар като Атанасовден',
    contentImage: '../../../assets/images/news-only-photo.png',
    img: '../../../assets/images/empty-page.png'
  }
  ];

 ngOnInit() {
  }

  constructor(
    private fb: FormBuilder,
    public dialog: MatDialog,
    private datePipe: DatePipe,
    private router: Router 
    ) {
    this.cultureEventsFilterForm = this.fb.group({
      fromDate: [this.firstDayOfMonth],
      toDate:   [this.today],
      heading: ['']
    });
  }

  openAddNewsDialog(): void {
    // Navigate to repairAdd route
    this.router.navigate(['/news/news-add']);
  }

  editElement(element: any): void {
    // Navigate to repairEdit route
    this.router.navigate(['/news/news-edit', element.id], {
      queryParams: { 
        heading: element.heading, 
        content: element.content,
        published: element.published
      }  // Send the whole element via router state
    });
  }

  deleteElement(element: any): void {
    const dialogRef = this.dialog.open(DeleteNewsComponent, {
      width: '530px',
      data: { id: element.id, content: element.content }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        // Изпълнение на изтриването
        // Тук можете да добавите реална логика за изтриване
      }
    });
  }

  viewDetails(element: any): void {
    const dialogRef = this.dialog.open(DetailsNewsComponent, {
      width: '530px',
      data: element
    });

    dialogRef.afterClosed().subscribe(result => {

    });
  }

  openPreview(element: any): void {
    const imgPath = element.img
    const headerPath = '../../../assets/images/news-header.png'
    const date = '10.01.2025'
    const datePath = '../../../assets/images/date-window.png'
    const dialogRef = this.dialog.open(PhoneEventsPreviewComponent, {
      width: '480px',
      data: {image: imgPath, heading: element.heading, content: element.content, contentImage: element.contentImage, headerImage: headerPath, date: date, datePath: datePath}
    });

    dialogRef.afterClosed().subscribe(result => {

    });
  }

  updatePublishStatus(element: any): void {
      const dialogRef = this.dialog.open(PublishStatusNewsComponent, {
        width: '530px',
        data: { id: element.id, content: element.content, published: element.published }
      });
  
      dialogRef.afterClosed().subscribe(result => {
        if (result) {
          // Изпълнение на изтриването
          // Тук можете да добавите реална логика за изтриване
        }
      });
    }

  onRowClick(event: MouseEvent, row: any): void {
    // Check if the event target is a button or inside a specific column
    const target = event.target as HTMLElement;
  
    // Prevent row click if the target is a button, icon, or menu
    if (
      target.closest('button') ||
      target.closest('mat-menu') ||
      target.closest('.actions-header')
    ) {
      return;
    }
  
    // Reset double-click flag
    this.isDoubleClick = false;

    // Set a timeout for single-click action
    this.clickTimeout = setTimeout(() => {
      if (!this.isDoubleClick) {
        this.openPreview(row); // Trigger single-click action
      }
    }, this.clickDelay);
  }
  
  onRowDoubleClick(event: MouseEvent, row: any): void {
    // Check if the event target is a button or inside a specific column
    const target = event.target as HTMLElement;
  
    // Prevent row double-click if the target is a button, icon, or menu
    if (
      target.closest('button') ||
      target.closest('mat-menu') ||
      target.closest('.actions-header')
    ) {
      return;
    }
  
    // Set double-click flag to true
    this.isDoubleClick = true;

    // Clear the single-click timeout to prevent its execution
    clearTimeout(this.clickTimeout);

    // Trigger double-click action
    this.editElement(row);
  }


  onSearch() {

  }

  changeSticky(element: string) {
    return this.gridColumns.find(column => column.columnName === element)?.fixed;
  }

}
