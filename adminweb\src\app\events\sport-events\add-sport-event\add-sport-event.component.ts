import { CommonModule } from '@angular/common';
import { Component, ElementRef, ViewChild } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatOptionModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { MatDividerModule } from '@angular/material/divider';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';  // Import MatInputModule
import { MatSelectModule } from '@angular/material/select';
import { Router } from '@angular/router';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { EditorComponent } from '../../../shared/editor/editor.component';
import { TranslationModalComponent } from '../../../shared/translation-modal/translation-modal.component';
import { TranslationDeleteModalComponent } from '../../../shared/translation-delete-modal/translation-delete-modal.component';
import { MessageService } from '../../../core/services/message.service';
import { EventsService } from '../../../core/services/events.service';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';

@Component({
  selector: 'app-add-sport-event',
  standalone: true,
  imports: [
    CommonModule, 
    TranslateModule,
    MatIconModule,
    MatDialogModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatDatepickerModule,
    MatSelectModule,
    MatOptionModule,
    MatDividerModule,
    MatCardModule,
    EditorComponent,
    MatSlideToggleModule,
    FormsModule
  ],
  templateUrl: './add-sport-event.component.html',
  styleUrl: './add-sport-event.component.css'
})
export class AddSportEventComponent {
  @ViewChild('fileInput') fileInput: ElementRef | undefined;

  addSportEventForm!: FormGroup;
  protected content = '';
  protected imageSrcs: { 
    fileName: string; 
    contentType: string; 
    extension: string; 
    content: string; 
  }[] = [];
  protected translatedLanguages: { languageShort: string, languageFullName: string, heading: string, content: string }[] = [];
  protected selectedThumbnailIndex: number | null = null;
   protected showImages: boolean = true;

  constructor(private translate: TranslateService, private fb: FormBuilder, private router: Router, public dialog: MatDialog, private messageService: MessageService,
    private eventsService: EventsService
  ) {
    this.addSportEventForm = this.fb.group({
      heading: ['', [Validators.maxLength(60)]],
      date: [''],
      time: ['', [this.timeValidator]], // Add time control with validator
      templateContent: [''],
      thumbnailIndex: [null]
    });
  }

  get form() {
    return this.addSportEventForm.controls;
  }

  // Custom validator for time format (HH:MM)
  timeValidator(control: any) {
    const value = control.value;
    if (!value) return null; // Allow empty value
    
    const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;
    if (!timeRegex.test(value)) {
      return { invalidTime: true };
    }
    
    return null;
  }

  onTimeInput(event: Event) {
    const input = event.target as HTMLInputElement;
    let value = input.value.replace(/[^\d]/g, ''); // Remove non-digits
    
    // Auto-format as user types
    if (value.length >= 3) {
      value = value.substring(0, 2) + ':' + value.substring(2, 4);
    }
    
    // Validate hours (00-23)
    if (value.length >= 2) {
      const hours = parseInt(value.substring(0, 2));
      if (hours > 23) {
        value = '23' + value.substring(2);
      }
    }
    
    // Validate minutes (00-59)
    if (value.length >= 5) {
      const minutes = parseInt(value.substring(3, 5));
      if (minutes > 59) {
        value = value.substring(0, 3) + '59';
      }
    }
    
    // Limit to 5 characters (HH:MM)
    if (value.length > 5) {
      value = value.substring(0, 5);
    }
    
    // Update the form control
    this.addSportEventForm.get('time')?.setValue(value);
    input.value = value;
  }

  onTimeKeydown(event: KeyboardEvent) {
    const input = event.target as HTMLInputElement;
    const allowedKeys = ['Backspace', 'Delete', 'Tab', 'Escape', 'Enter', 'ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown'];
    
    // Allow navigation and editing keys
    if (allowedKeys.includes(event.key)) {
      return;
    }
    
    // Allow only numeric input
    if (!/^\d$/.test(event.key)) {
      event.preventDefault();
      return;
    }
    
    // Handle cursor position for better UX
    const cursorPosition = input.selectionStart || 0;
    const currentValue = input.value;
    
    // Skip colon position
    if (cursorPosition === 2 && currentValue.length >= 2) {
      input.setSelectionRange(3, 3);
    }
  }

  onTimeClick(event: Event) {
    const input = event.target as HTMLInputElement;
    const cursorPosition = input.selectionStart || 0;
    
    // If clicked on colon, move cursor to next position
    if (cursorPosition === 2 && input.value.length >= 3) {
      setTimeout(() => {
        input.setSelectionRange(3, 3);
      }, 0);
    }
  }

  onSubmit() {
    const templateContent = this.cleanContent(this.addSportEventForm.value.templateContent);

    if(this.addSportEventForm.valid && templateContent.length > 0) {
      this.addSportEventForm.patchValue({
        thumbnailIndex: this.selectedThumbnailIndex,
      });

      const files = this.imageSrcs.map((src) => {
        const byteArray = this.convertBase64ToByteArray(src.content);
        const blob = new Blob([byteArray], { type: src.contentType });
        const file = new File([blob], src.fileName, { type: src.contentType });
        return file;
      });

      const coverFile = this.selectedThumbnailIndex !== null && this.imageSrcs[this.selectedThumbnailIndex]
        ? files[this.selectedThumbnailIndex]
        : null;

      const filesWithoutCover = files.filter((file, index) => index !== this.selectedThumbnailIndex);
    
      const translations = this.translatedLanguages.map((translation) => ({
        name: translation.heading,
        description: translation.content,
        languageName: translation.languageFullName,
        languageCode: translation.languageShort,
      }));
    
      const formData = new FormData();
    
      formData.append('Name', this.addSportEventForm.value.heading);
      formData.append('Description', this.addSportEventForm.value.templateContent);
      formData.append('Category', 'Sports');

       // Combine date and time into StartDate field
    if (this.addSportEventForm.value.date && this.addSportEventForm.value.time) {
      const combinedDateTime = this.combineDateAndTime(
        this.addSportEventForm.value.date, 
        this.addSportEventForm.value.time
      );
      formData.append('StartDate', combinedDateTime.toISOString());
    } else if (this.addSportEventForm.value.date) {
      // If only date is provided, use date with default time (00:00:00)
      const dateValue = this.addSportEventForm.value.date;
      const dateOnly = dateValue instanceof Date ? dateValue : new Date(dateValue);
      formData.append('StartDate', dateOnly.toISOString());
    }
    
      translations.forEach((translation, index) => {
        formData.append(`Translations[${index}].name`, translation.name);
        formData.append(`Translations[${index}].description`, translation.description);
        formData.append(`Translations[${index}].languageCode`, translation.languageCode);
        formData.append(`Translations[${index}].languageName`, translation.languageName);
      });
      
      filesWithoutCover.forEach((file, index) => {
        formData.append(`Files[${index}]`, file, file.name);
      });

      if (this.selectedThumbnailIndex !== null && this.imageSrcs[this.selectedThumbnailIndex]) { 
        const coverSrc = this.imageSrcs[this.selectedThumbnailIndex];
      
        if (coverSrc) {
          const byteArray = this.convertBase64ToByteArray(coverSrc.content);
          const blob = new Blob([byteArray], { type: coverSrc.contentType });
          const file = new File([blob], coverSrc.fileName, { type: coverSrc.contentType });
      
          formData.append('Cover', file);
        }
      }
      
      this.eventsService.addEvent(formData).subscribe({
        next: (response) => {
          console.log(`Sport event added successfully`, response);
        },
        error: (error) => {
          console.error(`Error adding sport event`, error);
        },
        complete: () => {
          console.log(`Sport event request complete`);
          this.messageService.showMessage(["CreateSuccessfully"], 'success');
          this.router.navigate(['/events/sport-events']);
        }
      });
    } else {
      this.messageService.showMessage(["PleaseCompleteTheForm"], 'error');
    }
  }

    // Helper method to combine date and time
  private combineDateAndTime(date: Date, time: string): Date {
    const [hours, minutes] = time.split(':').map(num => parseInt(num, 10));
    const combinedDate = new Date(date);
    combinedDate.setHours(hours, minutes, 0, 0);
    return combinedDate;
  }

 convertBase64ToByteArray(base64: string): Uint8Array {
    const binaryString = atob(base64.split(',')[1]);
    const byteArray = new Uint8Array(binaryString.length);
    for (let i = 0; i < binaryString.length; i++) {
      byteArray[i] = binaryString.charCodeAt(i);
    }
    return byteArray;
  }

  selectThumbnail(index: number) {
    this.selectedThumbnailIndex = index;
    this.addSportEventForm.patchValue({
      thumbnailIndex: this.selectedThumbnailIndex,
    });
  }

  onChangeContent() {
    const templateContentControl = this.addSportEventForm.get('templateContent');
    if (templateContentControl) {
      templateContentControl.setValue(this.content);
    }
  }

  openFileDialog() {
    if (this.fileInput) {
      this.fileInput.nativeElement.click();
    }
  }

  onFileSelected(event: Event) {
    const fileInput = event.target as HTMLInputElement;
    if (fileInput.files && fileInput.files.length > 0) {
      Array.from(fileInput.files).forEach((file: File) => {
        const reader = new FileReader();
  
        reader.onload = (e: any) => {
          const fileDetails = {
            fileName: file.name,
            contentType: file.type,
            extension: file.name.split('.').pop() || '',
            content: e.target.result
          };
  
          this.imageSrcs.push(fileDetails);
  
          if (this.imageSrcs.length === 1) {
            this.selectedThumbnailIndex = 0;
          }
        };
  
        reader.readAsDataURL(file);
      });
    }
  }
  
  removePhoto(index: number) {
    this.imageSrcs.splice(index, 1);

    if (this.fileInput) {
      this.fileInput.nativeElement.value = '';
    }
    
    if (this.selectedThumbnailIndex === index) {
      this.selectedThumbnailIndex = this.imageSrcs.length > 0 ? 0 : null;
    } else if (this.selectedThumbnailIndex !== null && index < this.selectedThumbnailIndex) {
      this.selectedThumbnailIndex--;
    }
  }

  openTranslationModal(language?: string) {
    const heading = this.addSportEventForm.value.heading?.trim();
    let content = this.addSportEventForm.value.templateContent?.trim();
    
    content = this.cleanContent(content);
    
    if (!heading || !content) {
      this.messageService.showMessage(['HeadingAndContentCannotBeEmpty'], 'error');
      return;
    }
    
    let existingTranslation = undefined;
    if (language) {
      existingTranslation = this.translatedLanguages.find(
        translation => translation.languageShort === language
      );
    }
    
    const dialogRef = this.dialog.open(TranslationModalComponent, {
      width: '693px',
      height: '95vh',
      data: {
        heading: existingTranslation ? existingTranslation.heading : this.addSportEventForm.value.heading,
        content: existingTranslation ? existingTranslation.content : this.content,
        selectedLanguage: language
      }
    });
  
    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        const existingTranslationIndex = this.translatedLanguages.findIndex(
          translation => translation.languageShort === result.languageShort
        );
  
        if (existingTranslationIndex !== -1) {
          this.translatedLanguages[existingTranslationIndex] = {
            languageShort: result.languageShort,
            languageFullName: result.languageFullName,
            heading: result.heading,
            content: result.content
          };
        } else {
          this.translatedLanguages.push({
            languageShort: result.languageShort,
            languageFullName: result.languageFullName,
            heading: result.heading,
            content: result.content
          });
        }
      }
    });
  }
  
  removeTranslation(language: string) {
    const dialogRef = this.dialog.open(TranslationDeleteModalComponent, {
      width: '530px'
    });
    
    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.translatedLanguages = this.translatedLanguages.filter(
          translation => translation.languageShort !== language
        );
      }
    });
  }
  
  cleanContent(content: string): string {
    if (!content) return '';
    content = content.replace(/<(p|br|div|span)>\s*<\/\1>/g, '');
    return content.trim();
  }

  onBack() {
    this.router.navigate(['/events/sport-events']);
  }
}