import { Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';

@Component({
  selector: 'app-publish-status-culture-event',
  standalone: true,
  imports: [
    TranslateModule,
    MatIconModule,
    MatDialogModule
  ],
  templateUrl: './publish-status-culture-event.component.html',
  styleUrl: './publish-status-culture-event.component.css'
})
export class PublishStatusCultureEventComponent {
  constructor(
    public dialogRef: MatDialogRef<PublishStatusCultureEventComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) { }

  ngOnInit(): void {
    console.log(this.data)
  }

  onNoClick(): void {
    this.dialogRef.close();
  }

  onYesClick(): void {
    this.dialogRef.close(true); // Потвърждение за изтриване
  }
}
