import { Injectable } from '@angular/core';
import { Observable, delay, of } from 'rxjs';
import { HttpClient, HttpParams } from '@angular/common/http';
import { environment } from '../../../environments/enviroment';
import { EventsGetModel } from '../../shared/interfaces/events/events-get.model';
import { EventsGetByIdModel } from '../../shared/interfaces/events/events-get-by-id.model';

@Injectable({
  providedIn: 'root'
})
export class EventsService {

  constructor(private http: HttpClient) {}
  
  
  getEvents(
      search:           string | null,
      fromDate:        string | null,
      toDate:          string | null,
      category:         string | null,
      sortColumn:       string | null,
      sortDirection:    string | null,
      pageNumber:       number,
      pageSize:         number,
    ): Observable<EventsGetModel> {
      return this.http.get<EventsGetModel>(`${environment.apiUrl}/Events/Admin`, {
        params: new HttpParams()
        .set('search', search ? search : '')
        .set('fromDate', fromDate ? fromDate : '')
        .set('toDate', toDate ? toDate : '')
        .set('category', category ? category : '')
        .set('sortColumn', sortColumn ? sortColumn : '')
        .set('sortDirection', sortDirection ? sortDirection : '')
        .set('pageNumber', pageNumber.toString())
        .set('pageSize', pageSize.toString())
      });
    }

  getEventById(eventId: number): Observable<EventsGetByIdModel> {
      return this.http.get<EventsGetByIdModel>(`${environment.apiUrl}/Events/${eventId}`);
  }

  addEvent(formData: FormData) {
    return this.http.post(`${environment.apiUrl}/Events`, formData);
  }

  editEvent(formData: FormData) {
    return this.http.put(`${environment.apiUrl}/Events/${formData.get('id')}`, formData);
  }

  deleteEvent(eventId: number) {
    return this.http.delete<number>(`${environment.apiUrl}/Events/${eventId}`)
  }

 
}