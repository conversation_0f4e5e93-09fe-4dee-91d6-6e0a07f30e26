import { RepairAreaPointsModel } from "./repair-area-points.model";
import { RepairImageModel } from "./repair-images.model";
import { RepairTranslationModel } from "./repair-translations.model";



export interface RepairGetModelGetByIdModel {
    name: string;
    description: string;
    latitude: number;
    longitude: number;
    startDate: string;
    endDate: string;
    id: number;
    translations: RepairTranslationModel[];
    areaPoints: RepairAreaPointsModel[];
    images: RepairImageModel[];
  }