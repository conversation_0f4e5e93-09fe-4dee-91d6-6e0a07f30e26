<form [formGroup]="form">
	<div class="NgxEditor__Wrapper" style="margin-bottom: 5px">
		<ngx-editor-menu [editor]="editor" [toolbar]="toolbar"></ngx-editor-menu>
		<ngx-editor
			formControlName="editorContent"
			[editor]="editor"
			[placeholder]="'...'"
			[ngModel]="content"
			(focusout)="onFocusoutChange($event)"
		></ngx-editor>
		<mat-error class="mat-error" *ngIf="form.controls['editorContent'].hasError('required')">
			{{ 'Content' | translate }} {{ 'isRequired' | translate  }}!
		</mat-error>
	</div>
</form>