import { CommonModule, DatePipe } from '@angular/common';
import { Component, DestroyRef, inject, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatDividerModule } from '@angular/material/divider';
import { MatSelectModule } from '@angular/material/select';
import { MatCardModule } from '@angular/material/card';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatInputModule } from '@angular/material/input';
import { MatMenuModule } from '@angular/material/menu';
import { MatSortModule, Sort } from '@angular/material/sort';
import { Router, RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { MatPseudoCheckboxModule } from '@angular/material/core';
import { MatDialog } from '@angular/material/dialog';
import { MatTooltip } from '@angular/material/tooltip';
import { GridColumnModel } from '../shared/interfaces/settings/grid-settings.model';
import { PhoneEventsPreviewComponent } from '../shared/phone-events-preview/phone-events-preview.component';
import { DeleteParkingComponent } from './delete-parking/delete-parking.component';
import { DetailsParkingComponent } from './details-parking/details-parking.component';
import { ParkingItemEntity } from '../shared/interfaces/parking/parking-get-item.model';
import { PaginationSortModel } from '../shared/interfaces/paginator/pagination-sort.model';
import { GridSetting } from '../shared/constants/grid-settings';
import { ParkingService } from '../core/services/parking.service';
import { tap } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

@Component({
  selector: 'app-parking',
  standalone: true,
  imports: [
  CommonModule,
  MatDividerModule,
  MatSelectModule,
  MatIconModule,
  MatButtonModule,
  MatCardModule,
  MatTableModule,
  MatPseudoCheckboxModule,
  ReactiveFormsModule,
  TranslateModule,
  MatInputModule,
  MatDatepickerModule,
  FormsModule,
  MatPaginatorModule,
  MatPaginator,
  MatTableModule,
  MatMenuModule,
  MatSortModule,
  RouterModule,
  ],
  templateUrl: './parking.component.html',
  styleUrl: './parking.component.css'
})
export class ParkingComponent {
  @ViewChild(MatPaginator) paginator!: MatPaginator | null;
  private destroyRef = inject(DestroyRef);

  parkingFilterForm!: FormGroup;
  
  private clickTimeout: any; // To manage single-click timeout
  private clickDelay = 300; // Time in milliseconds to distinguish single from double click
  private isDoubleClick = false; // Flag to track if double-click occurred
  protected today = new Date();
  protected firstDayOfMonth = new Date(this.today.getFullYear(), this.today.getMonth(), 1);
  protected gridColumns: GridColumnModel[] = [];
  protected gridColors: [] = []
  protected totalCount = 0;

  displayedColumns: string[] = ['id', 'name', 'actions'];

  protected dataSource: MatTableDataSource<ParkingItemEntity> =
        new MatTableDataSource<ParkingItemEntity>([]);

  protected paginationSort: PaginationSortModel = {
        pageNumber: GridSetting.defaultPageNumber,
        pageSize: GridSetting.defaultPageSize,
        sortColumn: GridSetting.defaultSortColumn,
        sortDirection: GridSetting.defaultSortDirection,
    };

 ngOnInit() {
  this.getParkingData();
  }

  constructor(
    private fb: FormBuilder,
    public dialog: MatDialog,
    private datePipe: DatePipe,
    private router: Router,
    private parkingService: ParkingService
    ) {
    this.parkingFilterForm = this.fb.group({
     id: [''],
     name: ['']
    });
  }

  private getParkingData(paginationSort = this.paginationSort): void {

    let name = this.parkingFilterForm.value.name;
    
        let { pageNumber, pageSize, sortColumn, sortDirection } = paginationSort;
        this.parkingService
          .getParkings(
            name,
            sortColumn,
            sortDirection,
            pageNumber,
            pageSize
          )
          .pipe(
            tap((data) => {
              this.dataSource = new MatTableDataSource<ParkingItemEntity>(data.items);
              this.totalCount = data.totalCount;
            }),
            takeUntilDestroyed(this.destroyRef)
          )
          .subscribe();
      }

       protected onPageChange(event: any) {
                  const pageIndex = event.pageIndex + 1; // Paginator index starts from 0, while your API starts from 1
                  const pageSize = event.pageSize;
                  this.paginationSort = {
                    ...this.paginationSort,
                    pageSize: pageSize,
                    pageNumber: pageIndex,
                  };
              
                  this.getParkingData();
                }
              
                protected onSortChange(sortState: Sort) {
                  this.paginationSort = {
                    ...this.paginationSort,
                    sortColumn: sortState.active,
                    sortDirection: sortState.direction,
                  };
                  this.getParkingData();
                }
              
                private resetPaginatorSort() {
                  if (this.paginator) {
                    this.paginator.firstPage();
                  }
                  this.paginationSort = {
                    pageNumber: GridSetting.defaultPageNumber,
                    pageSize: GridSetting.defaultPageSize,
                    sortColumn: GridSetting.defaultSortColumn,
                    sortDirection: GridSetting.defaultSortDirection,
                  };
                }

  openAddParkingDialog(): void {
    // Navigate to repairAdd route
    this.router.navigate(['/parking/parking-add']);
  }


  editElement(element: any): void {
    console.log(element.borderPoints);

    // Serialize border points to JSON string for query params
    const borderPointsParam = element.borderPoints ? JSON.stringify(element.borderPoints) : '[]';



    // Navigate to repairEdit route
    this.router.navigate(['/parking/parking-edit', element.id], {
      queryParams: {latitude: element.latitude, longitude: element.longitude, id: element.id, borderPoints: borderPointsParam, name: element.name },  // Send the whole element via router state
    });
  }

  deleteElement(element: any): void {
    const dialogRef = this.dialog.open(DeleteParkingComponent, {
      width: '530px',
      data: { id: element.id, content: element.content }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.getParkingData();
      }
    });
  }

  viewDetails(element: any): void {
    console.log(element)
    const dialogRef = this.dialog.open(DetailsParkingComponent, {
      width: '530px',
      data: element
    });

    dialogRef.afterClosed().subscribe(result => {

    });
  }

  onRowClick(event: MouseEvent, row: any): void {
    // Check if the event target is a button or inside a specific column
    const target = event.target as HTMLElement;
  
    // Prevent row click if the target is a button, icon, or menu
    if (
      target.closest('button') ||
      target.closest('mat-menu') ||
      target.closest('.actions-header')
    ) {
      return;
    }
  
    // Reset double-click flag
    this.isDoubleClick = false;

    // Set a timeout for single-click action
    this.clickTimeout = setTimeout(() => {
      if (!this.isDoubleClick) {
      }
    }, this.clickDelay);
  }
  
  onRowDoubleClick(event: MouseEvent, row: any): void {
    // Check if the event target is a button or inside a specific column
    const target = event.target as HTMLElement;
  
    // Prevent row double-click if the target is a button, icon, or menu
    if (
      target.closest('button') ||
      target.closest('mat-menu') ||
      target.closest('.actions-header')
    ) {
      return;
    }
  
    // Set double-click flag to true
    this.isDoubleClick = true;

    // Clear the single-click timeout to prevent its execution
    clearTimeout(this.clickTimeout);

    // Trigger double-click action
    this.editElement(row);
  }


  onSearch() {
   this.resetPaginatorSort();
   this.getParkingData();
  }

  changeSticky(element: string) {
    return this.gridColumns.find(column => column.columnName === element)?.fixed;
  }
}
