import {
  MAT_SELECT_CONFIG,
  MAT_SELECT_SCROLL_STRATEGY,
  MAT_SELECT_SCROLL_STRATEGY_PROVIDER,
  MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY,
  MAT_SELECT_TRIGGER,
  MatSelect,
  MatSelectChange,
  MatSelectModule,
  MatSelectTrigger,
  matSelectAnimations
} from "./chunk-ZBHKEFTT.js";
import "./chunk-CT6AZ7DA.js";
import "./chunk-6IZ2GXXI.js";
import "./chunk-RMPHBURT.js";
import "./chunk-E7CHA64F.js";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>ield,
  <PERSON><PERSON><PERSON>,
  MatLabel,
  MatPrefix,
  MatSuffix
} from "./chunk-LSI52TIE.js";
import "./chunk-S2IVXKQF.js";
import {
  MatOptgroup,
  MatOption
} from "./chunk-LY2EHQ3C.js";
import "./chunk-OQHNG3N5.js";
import "./chunk-EKDOZG4N.js";
import "./chunk-I3GN3EYL.js";
import "./chunk-MIJT6FLR.js";
import "./chunk-O4XABTPG.js";
import "./chunk-5OPE3T2R.js";
import "./chunk-4N4GOYJH.js";
import "./chunk-FHTVLBLO.js";
import "./chunk-WDMUDEB6.js";
export {
  MAT_SELECT_CONFIG,
  MAT_SELECT_SCROLL_STRATEGY,
  MAT_SELECT_SCROLL_STRATEGY_PROVIDER,
  MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY,
  MAT_SELECT_TRIGGER,
  MatError,
  MatFormField,
  MatHint,
  MatLabel,
  MatOptgroup,
  MatOption,
  MatPrefix,
  MatSelect,
  MatSelectChange,
  MatSelectModule,
  MatSelectTrigger,
  MatSuffix,
  matSelectAnimations
};
//# sourceMappingURL=@angular_material_select.js.map
