import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, BehaviorSubject, of, throwError } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { JwtHelperService } from '@auth0/angular-jwt';
import { environment } from '../../../environments/enviroment';

@Injectable({
  providedIn: 'root',
})
export class AuthService {
  private tokenKey = 'token'; // Key for storing the token in localStorage
  private refreshTokenKey = 'refreshToken'; // Key for storing the refresh token in localStorage
  private tokenSubject = new BehaviorSubject<string | null>(null);
  public token$: Observable<string | null> = this.tokenSubject.asObservable();

  private roleNavSubject = new BehaviorSubject<string | null>(null); // Holds the current role
  public roleNav$ = this.roleNavSubject.asObservable(); // Observable that components can subscribe to

  private userFullNameNavSubject = new BehaviorSubject<string | null>(null); // Holds the current role
  public userFullNameNav$ = this.userFullNameNavSubject.asObservable(); // Observable that components can subscribe to

  public isAuthenticated = false; // Boolean flag for authentication status


  private isUserLoggedIn = new BehaviorSubject<boolean>(
    this.getInitialLoggedIn()
  );

  private jwtHelper!: JwtHelperService; // Lazy initialize JwtHelperService

  constructor(private http: HttpClient) {
    this.loadToken();
  }

  private getJwtHelper(): JwtHelperService {
    // Initialize JwtHelperService when first needed
    if (!this.jwtHelper) {
      this.jwtHelper = new JwtHelperService();
    }
    return this.jwtHelper;
  }

  login(userName: string, password: string): Observable<any> {
    const url = `${environment.apiUrl}/Users/<USER>
    const body = { userName, password };

    return this.http.post(url, body).pipe(
      tap((response: any) => {
        if (response.accessToken && response.refreshToken) {
          this.setToken(response.accessToken, response.refreshToken);
          this.isAuthenticated = true;
          localStorage.setItem('loggedIn', 'true');
          this.setLoggedIn(true);
        }
      }),
      catchError((error) => {
        this.isAuthenticated = false;
        return of(error);
      })
    );
  }

  setToken(token: string, refreshToken?: string): void {
    localStorage.setItem(this.tokenKey, token);
    if (refreshToken) {
      localStorage.setItem(this.refreshTokenKey, refreshToken);
    }
    this.tokenSubject.next(token); // Update BehaviorSubject
  }

  getToken(): string | null {
    return localStorage.getItem(this.tokenKey);
  }

  getTokenSubject(): BehaviorSubject<string | null> {
    return this.tokenSubject;
  }

  getRefreshToken(): string | null {
    return localStorage.getItem(this.refreshTokenKey);
  }

  refreshToken(): Observable<any> {
    const url = `${environment.apiUrl}/Users/<USER>
    const refreshToken = this.getRefreshToken();
    if (!refreshToken) {
      return of(null);
    }
    
    return this.http.post(url, { refreshToken }).pipe(
      tap((response: any) => {
        if (response.accessToken) {
          this.setToken(response.accessToken, response.refreshToken);
        }
      }),
      catchError((error) => {
        this.logout(); // Clear the session if refreshing fails
        console.error('Refresh token error', error); // Log error for debugging
        return throwError(() => error); // Updated to use the new throwError syntax
      })
    );
  }

  logout(): void {
    localStorage.removeItem(this.tokenKey);
    localStorage.removeItem(this.refreshTokenKey);
    localStorage.removeItem('role');
    localStorage.removeItem('userFullName')
    this.isAuthenticated = false;
    this.setLoggedIn(false);
    this.tokenSubject.next(null); // Clear BehaviorSubject
    this.roleNavSubject.next(null); // Clear role on logout

  }

  private loadToken(): void {
    const token = this.getToken();
    this.isAuthenticated = !!token; // Set authentication status based on token presence
    this.tokenSubject.next(token);
  }

  public isUserAuthenticated = (): boolean => {
    const token = localStorage.getItem('token');
    if (token && !this.getJwtHelper().isTokenExpired(token!)) {
      return true;
    } else {
      return false;
    }
  };

  public getTokenExpirationDate = (): Date | null => {
    const token = localStorage.getItem('token');
    return this.getJwtHelper().getTokenExpirationDate(token!);
  };

  private getInitialLoggedIn(): boolean {
    const storedLoggedIn = localStorage.getItem('loggedIn');
    return storedLoggedIn ? JSON.parse(storedLoggedIn) : false;
  }

  setLoggedIn(data: boolean): void {
    localStorage.setItem('loggedIn', data.toString());
    this.isUserLoggedIn.next(data);
  }

  getLoggedIn(): Observable<boolean> {
    return this.isUserLoggedIn.asObservable();
  }

  getRole() {
   return localStorage.getItem('role')
  }

  setNavigationRole(role: string) {
    this.roleNavSubject.next(role);
  }

  getNavigationRole(): string | null {
    return this.roleNavSubject.getValue();
  }

  getUserFullNameName() {
    return localStorage.getItem('userFullName')
  }

  setNavigationUserFullName(userFullName: string) {
    this.userFullNameNavSubject.next(userFullName);
  }

  getNavigationUserFullName(): string | null {
    return this.userFullNameNavSubject.getValue();
  }

}
