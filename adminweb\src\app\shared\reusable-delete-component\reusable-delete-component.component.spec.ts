import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ReusableDeleteComponentComponent } from './reusable-delete-component.component';

describe('ReusableDeleteComponentComponent', () => {
  let component: ReusableDeleteComponentComponent;
  let fixture: ComponentFixture<ReusableDeleteComponentComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ReusableDeleteComponentComponent]
    })
    .compileComponents();

    fixture = TestBed.createComponent(ReusableDeleteComponentComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
