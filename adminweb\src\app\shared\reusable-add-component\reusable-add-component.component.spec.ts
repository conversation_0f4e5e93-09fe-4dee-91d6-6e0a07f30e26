import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ReusableAddComponentComponent } from './reusable-add-component.component';

describe('ReusableAddComponentComponent', () => {
  let component: ReusableAddComponentComponent;
  let fixture: ComponentFixture<ReusableAddComponentComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ReusableAddComponentComponent]
    })
    .compileComponents();

    fixture = TestBed.createComponent(ReusableAddComponentComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
