import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';

@Component({
  selector: 'app-details-news',
  standalone: true,
  imports: [
        TranslateModule,
        MatIconModule,
        MatDialogModule
  ],
  templateUrl: './details-news.component.html',
  styleUrl: './details-news.component.css'
})
export class DetailsNewsComponent {
  constructor(
    public dialogRef: MatDialogRef<DetailsNewsComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) {}

  onClose(): void {
    this.dialogRef.close();
  }
}
