<div class="login-container">
    <form [formGroup]="loginForm">
      <div class="login-form">
        <div>
          <h1> {{ 'Login' | translate }}</h1>
        </div>
        <mat-form-field appearance="outline">
          <mat-label>{{ 'Email' | translate }}</mat-label>
          <input matInput formControlName="email" type="email">
        </mat-form-field>
        <mat-form-field appearance="outline">
          <mat-label>{{ 'Password' | translate }}</mat-label>
          <input matInput formControlName="password" type="password">
        </mat-form-field>
        <div>
          <button mat-raised-button (click)="onSubmit()" color="primary" type="submit" [routerLink]="['/home']" routerLinkActive="router-link-active"> {{ 'Enter' | translate }}</button>
        </div>
  
      </div>
      <div class="login-image">
        <div class="login-image-overlay">
          <div class="login-image-texts">
            <div>
              <img src="../../assets/images/login/arrow-double-right.png" alt="arrow double right image">
              <p>{{ 'NewsAdministration' | translate }}</p>
            </div>
            <div>
              <img src="../../assets/images/login/arrow-double-right.png" alt="arrow double right image">
              <p>{{ 'LandmarksOverview' | translate }}</p>
            </div>
            <div>
              <img src="../../assets/images/login/arrow-double-right.png" alt="arrow double right image">
              <p>{{ 'BusinessRecords' | translate }}</p>
            </div>
            <div>
              <img src="../../assets/images/login/arrow-double-right.png" alt="arrow double right image">
              <p>{{ 'SportManagement' | translate }}</p>
            </div>
          </div>
        </div>
      </div>
    </form>
  </div>
  