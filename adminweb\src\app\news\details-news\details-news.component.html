<div class="contentPanel" fxLayout="column" xLayoutAlign="flex-start">
    <div fxLayout="row"
         fxFlex="0 1 100%"
         fxFlex.lt-lg="0 1 100%"
         fxFlex.lt-md="0 1 100%"
         class="panelHead">
          <span class="h1">{{ 'Details' | translate }}</span>
          <button mat-button class="close-icon" [mat-dialog-close]="false">
            <mat-icon>close</mat-icon>
          </button>
    </div>
  </div>
  <div mat-dialog-content>
    <div class="details-report-content-row">
      <strong>{{ 'ID' | translate}}</strong>
      <p>{{data.id}}</p>
    </div>
    <div class="details-report-content-row">
      <strong>{{ 'heading' | translate}}</strong>
      <p class="heading">{{data.heading}}</p>
    </div>
    <div class="details-report-content-row">
      <strong>{{ 'Content' | translate}}</strong>
      <p class="content">{{data.content}}</p>
    </div>
  </div>