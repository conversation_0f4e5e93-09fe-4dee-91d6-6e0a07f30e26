import { CommonModule } from '@angular/common';
import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';

@Component({
  selector: 'app-details-users',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    MatIconModule,
    MatDialogModule
  ],
  templateUrl: './details-users.component.html',
  styleUrl: './details-users.component.css'
})
export class DetailsUsersComponent {

  protected profilePicture = 'assets/images/user-profile-mock-picture.webp'


  constructor(
    public dialogRef: MatDialogRef<DetailsUsersComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) {}

  onClose(): void {
    this.dialogRef.close();
  }
}
