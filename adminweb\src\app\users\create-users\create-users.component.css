.contentPanel{
    padding: 2px;
    width: 100%;
    border-top: 8px solid var(--primary-color);
    justify-content: space-between;
    border-bottom: 1px solid #ccc;
  }
  
  .panelHead{
    margin: 20px;
    display: flex;
    align-items: center;
  }
  
  .h1{
    text-align: center;
    color: var(--color-main);
    width: 100%;
    border-bottom: 1px solid var(--color-gray-border) !important;
    margin: 0;
    font-family: 'Roboto', sans-serif;
    font-size: 20px;
    font-weight: 700;
    line-height: 16px;
  }
  
  .close-icon{
      cursor: pointer;
      font-size: 35px;
      border: none;
      background-color: transparent;
  }
  
  .save{
    background-color: var(--secondary-color);
    color: white;
  }
  
  .cancel {
    background-color: white;
    color: var(--secondary-color);
  }
  
  .dialog-window-form-save:hover,
  .dialog-window-form-cancel:hover {
    opacity: 0.8;
  }
  
  .mat-mdc-button>.mat-icon {
    margin-right: 0;
  }
  
  :host ::ng-deep .mat-mdc-text-field-wrapper:not(textarea){
    height: 44px;
  }
  
  :host ::ng-deep .mdc-floating-label{
    display: flex;
    width: fit-content;
  }
  
  mat-form-field mat-label label{
    margin-right: 5px;
  }
  
  .mat-mdc-dialog-actions.mat-mdc-dialog-actions-align-center{
    border-top: 1px solid #ccc;
  }
  
  .tabColumn{
    width: 100%;
    margin-left: 2.5px;
  }
  
  .tabColumn-1-fields{
    width: 100%;
    margin: 0 5px;
  }
  
  .tabColumn-2-fields{
    width: calc(32% + 3px);
    margin: 0 2.5px;
  }
  
  .tabColumn-3-fields{
    width: calc(48% + 7px);
    margin: 0 2.5px;
  }
  
  .tabColumn-4-fields{
    width: 24%;
    margin: 0 2.5px;
  }
  
  .mdc-text-field__input{
    height: 24px;
  }
  
  .description-field {
    width: 100%;
  }
  
  .mat-form-field-wrapper {
    display: flex;
    flex-direction: column;
  }
  
  .mat-form-field-outline {
    width: 100%;
    display: flex;
    flex-direction: column;
  }