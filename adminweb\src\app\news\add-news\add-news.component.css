.wrapper {
  margin: 10px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  height: 90vh;
  max-height: 90vh;
  overflow: hidden;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  background-color: #FFFFFF;
}

.form-container {
  margin-right: 20px; /* Adjust as needed for spacing */
}

.crud-buttons{
  display: flex;
  margin-left: 28%;
}

.id-field{
  margin-left: 10px;
  margin-bottom: -25px;
}

.add-button-container {
  margin-left: 10px;
  margin-top: 25px;
  display: flex;
  justify-content: flex-start;
}

.add-button {
  width: 225px;
  height: 40px;
  border-radius: 5px;
  box-shadow: 0px 1px 3px 0px #0000004D;
  box-shadow: 0px 4px 8px 3px #00000026;
  background: #FFFFFF;
  margin-right: 10px;
}

.image-container {
  position: relative;
  display: inline-block;
  width: 80%;
  height: 80%;
  margin-top: 10px;
  max-width: 600px;
}

.uploaded-image {
  width: 50%; /* Adjust width as needed */
  height: 50%; /* Adjust height as needed */
  object-fit: cover;
}

.remove-icon {
  position: absolute;
  top: 25px;
  background-color: rgba(255, 255, 255, 0.7); /* Slight white background for better visibility */
  border-radius: 50%;
}

.example-card {
  display: flex;
  margin-top: 30px;
  margin-bottom: 30px;
}

.card-content-title {
  margin-bottom: 10px;
}


.header {
padding-right: 200px;
}

.save-button{
  width: 135px;
  height: 40px;
  gap: 0px;
  border-radius: 5px;
  opacity: 0px;
  background-color: #383838 !important;
  color: white !important;
  box-shadow: 0px 1px 3px 0px #0000004D;
  box-shadow: 0px 4px 8px 3px #00000026;
  margin-right: 10px;
}

.cancel-button{
  width: 100px;
  height: 40px;
  gap: 0px;
  border-radius: 5px;
  opacity: 0px;
  background-color: #FFFFFF !important;
  box-shadow: 0px 1px 3px 0px #0000004D;
  box-shadow: 0px 4px 8px 3px #00000026;
}

.group-label {
  font-weight: bold;       /* Make the label bold */
  color: black;         /* Set a primary color */
  margin-top: 10px;       /* Add some space above */
  margin-bottom: 5px;     /* Add some space below */
  font-size: 1.1em;       /* Increase font size for visibility */
}

.heading{
  display: flex;
  align-items: center;
  justify-content: flex-start; /* Align items to the start */
  height: 63px;
  width: 711px; /* Use full width */
  padding: 20px 15px 20px 10px;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 63px;
  width: 100%; /* Use full width */
  padding: 20px 0px 20px 0px;
}

.header button {
  width: 100px;
  height: 40px;
  border-radius: 5px;
  margin-left: 10px;
  margin-right: 10px;
  box-shadow: 0px 1px 3px 0px #0000004D;
  box-shadow: 0px 4px 8px 3px #00000026;

}

.h1 {
  size: 20px;
  color: var(--primary-color);
  font-weight: bold;
}

.filter-container {
  align-items: center;
}

.wide-dropdown {
  width: 220px;
  margin-right: 10px;
}

.mat-form-field {
  width: 100%; /* Make the fields take full width */
  margin-right: 0; /* Remove horizontal margin */
}



.date,
.long-fields,
.priority {
width: calc(33% - 10px); /* Three fields per row with spacing between */
}

.date {
width: 167px;
border-radius: 2px;
border: 1px;
margin-right: 10px;
}

.long-fields {
width: 211px;
border: 1px;
margin-right: 10px;
}

.priority {
  width: 167px;
  border-radius: 2px;
  border: 1px;
  margin-right: 10px;
}

.left-big-border {
  height: 33px;
}

.mat-mdc-text-field-wrapper {
  height: 0px;
  flex: auto;
  will-change: auto;
}

.main-content {
  display: flex;
  padding-top: 23px;
  width: 100%; /* Use full width */
  justify-content: center;
}

.info-line {
  display: flex;
}

.value-text {
  font-weight: bold;
}

.demo-chart {
  height: 100%;
}

/* Table styling adjustments */
.mat-mdc-header-cell {
  border-bottom: none;
}

.mat-mdc-cell {
  border-bottom: none;
}

.mdc-data-table__cell,
.mdc-data-table__header-cell {
  padding: 0px;
}

.mat-mdc-header-cell.border-top-bottom {
  border-bottom: 1px solid #CCCCCC;
  border-top: 1px solid #CCCCCC;
}

/* Row height adjustments */
mat-row,
mat-footer-row {
  min-height: 28px;
}

.mat-mdc-table mat-header-row.mat-mdc-header-row,
.mat-mdc-table mat-row.mat-mdc-row,
.mat-mdc-table mat-footer-row.mat-mdc-footer-cell {
  height: 28px;
}

/* Button styling */
.button-more {
  width: 135px;
  text-align: center;
  background-color: #383838;
  color: white;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.center-cell {
  display: flex;
  justify-content: center;
}

.end-cell {
  display: flex;
  justify-content: flex-end;
}

/* Adjust text field wrapper height */
:host ::ng-deep .mat-mdc-text-field-wrapper {
  height: 44px;
}

:host ::ng-deep .mdc-text-field--outlined .mat-mdc-form-field-infix,
:host ::ng-deep .mdc-text-field--no-label .mat-mdc-form-field-infix {
  padding-top: 12px;
}

/* Responsive adjustments */
@media screen and (max-width: 768px) {
  .wrapper {
    overflow-y: auto;
    margin: 10px 5px; /* Adjusted margin for better spacing */
  }

  .header {
    width: 100%;
    padding: 10px;
  }

  .h1 {
    margin-left: 0px;
    font-size: 18px; /* Slightly smaller font size for smaller screens */
    margin-top: 10px; /* Space between button and heading */
  }

  .main-content {
    flex-direction: column;
    padding: 10px; /* Added padding for better spacing */
    width: 100%;
  }

  .filter-container {
    display: flex;
    flex-direction: column; /* Stack elements vertically */
    align-items: flex-start; /* Align elements to the start */
    gap: 15px; /* Add spacing between elements */
  }

  .wide-dropdown,
.mat-form-field,
.date,
.long-fields,
.description,
.search,
.priority {
width: 100%;
margin-right: 0;
margin-bottom: 10px; /* Added margin to separate elements */
}

}
  