import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { InterestPointsService } from '../../core/services/interest-points.service';
import { MessageService } from '../../core/services/message.service';


@Component({
  selector: 'app-verification-modal',
  standalone: true,
  imports: [
  TranslateModule,
  MatIconModule,
  MatDialogModule
  ],
  templateUrl: './verification-modal.component.html',
  styleUrl: './verification-modal.component.css'
})
export class VerificationModalComponent {
constructor(
    public dialogRef: MatDialogRef<VerificationModalComponent>,
    private interestPointsService: InterestPointsService,
    private messageService: MessageService,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) { }

  onNoClick(): void {
    this.dialogRef.close();
  }

  onYesClick(): void {
    this.interestPointsService.verifyInterestPoint(this.data.id).subscribe(
      {
        next: (response) => {
          this.messageService.showMessage(["VerificationSuccessfully"], 'success');
          this.dialogRef.close(true);
        },
        error: (error: any) => {
          console.error('Error deleting interest point:', error);
          // Handle error as per your application's requirement
        }
      }
    );
  }
}
