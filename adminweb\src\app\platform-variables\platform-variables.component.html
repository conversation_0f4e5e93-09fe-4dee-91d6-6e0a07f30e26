<form [formGroup]="platformVariablesFilterForm">
    <div class="wrapper">
      <div class="header">
          <div class="filter-container">
  
          <!-- name-->
          <mat-form-field appearance="outline" class="mat-form-field">
            <mat-label>{{'name' | translate}}</mat-label>
            <input matInput formControlName="name">
        </mat-form-field>

 
            <mat-radio-group class="radio-group" formControlName="isDeleted" (change)="onDeletedStatusChange($event)">
              <mat-radio-button value="true">{{"deleted" | translate}}</mat-radio-button>
              <mat-radio-button value="false">{{"notDeleted" | translate}}</mat-radio-button>
              <mat-radio-button value="null">{{"All" | translate}}</mat-radio-button> <!-- Represents the null value -->
            </mat-radio-group>

  
              <button mat-icon-button type="submit" class="search-button" (click)="onSearch()">
                  <mat-icon>search</mat-icon>
              </button>
          </div>
      </div>
      <mat-divider></mat-divider>
  
      <div class="main-content">
          <div class="title">
              <span class="left-big-border"></span>
              <h1 class="h1">{{ 'variables' | translate }}</h1>
          </div>
  
          <div class="add-button-container">
              <button mat-raised-button tabindex="1" class="add-button" (click)="openAddDialog()">
                  <mat-icon>add</mat-icon>
                  <span>{{ 'AddVariables' | translate }}</span>
              </button>
          </div>
      </div>
  
      <div class="card-column">
        <mat-card class="custom-card table-container">
            <div class="mat-elevation-z8 custom-table">
              <table mat-table [dataSource]="dataSource" matSort>
  
             <!-- ID Column -->
             <ng-container matColumnDef="id">
                <th mat-header-cell *matHeaderCellDef mat-sort-header class="custom-header"> {{'ID' | translate}} </th>
                <td mat-cell *matCellDef="let element" class="custom-cell"> {{element.id}} </td>
            </ng-container>
            
            <!-- Name Column -->
            <ng-container matColumnDef="name">
                <th mat-header-cell *matHeaderCellDef mat-sort-header class="custom-header"> {{'name' | translate}} </th>
                <td mat-cell *matCellDef="let element" class="custom-cell"> {{element.name}} </td>
            </ng-container>
            
            <!-- Value Column -->
            <ng-container matColumnDef="value">
                <th mat-header-cell *matHeaderCellDef mat-sort-header class="custom-header"> {{'value' | translate}} </th>
                <td mat-cell *matCellDef="let element" class="custom-cell"> {{element.value}} </td>
            </ng-container>
            
            <!-- Type Column -->
            <ng-container matColumnDef="type">
                <th mat-header-cell *matHeaderCellDef mat-sort-header class="custom-header"> {{'type' | translate}} </th>
                <td mat-cell *matCellDef="let element" class="custom-cell"> {{element.type}} </td>
            </ng-container>
            
            <!-- Created By Column -->
            <ng-container matColumnDef="createdBy">
                <th mat-header-cell *matHeaderCellDef mat-sort-header class="custom-header"> {{'createdBy' | translate}} </th>
                <td mat-cell *matCellDef="let element" class="custom-cell"> {{element.createdBy}} </td>
            </ng-container>
            
            <!-- Created Date Column -->
            <ng-container matColumnDef="createdDate">
                <th mat-header-cell *matHeaderCellDef mat-sort-header class="custom-header"> {{'createdDate' | translate}} </th>
                <td mat-cell *matCellDef="let element" class="custom-cell"> {{element.createdDate | date}} </td>
            </ng-container>
            
            <!-- Last Modified By Column -->
            <ng-container matColumnDef="lastModifiedBy">
                <th mat-header-cell *matHeaderCellDef mat-sort-header class="custom-header"> {{'lastModifiedBy' | translate}} </th>
                <td mat-cell *matCellDef="let element" class="custom-cell"> {{element.lastModifiedBy}} </td>
            </ng-container>
            
            <!-- Last Modified Date Column -->
            <ng-container matColumnDef="lastModifiedDate">
                <th mat-header-cell *matHeaderCellDef mat-sort-header class="custom-header"> {{'lastModifiedDate' | translate}} </th>
                <td mat-cell *matCellDef="let element" class="custom-cell"> {{element.lastModifiedDate | date}} </td>
            </ng-container>
            
            <!-- Deleted By Column -->
            <ng-container matColumnDef="deletedBy">
                <th mat-header-cell *matHeaderCellDef mat-sort-header class="custom-header"> {{'deletedBy' | translate}} </th>
                <td mat-cell *matCellDef="let element" class="custom-cell"> {{element.deletedBy}} </td>
            </ng-container>
            
            <!-- Deleted Date Column -->
            <ng-container matColumnDef="deletedDate">
                <th mat-header-cell *matHeaderCellDef mat-sort-header class="custom-header"> {{'deletedDate' | translate}} </th>
                <td mat-cell *matCellDef="let element" class="custom-cell"> {{element.deletedDate | date}} </td>
            </ng-container>
              
                <!-- Actions Column -->
                    <ng-container matColumnDef="actions">
                        <th mat-header-cell *matHeaderCellDef class="custom-header actions-header"> {{ 'actions' | translate }} </th>
                        <td mat-cell *matCellDef="let element" class="custom-cell">
                        <button mat-icon-button [matMenuTriggerFor]="menu">
                            <mat-icon>more_horizontal</mat-icon>
                        </button>
                        <mat-menu #menu="matMenu">
                            <div class="menu-actions">
                            <!-- If deletedBy is set (item is deleted), show only Restore -->
                            <ng-container *ngIf="element.deletedBy; else normalActions">
                                <button mat-menu-item (click)="restoreElement(element)" class="restore-button">
                                <mat-icon>restore</mat-icon> {{ 'restore' | translate }}
                                </button>
                            </ng-container>
                    
                            <!-- Normal actions for non-deleted items -->
                            <ng-template #normalActions>
                                <button mat-menu-item (click)="viewDetails(element)" class="details-button">
                                <mat-icon>info</mat-icon> {{ 'details' | translate }}
                                </button>
                                <button mat-menu-item (click)="editElement(element)" class="edit-button">
                                <mat-icon>edit</mat-icon> {{ 'edit' | translate }}
                                </button>
                                <button mat-menu-item (click)="deleteElement(element)" class="delete-button">
                                <mat-icon>delete</mat-icon> {{ 'delete' | translate }}
                                </button>
                            </ng-template>
                            </div>
                        </mat-menu>
                        </td>
                    </ng-container>
  
              
                <tr mat-header-row *matHeaderRowDef="displayedColumns; sticky: true" class="custom-row"></tr>
                <tr mat-row *matRowDef="let row; columns: displayedColumns;" class="custom-row"
                (dblclick)="row.deletedBy ? null : onRowDoubleClick($event, row)"></tr>
              
              </table>
  
  
            </div>
        </mat-card>
        <div class="mat-paginator-sticky table-footer">
          <div class="buttons table-footer-left">
           
           </div>
           <div>
            <mat-paginator 
              [length]="totalCount"
              [pageSize]="paginationSort.pageSize"
              [pageSizeOptions]="[25, 50, 100]"
              showFirstLastButtons
              (page)="onPageChange($event)"
              aria-label="Select page of periodic elements">
            </mat-paginator>
           </div>
        </div>
      </div>
  
    </div>
  </form>