import { TextFieldModule } from '@angular/cdk/text-field';
import { CommonModule } from '@angular/common';
import { Component, Inject, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatOptionModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { TranslateModule } from '@ngx-translate/core';
import { UsersService } from '../../core/services/user.service';
import { MessageService } from '../../core/services/message.service';
import { AuthService } from '../../core/services/auth.service';

@Component({
  selector: 'app-edit-users',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    MatIconModule,
    MatDialogModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatDatepickerModule,
    MatSelectModule,
    MatOptionModule,
    TextFieldModule
  ],
  templateUrl: './edit-users.component.html',
  styleUrl: './edit-users.component.css'
})
export class EditUsersComponent {
  editFormUser: FormGroup;
  protected roles: string[] = []; // Initialize as an empty array or as needed
  protected filteredRoles: string[] = []; // Initialize as an empty array or as needed
  protected profilePicture: string | ArrayBuffer | null = null
  protected removeProfilePicture: boolean = false; // Flag to send to the backend

  constructor(
    public dialogRef: MatDialogRef<EditUsersComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private fb: FormBuilder, private usersService: UsersService, private messageService: MessageService, private authService: AuthService) {

      this.editFormUser = this.fb.group({
        id:              [this.data.id],
        firstName:       [this.data.firstName, Validators.required],
        lastName:        [this.data.lastName, Validators.required],
        role:            [this.data.role, Validators.required],
        removeProfilePicture: [false]
      });
  }

  ngOnInit(): void {
    this.roles = this.data.roles
    const userRole = this.authService.getRole()
    this.filteredRoles = this.roles.filter(role => userRole === 'AdminPlatform' || role !== 'AdminPlatform');

    if (this.data.role === 'AdminPlatform' && userRole !== 'AdminPlatform') {
      this.editFormUser.disable(); // Disables the entire form
    }
  
    //MOCK PICTURE FOR NOW
    this.profilePicture = 'assets/images/user-profile-mock-picture.webp'

    /*this.profilePicture = this.data.profilePicture || null;*/
    

  }

  removePicture(): void {
    this.profilePicture = null; // Clear the image preview
    this.removeProfilePicture = true; // Mark it for backend removal
    this.editFormUser.patchValue({ removeProfilePicture: true });
  }



  onCancel(): void {
    this.dialogRef.close();
  }

  onSubmit(): void {
    if (this.editFormUser.valid) {

      let updatedUser = this.editFormUser.value;

      this.usersService.editUser(updatedUser).subscribe({
        next: (response) => {
          this.messageService.showMessage(["UpdateSuccessfully"], 'success');
          // Handle the response if needed
          this.dialogRef.close(updatedUser);
        },
        error: (error) => {
          // Handle error
          console.error('Error updating user:', error);
          // Optionally, display an error message to the user
        }
      });
    }
  }
}
