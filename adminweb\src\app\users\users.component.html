<form [formGroup]="usersFilterForm">
    <div class="wrapper">
      <div class="header">
          <div class="filter-container">
             <!-- search-->
             <mat-form-field appearance="outline" class="mat-form-field">
                <mat-label>{{'SearchFilter' | translate}}</mat-label>
                <input matInput formControlName="search">
            </mat-form-field>

            <!-- role -->
            <mat-form-field appearance="outline" color="primary" class="wide-dropdown">
                <mat-label>{{'role' | translate}}</mat-label>
                <mat-select formControlName="role">
                    <mat-option></mat-option>
                    <mat-option *ngFor="let role of roles" [value]="role">
                        {{ role | translate }}
                    </mat-option>
                </mat-select>
              </mat-form-field>

              <button mat-icon-button type="submit" class="search-button" (click)="onSearch()">
                  <mat-icon>search</mat-icon>
              </button>

          </div>
      </div>
      <mat-divider></mat-divider>

      <div class="main-content">
          <div class="title">
              <span class="left-big-border"></span>
              <h1 class="h2">{{ 'users' | translate }}</h1>
          </div>

          <div class="add-button-container">
              <button mat-raised-button tabindex="1" class="add-button" (click)="openAddUserDialog()">
                  <mat-icon>add</mat-icon>
                  <span>{{ 'AddUser' | translate }}</span>
              </button>
          </div>
      </div>

      <div class="card-column">
        <mat-card class="custom-card">
            <div class=" custom-table">
                <div class="custom-table-container">
                    <table mat-table [dataSource]="dataSource" matSort (matSortChange)="onSortChange($event)">
  
                        <!-- ID Column -->
                        <ng-container matColumnDef="id">
                            <th mat-header-cell *matHeaderCellDef mat-sort-header class="custom-header"> {{'ID' | translate}} </th>
                            <td mat-cell *matCellDef="let element" class="custom-cell"> {{element.id}} </td>
                        </ng-container>
                    
                        <!-- Email Column -->
                        <ng-container matColumnDef="email">
                            <th mat-header-cell *matHeaderCellDef mat-sort-header class="custom-header"> {{'email' | translate}} </th>
                            <td mat-cell *matCellDef="let element" class="custom-cell"> {{element.email}} </td>
                        </ng-container>
                    
                        <!-- firstName Column -->
                        <ng-container matColumnDef="firstName">
                            <th mat-header-cell *matHeaderCellDef mat-sort-header class="custom-header"> {{'firstName' | translate}} </th>
                            <td mat-cell *matCellDef="let element" class="custom-cell"> {{element.firstName}} </td>
                        </ng-container>

                        <!-- lastName Column -->
                        <ng-container matColumnDef="lastName">
                            <th mat-header-cell *matHeaderCellDef mat-sort-header class="custom-header"> {{'lastName' | translate}} </th>
                            <td mat-cell *matCellDef="let element" class="custom-cell"> {{element.lastName}} </td>
                        </ng-container>
                    
                        <!-- Role Column -->
                        <ng-container matColumnDef="role">
                            <th mat-header-cell *matHeaderCellDef mat-sort-header class="custom-header"> {{'role' | translate}} </th>
                            <td mat-cell *matCellDef="let element" class="custom-cell"> {{element.role | translate}} </td>
                        </ng-container>
                    
                        <!-- Actions Column -->
                        <ng-container matColumnDef="actions">
                            <th mat-header-cell *matHeaderCellDef class="custom-header"> {{'actions' | translate}} </th>
                            <td mat-cell *matCellDef="let element" class="custom-cell">
                                <button mat-icon-button [matMenuTriggerFor]="menu">
                                    <mat-icon>more_horizontal</mat-icon>
                                </button>
                                <mat-menu #menu="matMenu">
                                    <div class="menu-actions">
                                        <button mat-menu-item (click)="viewDetails(element)" class="details-button">
                                            <mat-icon>info</mat-icon>
                                        </button>
                                        <button mat-menu-item (click)="editElement(element)" class="edit-button">
                                            <mat-icon>edit</mat-icon>
                                        </button>
                                        <button mat-menu-item (click)="resetPassword(element)" class="key-button">
                                            <mat-icon>key</mat-icon>
                                        </button>
                                        <button mat-menu-item (click)="deleteElement(element)" class="delete-button">
                                            <mat-icon>delete</mat-icon>
                                        </button>
                                    </div>
                                </mat-menu>
                            </td>
                        </ng-container>
                    
                        <tr mat-header-row *matHeaderRowDef="displayedColumns; sticky: true" class="custom-row"></tr>
                        <tr mat-row *matRowDef="let row; columns: displayedColumns;" class="custom-row" (dblclick)="onRowDoubleClick($event, row)"></tr>
                    </table>
            </div>
            </div>
        </mat-card>
        <div class="bottom">

           <mat-paginator
           [length]="totalCount"
           [pageSize]="paginationSort.pageSize"
           [pageSizeOptions]="[25, 50, 100]"
           showFirstLastButtons
           aria-label="Select page of periodic elements"
           (page)="onPageChange($event)"
           class="mat-paginator-sticky">
           </mat-paginator>

      </div>
    </div>

    </div>
  </form>
