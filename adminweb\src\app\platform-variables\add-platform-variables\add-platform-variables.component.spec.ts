import { ComponentFixture, TestBed } from '@angular/core/testing';

import { AddPlatformVariablesComponent } from './add-platform-variables.component';

describe('AddPlatformVariablesComponent', () => {
  let component: AddPlatformVariablesComponent;
  let fixture: ComponentFixture<AddPlatformVariablesComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [AddPlatformVariablesComponent]
    })
    .compileComponents();

    fixture = TestBed.createComponent(AddPlatformVariablesComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
