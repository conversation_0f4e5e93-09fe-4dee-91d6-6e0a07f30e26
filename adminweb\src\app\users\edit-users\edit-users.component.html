<div class="contentPanel" fxLayout="column" xLayoutAlign="flex-start">
    <div fxLayout="row"
         fxFlex="0 1 100%"
         fxFlex.lt-lg="0 1 100%"
         fxFlex.lt-md="0 1 100%"
         class="panelHead">
          <span class="h1">{{ 'EditUser' | translate }}</span>
          <button mat-button class="close-icon" [mat-dialog-close]="false">
            <mat-icon class="close-modal">close</mat-icon>
          </button>
    </div>
  </div>
  <div mat-dialog-content class="form-container">
    <form [formGroup]="editFormUser" (ngSubmit)="onSubmit()">
      <div fxLayout="column" fxLayoutAlign="flex-start">
        <div class="tabContent" fxLayout="row wrap" fxLayoutAlign="flex-start">
          <div class="tabColumn" fxLayout="row wrap">
            <mat-dialog-content class="mat-dialog-content-wrapper">

              <mat-form-field appearance="outline" class="tabColumn-3-fields disabled-field">
                <mat-label>{{ 'ID' | translate }}</mat-label>
                <input matInput formControlName="id" readonly>
              </mat-form-field>

              <mat-form-field appearance="outline"  class="tabColumn-3-fields" hideRequiredMarker="true">
                <mat-label>{{ 'firstName' | translate}}<span>*</span></mat-label>
                <input matInput formControlName="firstName">
              </mat-form-field>

              <mat-form-field appearance="outline"  class="tabColumn-3-fields" hideRequiredMarker="true">
                <mat-label>{{ 'lastName' | translate}}<span>*</span></mat-label>
                <input matInput formControlName="lastName">
              </mat-form-field>

              <mat-form-field appearance="outline" class="tabColumn-3-fields" hideRequiredMarker="true">
                <mat-label class="custom-label">{{'role' | translate}}<span>*</span></mat-label>
                <mat-select formControlName="role">
                  <mat-option *ngFor="let role of filteredRoles" [value]="role">
                    {{ role | translate }}
                </mat-option>
                </mat-select>
              </mat-form-field>

              <div fxLayout="column" fxLayoutAlign="center center" class="profile-picture-container" *ngIf="profilePicture">
                <img [src]="profilePicture" alt="Profile Picture" class="profile-picture">
                <button mat-icon-button color="warn" [disabled]="editFormUser.disabled || editFormUser.invalid" (click)="removePicture()" class="remove-button">
                  <mat-icon class="remove-photo-icon">delete</mat-icon>
                </button>
              </div>
              

            </mat-dialog-content>
          </div>
        </div>
      </div>
      <mat-dialog-actions align="center" class="mat-dialog-content-wrapper">
        <button type="submit" [disabled]="editFormUser.disabled || editFormUser.invalid" mat-raised-button tabindex="1" class="save">
          <mat-icon>save</mat-icon>
          <span>{{ 'Save' | translate }}</span>
        </button>
        <button mat-raised-button mat-dialog-close tabindex="-1" class="cancel">{{ 'Cancel' | translate }}</button>
      </mat-dialog-actions>
    </form>

  </div>