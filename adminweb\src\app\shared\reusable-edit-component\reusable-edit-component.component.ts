import { CommonModule } from '@angular/common';
import { Component, ElementRef, Input, OnDestroy, OnInit, ViewChild, ViewContainerRef } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatOptionModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { MatDividerModule } from '@angular/material/divider';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';  // Import MatInputModule
import { MatSelectModule } from '@angular/material/select';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateModule, TranslateService } from '@ngx-translate/core';

import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { EditorComponent } from '../editor/editor.component';
import { InterestPointsGetByIdModel } from '../interfaces/interest-points/interest-points-get-by-id.model';
import { environment } from '../../../environments/enviroment';
import { GoogleMapsService } from '../../core/services/google.maps.service';
import { MessageService } from '../../core/services/message.service';
import { InterestPointsService } from '../../core/services/interest-points.service';
import { TranslationModalComponent } from '../translation-modal/translation-modal.component';
import { TranslationDeleteModalComponent } from '../translation-delete-modal/translation-delete-modal.component';

@Component({
  selector: 'app-reusable-edit-component',
  standalone: true,
  imports: [
  CommonModule, 
  TranslateModule,
  MatIconModule,
  MatDialogModule,
  ReactiveFormsModule,
  MatFormFieldModule,
  MatInputModule,
  MatButtonModule,
  MatDatepickerModule,
  MatSelectModule,
  MatOptionModule,
  MatDividerModule,
  MatCardModule,
  EditorComponent,
  FormsModule,
  MatSlideToggleModule
  ],
  templateUrl: './reusable-edit-component.component.html',
  styleUrl: './reusable-edit-component.component.css'
})
export class ReusableEditComponentComponent implements OnInit, OnDestroy {
  @ViewChild('fileInput') fileInput: ElementRef | undefined;
  @ViewChild('mapContainer') mapContainer!: ElementRef;
  @ViewChild('markerContainer', { read: ViewContainerRef, static: true }) markerContainer!: ViewContainerRef;
  
  // Inputs for reusability
    @Input() returnPath: string = '/tourism';
    @Input() category: string = 'InterestPoint';
    @Input() componentTitle: string = 'Add Interest Point';
    @Input() markerIcon: string = 'assets/images/landmark-icon.png';

  editInterestPointForm!: FormGroup;
  protected content = '';
  protected imageSrcs: { 
    id?: number;
    fileName: string; 
    contentType: string; 
    extension: string; 
    content: string; 
    isCover: boolean;
  }[] = [];
  private interestPointId!: number;
  protected interestPoint!: InterestPointsGetByIdModel;
  protected selectedThumbnailIndex: number | null = null; // Track selected thumbnail
  protected mapHeight: number = 0;
  protected mapWidth: number = 0;
  protected center!: google.maps.LatLngLiteral;
  private currentMarker: any = null;
  protected zoom = 13;
  private map: google.maps.Map | undefined;
  private dayMapId = environment.dayMapId;
  //private aggregatedGroupsData: AggregatedGroupModel[] = [];
  private marker: google.maps.marker.AdvancedMarkerElement | undefined;
  protected translatedLanguages: { languageShort: string, languageFullName: string, heading: string, content: string }[] = [];
  protected showImages: boolean = true;
  

  constructor(private translate: TranslateService, private fb: FormBuilder, private router: Router, private route: ActivatedRoute,
  private googleMapsService: GoogleMapsService, public dialog: MatDialog, private messageService: MessageService, private interestPointsService: InterestPointsService
  ) {
    this.editInterestPointForm = this.fb.group({
      id: [''],
      heading: ['', [Validators.required, Validators.maxLength(60)]],
      templateContent: ['', Validators.required],
      thumbnailIndex: [null], // Add control for thumbnail index
      latitude: [null],  // Latitude is now optional
      longitude: [null], // Longitude is now optional
      phoneNumber: [''],
      website: ['']
    });
  }

  ngOnInit(): void {
    this.initializeGoogleMaps();

    this.route.queryParams.subscribe(params => {
      this.category = params['category'] || 'InterestPoint';
    
    // Decode the returnPath if it exists
    if (params['returnPath']) {
      this.returnPath = decodeURIComponent(params['returnPath']);
    } else {
      this.returnPath = '/tourism';
    }
    
    this.componentTitle = params['title'] || 'Add Interest Point';
    this.markerIcon = params['markerIcon'] || 'assets/images/landmark-icon.png'
      
      // Any other setup based on these parameters
    });

    this.route.paramMap.subscribe((params) => {
      const id = params.get('id');
      if (id) {
        this.interestPointId = Number(id);
        this.loadInterestPoint(this.interestPointId);
      }
    });
  
  }

  protected get latitudeControl(): FormControl<string> {
      return this.editInterestPointForm.get(
        'latitude'
      ) as FormControl<string>;
    }
  
    protected get longitudeControl(): FormControl<string> {
      return this.editInterestPointForm.get(
        'longitude'
      ) as FormControl<string>;
    }
  
    protected get headingControl(): FormControl<string> {
      return this.editInterestPointForm.get(
        'heading'
      ) as FormControl<string>;
    }

    protected get phoneNumberControl(): FormControl<string> {
      return this.editInterestPointForm.get(
        'phoneNumber'
      ) as FormControl<string>;
    }
  
    protected get websiteControl(): FormControl<string> {
      return this.editInterestPointForm.get(
        'website'
      ) as FormControl<string>;
    }

    protected get templateContentControl(): FormControl<string> {
      return this.editInterestPointForm.get(
        'templateContent'
      ) as FormControl<string>;
    }

    private async initializeGoogleMaps() {
      try {
        // Load necessary libraries
        await this.googleMapsService.loadLibraries();
  
        this.center = {
          lat: 42.482798, 
          lng: 26.503206 
        };
  
        this.map = await this.googleMapsService.initializeMap(
          this.mapContainer.nativeElement,
          this.center,
          this.zoom,
          this.dayMapId,
          'day'
        );
  
        // Set click listener
     this.googleMapsService.setClickListener((latLng) => {
        // Remove the previous marker if it exists
        if (this.currentMarker) {
          this.currentMarker.setMap(null); // Remove the marker from the map
        }
        
        // Add a new marker at the clicked position and store the reference
        this.currentMarker = this.googleMapsService.addAdvancedMarker(latLng, 2, undefined);
        
        this.editInterestPointForm.patchValue({
          latitude: latLng.lat,
          longitude: latLng.lng
        });
      });
  
      } catch (error) {
        console.error('Error loading Google Maps:', error);
      }
    }

    loadInterestPoint(id: number): void {
      this.interestPointsService.getInterestPointById(id).subscribe({
        next: (data: InterestPointsGetByIdModel) => {
          this.editInterestPointForm.patchValue({
            id: data.id,
            heading: data.name,
            templateContent: data.description,
            latitude: data.latitude,
            longitude: data.longitude,
            phoneNumber: data.phoneNumber,
            website: data.website
          });
          
          // Set the map marker with the received coordinates (latitude and longitude) if they exist
          if (data.latitude && data.longitude) {
            const latLng = {
              lat: data.latitude,
              lng: data.longitude
            };
    
            // If the map is initialized, add the marker immediately
            if (this.map) {
              this.addMarkerToMap(latLng);
            } else {
              // Otherwise, wait for the map to initialize, then add the marker
              const checkMap = setInterval(() => {
                if (this.map) {
                  this.addMarkerToMap(latLng);
                  clearInterval(checkMap);
                }
              }, 100);
            }
          }

          if(data.description) {
            this.content = data.description;
          }

          if(data.images) {
            this.imageSrcs = data.images.map(img => ({
              id: img.id,
              fileName: img.key,
              contentType: 'image/png',  // You can adjust this depending on the image type
              extension: img.key.split('.').pop() || '',
              content: img.preSignedUrl, // Directly using the URL here
              isCover: img.isCover
            }));
            
            // Find the cover image and set it as selected
            const coverIndex = this.imageSrcs.findIndex(img => img.isCover);
            this.selectedThumbnailIndex = coverIndex !== -1 ? coverIndex : null;
            
            // Update the form control
            this.editInterestPointForm.patchValue({
              thumbnailIndex: this.selectedThumbnailIndex
            });
          }

          if (data.translations && data.translations.length > 0) {
            this.translatedLanguages = data.translations.map(translation => ({
              languageShort: translation.languageCode || 'en', // Assuming your API returns languageCode
              languageFullName: translation.languageName || 'English', // Assuming your API returns languageName
              heading: translation.name || '',
              content: translation.description || ''
            }));
            
          }
              
        },
        error: (error) => {
          console.error('Error fetching interest point details:', error);
        }
      });
    }
    
    // Add this helper method to keep your code DRY
    private addMarkerToMap(latLng: {lat: number, lng: number}): void {
      // Remove any existing marker first
      if (this.currentMarker) {
        this.currentMarker.setMap(null);
      }
      
      // Add the new marker at the specified position
      this.googleMapsService.addAdvancedMarker(
        latLng,
        2,
        undefined,
        undefined,
        this.markerIcon,
        undefined,
        true
      );
      
      // Optional: pan to the marker location
      if (this.map) {
        this.map.panTo(latLng);
      }
    }

  
  get form() {
    return this.editInterestPointForm.controls;
  }

  onSubmit() {
    // Update isCover property for all images before submitting
    this.updateCoverImage();
    
    this.editInterestPointForm.patchValue({
      templateContent: this.content
    });
  
    const templateContent = this.cleanContent(this.editInterestPointForm.value.templateContent);
  
    if (this.editInterestPointForm.valid && templateContent.length > 0) {
      // Patch form data with thumbnail selection
      this.editInterestPointForm.patchValue({ thumbnailIndex: this.selectedThumbnailIndex });
  
      // Initialize storage for files and existing images
      let coverFile: File | null = null;
      let coverImageId: number | null = null;
      const files: File[] = [];
      const existingImageIds: number[] = [];
  
      // Process images: differentiate between new and existing images
      this.imageSrcs.forEach((src, index) => {
        if (src.id) {
          // Existing image: check if it's the cover
          if (index === this.selectedThumbnailIndex) {
            coverImageId = src.id;
          } else {
            existingImageIds.push(src.id);
          }
        } else {
          // New image: convert base64 to file
          const byteArray = this.convertBase64ToByteArray(src.content);
          const blob = new Blob([byteArray], { type: src.contentType });
          const file = new File([blob], src.fileName, { type: src.contentType });
  
          if (index === this.selectedThumbnailIndex) {
            coverFile = file;
          } else {
            files.push(file);
          }
        }
      });
  
      // Prepare translation data
      const translations = this.translatedLanguages.map(translation => ({
        name: translation.heading,
        description: translation.content,
        languageName: translation.languageFullName,
        languageCode: translation.languageShort,
      }));
  
      // Prepare form data
      const formData = new FormData();
      formData.append('id', this.interestPointId.toString());
      formData.append('Name', this.editInterestPointForm.value.heading);
      formData.append('Description', this.editInterestPointForm.value.templateContent);
      formData.append('Category', this.category);
      
      // Only append latitude and longitude if they exist
      const latitude = this.editInterestPointForm.value.latitude;
      const longitude = this.editInterestPointForm.value.longitude;

      console.log(latitude, longitude);
      
      if (latitude !== null && latitude !== undefined && longitude !== null && longitude !== undefined) {
        formData.append('Latitude', latitude.toString());
        formData.append('Longitude', longitude.toString());
      }
      
      if(this.editInterestPointForm.value.phoneNumber) {
        formData.append('PhoneNumber', this.editInterestPointForm.value.phoneNumber.toString()); 
      }
        
      if(this.editInterestPointForm.value.website) {
        formData.append('Website', this.editInterestPointForm.value.website.toString()); 
      }
  
      // Append existing image IDs
      existingImageIds.forEach((id, index) => {
        formData.append(`ExistingImageIds[${index}]`, id.toString());
      });
  
      // Append translation data
      translations.forEach((translation, index) => {
        formData.append(`Translations[${index}].name`, translation.name);
        formData.append(`Translations[${index}].description`, translation.description);
        formData.append(`Translations[${index}].languageCode`, translation.languageCode);
        formData.append(`Translations[${index}].languageName`, translation.languageName);
      });
  
      // Append new images (excluding the cover)
      files.forEach((file, index) => {
        formData.append(`Files[${index}]`, file, file.name);
      });
  
      // Append cover image (either as an ID or a new file)
      if (coverImageId !== null) {
        formData.append('ExistingCoverId', String(coverImageId));
      } else if (coverFile !== null) {
        formData.append('Cover', coverFile);
      }
      
      // Submit to backend
      this.interestPointsService.editInterestPoint(formData).subscribe({
        next: (response) => {
          console.log('Interest point updated successfully', response);
          this.messageService.showMessage(["UpdateSuccessfully"], 'success');
          this.router.navigate([this.returnPath]);
        },
        error: (error) => {
          console.error('Error updating interest point', error);
          this.messageService.showMessage(["ErrorSubmittingForm"], 'error');
        }
      });
    } else {
      this.messageService.showMessage(["PleaseCompleteTheForm"], 'error');
    }
  }
  

  // Utility function to convert base64 to byte array
  convertBase64ToByteArray(base64: string): Uint8Array {
    const binaryString = atob(base64.split(',')[1]); // Decode base64 string
    const byteArray = new Uint8Array(binaryString.length);
    for (let i = 0; i < binaryString.length; i++) {
      byteArray[i] = binaryString.charCodeAt(i);
    }
    return byteArray;
  }

  // Update the selectThumbnail method to handle isCover property
  selectThumbnail(index: number) {
    this.selectedThumbnailIndex = index;
    this.editInterestPointForm.patchValue({
      thumbnailIndex: this.selectedThumbnailIndex,
    });
    
    // Update isCover property for all images
    this.updateCoverImage();
  }
  
  // New method to update isCover property for all images
  private updateCoverImage() {
    if (this.imageSrcs.length > 0 && this.selectedThumbnailIndex !== null) {
      // Reset isCover for all images
      this.imageSrcs.forEach((img, i) => {
        img.isCover = i === this.selectedThumbnailIndex;
      });
    }
  }

  onChangeContent() {
    const templateContentControl = this.editInterestPointForm.get('templateContent');
    if (templateContentControl) {
      templateContentControl.setValue(this.content);
    }
  }

 openFileDialog() {
     if (this.fileInput) {
       this.fileInput.nativeElement.click();
     }
   }
 
   onFileSelected(event: Event) {
     const fileInput = event.target as HTMLInputElement;
     if (fileInput.files && fileInput.files.length > 0) {
       Array.from(fileInput.files).forEach((file: File) => {
         const reader = new FileReader();
   
         // Read the file as a data URL to get the content in base64
         reader.onload = (e: any) => {
           // Create a file object with dynamically populated properties
           const fileDetails = {
             fileName: file.name,              // Get the file name
             contentType: file.type,           // Get the content type (MIME type)
             extension: file.name.split('.').pop() || '', // Extract the file extension
             content: e.target.result,         // Base64 encoded content of the file
             isCover: false                    // Initialize isCover to false
           };
   
           // Push the file details to the array
           this.imageSrcs.push(fileDetails);
   
           // If this is the first image, automatically set it as the thumbnail and cover
           if (this.imageSrcs.length === 1) {
             this.selectedThumbnailIndex = 0;
             this.imageSrcs[0].isCover = true;
           }
           
           // Update the form control
           this.editInterestPointForm.patchValue({
             thumbnailIndex: this.selectedThumbnailIndex
           });
         };
   
         // Read the file as base64
         reader.readAsDataURL(file);
       });
     }
   }
   
   removePhoto(index: number) {
     const removedWasCover = this.imageSrcs[index].isCover;
     this.imageSrcs.splice(index, 1); // Remove image at the given index

     if (this.fileInput) {
      this.fileInput.nativeElement.value = '';
    }
     
     // Update thumbnail selection if needed
     if (this.selectedThumbnailIndex === index) {
       // If we removed the selected thumbnail, select the first image if available
       this.selectedThumbnailIndex = this.imageSrcs.length > 0 ? 0 : null;
       
       // If the removed image was the cover and we have other images, set the new selection as cover
       if (removedWasCover && this.imageSrcs.length > 0 && this.selectedThumbnailIndex !== null) {
         this.imageSrcs[this.selectedThumbnailIndex].isCover = true;
       }
     } else if (this.selectedThumbnailIndex !== null && index < this.selectedThumbnailIndex) {
       // If we removed an image before the selected one, update the index
       this.selectedThumbnailIndex--;
     }
     
     // Update the form control
     this.editInterestPointForm.patchValue({
       thumbnailIndex: this.selectedThumbnailIndex
     });
   }
 
   openTranslationModal(language?: string) {
      
    let heading;
    let content;

    if(language === undefined) {
     heading = this.editInterestPointForm.value.heading?.trim();
     content = this.editInterestPointForm.value.templateContent?.trim(); // Read from the form
    } else if(language !== undefined) {
      const selectedTranslation = this.translatedLanguages.find(
        translation => translation.languageShort === language
      );

      heading = selectedTranslation?.heading
      content = selectedTranslation?.content
    }

     content = this.cleanContent(content);
 
     if (!heading || !content) {
       this.messageService.showMessage(['HeadingAndContentCannotBeEmpty'], 'error')
       return; // Exit function if both are empty
     }
     
     const dialogRef = this.dialog.open(TranslationModalComponent, {
       width: '693px',
       height: '95vh',
       data: {
         heading: heading,
         content: content,
         selectedLanguage: language
       }
     });
 
     dialogRef.afterClosed().subscribe(result => {
       if (result) {
         // Check if the translation already exists for the selected language
         const existingTranslationIndex = this.translatedLanguages.findIndex(
           translation => translation.languageShort === result.languageShort
         );
 
         if (existingTranslationIndex !== -1) {
           // If it exists, update the existing translation
           this.translatedLanguages[existingTranslationIndex] = {
             languageShort: result.languageShort,
             languageFullName: result.languageFullName,
             heading: result.heading,
             content: result.content
           };
         } else {
           // If it doesn't exist, add a new translation
           this.translatedLanguages.push({
             languageShort: result.languageShort,
             languageFullName: result.languageFullName,
             heading: result.heading,
             content: result.content
           });
         }
       }
     });
   }
 
   removeTranslation(language: string) {
     const dialogRef = this.dialog.open(TranslationDeleteModalComponent, {
       width: '530px'
     });
 
     dialogRef.afterClosed().subscribe(result => {
       if (result) {
         // Proceed with deletion if the user confirmed
         this.translatedLanguages = this.translatedLanguages.filter(
           translation => translation.languageShort !== language
         );
       }
     });
   }
 
   cleanContent(content: string): string {
     if (!content) return ''; // Ensure it's not undefined/null
   
     // Remove empty paragraphs, line breaks, and spaces
     content = content.replace(/<(p|br|div|span)>\s*<\/\1>/g, '');
   
     return content.trim(); // Trim remaining spaces
   }

   toggleImages() {
    this.showImages = !this.showImages;
   }
 
   onBack() {
     this.router.navigate([this.returnPath]);
   }

   ngOnDestroy(): void {
    this.googleMapsService.unloadMap();
   }
}
