import { CommonModule } from '@angular/common';
import { Component, ElementRef, Input, OnDestroy, OnInit, ViewChild, ViewContainerRef } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatOptionModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { MatDividerModule } from '@angular/material/divider';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { EditorComponent } from '../editor/editor.component';
import { environment } from '../../../environments/enviroment';
import { GoogleMapsService } from '../../core/services/google.maps.service';
import { MessageService } from '../../core/services/message.service';
import { InterestPointsService } from '../../core/services/interest-points.service';
import { TranslationModalComponent } from '../translation-modal/translation-modal.component';
import { TranslationDeleteModalComponent } from '../translation-delete-modal/translation-delete-modal.component';


@Component({
  selector: 'app-reusable-add-component',
  standalone: true,
  imports: [
    CommonModule, 
    TranslateModule,
    MatIconModule,
    MatDialogModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatDatepickerModule,
    MatSelectModule,
    MatOptionModule,
    MatDividerModule,
    MatCardModule,
    EditorComponent,
    FormsModule,
    MatSlideToggleModule
  ],
  templateUrl: './reusable-add-component.component.html',
  styleUrl: './reusable-add-component.component.css'
})
export class ReusableAddComponentComponent implements OnInit, OnDestroy {
  @ViewChild('fileInput') fileInput: ElementRef | undefined;
  @ViewChild('mapContainer') mapContainer!: ElementRef;
  @ViewChild('markerContainer', { read: ViewContainerRef, static: true }) markerContainer!: ViewContainerRef;

  // Inputs for reusability
  @Input() returnPath: string = '/tourism';
  @Input() category: string = 'InterestPoint';
  @Input() componentTitle: string = 'Add Interest Point';
  
  addInterestPointForm!: FormGroup;
  protected content = '';
  protected imageSrcs: { 
    fileName: string; 
    contentType: string; 
    extension: string; 
    content: string; 
  }[] = [];

  protected selectedThumbnailIndex: number | null = null;
  protected mapHeight: number = 0;
  protected mapWidth: number = 0;
  protected center!: google.maps.LatLngLiteral;
  private currentMarker: any = null;
  protected zoom = 13;
  private map: google.maps.Map | undefined;
  private dayMapId = environment.dayMapId;
  private marker: google.maps.marker.AdvancedMarkerElement | undefined;
  protected translatedLanguages: { languageShort: string, languageFullName: string, heading: string, content: string }[] = [];
  protected showImages: boolean = true;

  constructor(
    private translate: TranslateService, 
    private fb: FormBuilder, 
    private router: Router, 
    private googleMapsService: GoogleMapsService, 
    public dialog: MatDialog, 
    private messageService: MessageService,
    private interestPointsService: InterestPointsService,
    private route: ActivatedRoute
  ) {
    this.addInterestPointForm = this.fb.group({
      heading: ['', [Validators.required, Validators.maxLength(60)]],
      templateContent: ['', Validators.required],
      thumbnailIndex: [null],
      latitude: [null],
      longitude: [null],
      phoneNumber: [''],
      website: ['']
    });
  }

  protected get latitudeControl(): FormControl<string> {
    return this.addInterestPointForm.get('latitude') as FormControl<string>;
  }

  protected get longitudeControl(): FormControl<string> {
    return this.addInterestPointForm.get('longitude') as FormControl<string>;
  }

  protected get headingControl(): FormControl<string> {
    return this.addInterestPointForm.get('heading') as FormControl<string>;
  }

  protected get phoneNumberControl(): FormControl<string> {
    return this.addInterestPointForm.get('phoneNumber') as FormControl<string>;
  }

  protected get websiteControl(): FormControl<string> {
    return this.addInterestPointForm.get('website') as FormControl<string>;
  }

  ngOnInit(): void {
    this.route.queryParams.subscribe(params => {
      this.category = params['category'] || 'InterestPoint';
    
    // Decode the returnPath if it exists
    if (params['returnPath']) {
      this.returnPath = decodeURIComponent(params['returnPath']);
    } else {
      this.returnPath = '/tourism';
    }
    
    this.componentTitle = params['title'] || 'Add Interest Point';
      
      // Any other setup based on these parameters
    });

    this.initializeGoogleMaps();
  }

  private async initializeGoogleMaps() {
    try {
      await this.googleMapsService.loadLibraries();

      this.center = {
        lat: 42.482798, 
        lng: 26.503206 
      };

      this.map = await this.googleMapsService.initializeMap(
        this.mapContainer.nativeElement,
        this.center,
        this.zoom,
        this.dayMapId,
        'day'
      );

      this.googleMapsService.setClickListener((latLng) => {
        if (this.currentMarker) {
          this.currentMarker.setMap(null);
        }
        
        this.currentMarker = this.googleMapsService.addAdvancedMarker(latLng, 2, undefined);
        
        this.addInterestPointForm.patchValue({
          latitude: latLng.lat,
          longitude: latLng.lng
        });
      });

    } catch (error) {
      console.error('Error loading Google Maps:', error);
    }
  }

  get form() {
    return this.addInterestPointForm.controls;
  }

  onSubmit() {
    const templateContent = this.cleanContent(this.addInterestPointForm.value.templateContent);

    if(this.addInterestPointForm.valid && templateContent.length > 0) {
      this.addInterestPointForm.patchValue({
        thumbnailIndex: this.selectedThumbnailIndex,
      });

      const files = this.imageSrcs.map((src) => {
        const byteArray = this.convertBase64ToByteArray(src.content);
        const blob = new Blob([byteArray], { type: src.contentType });
        const file = new File([blob], src.fileName, { type: src.contentType });
        return file;
      });

      const coverFile = this.selectedThumbnailIndex !== null && this.imageSrcs[this.selectedThumbnailIndex]
        ? files[this.selectedThumbnailIndex]
        : null;

      const filesWithoutCover = files.filter((file, index) => index !== this.selectedThumbnailIndex);
    
      const translations = this.translatedLanguages.map((translation) => ({
        name: translation.heading,
        description: translation.content,
        languageName: translation.languageFullName,
        languageCode: translation.languageShort,
      }));
    
      const formData = new FormData();
    
      formData.append('Name', this.addInterestPointForm.value.heading);
      formData.append('Description', this.addInterestPointForm.value.templateContent);
      formData.append('Category', this.category);

      if(this.addInterestPointForm.value.latitude) {
      formData.append('Latitude', this.addInterestPointForm.value.latitude.toString());
      }

      if(this.addInterestPointForm.value.longitude) {
      formData.append('Longitude', this.addInterestPointForm.value.longitude.toString());
      }

      if(this.addInterestPointForm.value.phoneNumber) {
        formData.append('PhoneNumber', this.addInterestPointForm.value.phoneNumber.toString()); 
      }
        
      if(this.addInterestPointForm.value.website) {
        formData.append('Website', this.addInterestPointForm.value.website.toString()); 
      }
    
      translations.forEach((translation, index) => {
        formData.append(`Translations[${index}].name`, translation.name);
        formData.append(`Translations[${index}].description`, translation.description);
        formData.append(`Translations[${index}].languageCode`, translation.languageCode);
        formData.append(`Translations[${index}].languageName`, translation.languageName);
      });
      
      filesWithoutCover.forEach((file, index) => {
        formData.append(`Files[${index}]`, file, file.name);
      });

      if (this.selectedThumbnailIndex !== null && this.imageSrcs[this.selectedThumbnailIndex]) { 
        const coverSrc = this.imageSrcs[this.selectedThumbnailIndex];
      
        if (coverSrc) {
          const byteArray = this.convertBase64ToByteArray(coverSrc.content);
          const blob = new Blob([byteArray], { type: coverSrc.contentType });
          const file = new File([blob], coverSrc.fileName, { type: coverSrc.contentType });
      
          formData.append('Cover', file);
        }
      }
      
      this.interestPointsService.addInterestPoint(formData).subscribe({
        next: (response) => {
          console.log(`${this.componentTitle} added successfully`, response);
        },
        error: (error) => {
          console.error(`Error adding ${this.componentTitle.toLowerCase()}`, error);
        },
        complete: () => {
          console.log(`${this.componentTitle} request complete`);
          this.messageService.showMessage(["CreateSuccessfully"], 'success');
          this.router.navigate([this.returnPath]);
        }
      });
    } else {
      this.messageService.showMessage(["PleaseCompleteTheForm"], 'error');
    }
  }
  
  convertBase64ToByteArray(base64: string): Uint8Array {
  const binaryString = atob(base64.split(',')[1]);
  const byteArray: Uint8Array = new Uint8Array(binaryString.length);
  for (let i = 0; i < binaryString.length; i++) {
    byteArray[i] = binaryString.charCodeAt(i);
  }
  return byteArray;
}

  selectThumbnail(index: number) {
    this.selectedThumbnailIndex = index;
    this.addInterestPointForm.patchValue({
      thumbnailIndex: this.selectedThumbnailIndex,
    });
  }

  onChangeContent() {
    const templateContentControl = this.addInterestPointForm.get('templateContent');
    if (templateContentControl) {
      templateContentControl.setValue(this.content);
    }
  }

  openFileDialog() {
    if (this.fileInput) {
      this.fileInput.nativeElement.click();
    }
  }

  onFileSelected(event: Event) {
    const fileInput = event.target as HTMLInputElement;
    if (fileInput.files && fileInput.files.length > 0) {
      Array.from(fileInput.files).forEach((file: File) => {
        const reader = new FileReader();
  
        reader.onload = (e: any) => {
          const fileDetails = {
            fileName: file.name,
            contentType: file.type,
            extension: file.name.split('.').pop() || '',
            content: e.target.result
          };
  
          this.imageSrcs.push(fileDetails);
  
          if (this.imageSrcs.length === 1) {
            this.selectedThumbnailIndex = 0;
          }
        };
  
        reader.readAsDataURL(file);
      });
    }
  }
  
  removePhoto(index: number) {
    this.imageSrcs.splice(index, 1);

    if (this.fileInput) {
      this.fileInput.nativeElement.value = '';
    }
    
    if (this.selectedThumbnailIndex === index) {
      this.selectedThumbnailIndex = this.imageSrcs.length > 0 ? 0 : null;
    } else if (this.selectedThumbnailIndex !== null && index < this.selectedThumbnailIndex) {
      this.selectedThumbnailIndex--;
    }
  }

  openTranslationModal(language?: string) {
    const heading = this.addInterestPointForm.value.heading?.trim();
    let content = this.addInterestPointForm.value.templateContent?.trim();
    
    content = this.cleanContent(content);
    
    if (!heading || !content) {
      this.messageService.showMessage(['HeadingAndContentCannotBeEmpty'], 'error');
      return;
    }
    
    let existingTranslation = undefined;
    if (language) {
      existingTranslation = this.translatedLanguages.find(
        translation => translation.languageShort === language
      );
    }
    
    const dialogRef = this.dialog.open(TranslationModalComponent, {
      width: '693px',
      height: '95vh',
      data: {
        heading: existingTranslation ? existingTranslation.heading : this.addInterestPointForm.value.heading,
        content: existingTranslation ? existingTranslation.content : this.content,
        selectedLanguage: language
      }
    });
  
    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        const existingTranslationIndex = this.translatedLanguages.findIndex(
          translation => translation.languageShort === result.languageShort
        );
  
        if (existingTranslationIndex !== -1) {
          this.translatedLanguages[existingTranslationIndex] = {
            languageShort: result.languageShort,
            languageFullName: result.languageFullName,
            heading: result.heading,
            content: result.content
          };
        } else {
          this.translatedLanguages.push({
            languageShort: result.languageShort,
            languageFullName: result.languageFullName,
            heading: result.heading,
            content: result.content
          });
        }
      }
    });
  }
  
  removeTranslation(language: string) {
    const dialogRef = this.dialog.open(TranslationDeleteModalComponent, {
      width: '530px'
    });
    
    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.translatedLanguages = this.translatedLanguages.filter(
          translation => translation.languageShort !== language
        );
      }
    });
  }
  
  cleanContent(content: string): string {
    if (!content) return '';
    content = content.replace(/<(p|br|div|span)>\s*<\/\1>/g, '');
    return content.trim();
  }

  toggleImages() {
    this.showImages = !this.showImages;
  }

  onBack() {
    this.router.navigate([this.returnPath]);
  }

  ngOnDestroy(): void {
    this.googleMapsService.unloadMap();
  }
}