import { CommonModule } from '@angular/common';
import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatOptionModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatDialogModule } from '@angular/material/dialog';
import { MatDividerModule } from '@angular/material/divider';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';  // Import MatInputModule
import { MatSelectModule } from '@angular/material/select';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { EditorComponent } from '../../shared/editor/editor.component';

@Component({
  selector: 'app-edit-news',
  standalone: true,
  imports: [
  CommonModule, 
  TranslateModule,
  MatIconModule,
  MatDialogModule,
  ReactiveFormsModule,
  MatFormFieldModule,
  MatInputModule,
  MatButtonModule,
  MatDatepickerModule,
  MatSelectModule,
  MatOptionModule,
  MatDividerModule,
  MatCardModule,
  EditorComponent
  ],
  templateUrl: './edit-news.component.html',
  styleUrl: './edit-news.component.css'
})
export class EditNewsComponent {
  @ViewChild('fileInput') fileInput: ElementRef | undefined;


  editNewsForm!: FormGroup;
  protected content = '';
  protected imageSrc: string | ArrayBuffer | null = null; // To hold the before image source
  private landmarkId!: number;
  protected landmark: any;

  constructor(private translate: TranslateService, private fb: FormBuilder, private router: Router, private route: ActivatedRoute) {
    this.editNewsForm = this.fb.group({
      id: [''],
      heading: ['', [Validators.maxLength(60)]],
      templateContent: ['']
    });
  }

  ngOnInit(): void {

    this.route.paramMap.subscribe((params) => {
      const id = params.get('id');
      if (id) {
        this.landmarkId = Number(id);
        this.loadLandmarkData(Number(id));
      }
    });

    this.route.queryParams.subscribe((queryParams) => {
      // Assuming you passed the landmark data like heading, content, published in queryParams
      this.landmark = {
        heading: queryParams['heading'],
        content: queryParams['content'],
        published: queryParams['published']
      };

      if (this.landmark) {

        const htmlContent = `<p>${this.landmark.content}</p>`;

        this.content = htmlContent;

        this.editNewsForm.patchValue({
          id: this.landmarkId,
          heading: this.landmark.heading,
          templateContent: htmlContent
        });
      }

    });

  }

  private loadLandmarkData(id: number): void {
  //LOADING MOCK FOR NOW
  }

  get form() {
    return this.editNewsForm.controls;
  }

  onSubmit() {
  console.log(this.content)
  }

  onChangeContent() {
    const templateContentControl = this.editNewsForm.get('templateContent');
    if (templateContentControl) {
      templateContentControl.setValue(this.content);
    }
  }

  openFileDialog() {
      if (this.fileInput) {
        this.fileInput.nativeElement.click();
      }
  }

  onFileSelected(event: Event) {
    const fileInput = event.target as HTMLInputElement;
    if (fileInput.files && fileInput.files[0]) {
      const file = fileInput.files[0];
      const reader = new FileReader();

      reader.onload = (e: any) => {
          this.imageSrc = e.target.result; // Set the image source for before image
      };

      reader.readAsDataURL(file); // Read the file as a data URL
    }
  }

  removePhoto() {
      this.imageSrc = null; // Clear the before image source
      if (this.fileInput) {
        this.fileInput.nativeElement.value = ''; // Clear the file input for before image
      }
  }

  onBack() {
    this.router.navigate(['/news']);
  }
}
