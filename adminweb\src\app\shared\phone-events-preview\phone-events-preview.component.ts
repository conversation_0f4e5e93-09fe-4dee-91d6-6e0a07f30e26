import { CommonModule } from '@angular/common';
import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { MatDividerModule } from '@angular/material/divider';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';

@Component({
  selector: 'app-phone-events-preview',
  standalone: true,
  imports: [
    TranslateModule,
    MatIconModule,
    MatDialogModule,
    CommonModule,
    MatDividerModule
  ],
  templateUrl: './phone-events-preview.component.html',
  styleUrl: './phone-events-preview.component.css'
})
export class PhoneEventsPreviewComponent {
  protected image = '../../../assets/images/empty-page-new.png';
  constructor(@Inject(MAT_DIALOG_DATA) public data: any) {}
}
