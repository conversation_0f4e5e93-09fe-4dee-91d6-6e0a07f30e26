import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { EventsService } from '../../../core/services/events.service';
import { MessageService } from '../../../core/services/message.service';

@Component({
  selector: 'app-delete-celebrations',
  standalone: true,
  imports: [
    TranslateModule,
    MatIconModule,
    MatDialogModule
  ],
  templateUrl: './delete-celebrations.component.html',
  styleUrl: './delete-celebrations.component.css'
})
export class DeleteCelebrationsComponent {
constructor(
      public dialogRef: MatDialogRef<DeleteCelebrationsComponent>,
      private eventsService: EventsService,
      private messageService: MessageService,
      @Inject(MAT_DIALOG_DATA) public data: any
    ) { }
  
    onNoClick(): void {
      this.dialogRef.close();
    }
  
    onYesClick(): void {
      this.eventsService.deleteEvent(this.data.id).subscribe(
        {
          next: (response) => {
            this.messageService.showMessage(["DeleteSuccessfully"], 'success');
            this.dialogRef.close(true);
          },
          error: (error: any) => {
            console.error('Error deleting interest point:', error);
            // Handle error as per your application's requirement
          }
        }
      );
    }
}
