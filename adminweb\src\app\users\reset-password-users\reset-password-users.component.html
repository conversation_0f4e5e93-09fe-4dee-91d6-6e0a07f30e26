<div class="contentPanel" fxLayout="column" xLayoutAlign="flex-start">
    <div fxLayout="row"
         fxFlex="0 1 100%"
         fxFlex.lt-lg="0 1 100%"
         fxFlex.lt-md="0 1 100%"
         class="panelHead">
          <span class="h1">{{ 'PasswordReset' | translate }}</span>
          <button mat-button class="close-icon" [mat-dialog-close]="false">
            <mat-icon>close</mat-icon>
          </button>
    </div>
  </div>
  <div mat-dialog-content>
    <form [formGroup]="resetPasswordForm">
        <div fxLayout="column" fxLayoutAlign="flex-start">
          <div class="tabContent" fxLayout="row wrap" fxLayoutAlign="flex-start">
            <div class="tabColumn" fxLayout="row wrap">
              <mat-dialog-content class="mat-dialog-content-wrapper">

                <mat-form-field appearance="outline"  class="tabColumn-1-fields">
                  <mat-label>{{ 'Password' | translate}}</mat-label>
                  <input matInput formControlName="newPassword" type="password" [type]="passwordVisible ? 'text' : 'password'">

                  <button mat-icon-button 
                  matSuffix 
                  type="button" 
                  (click)="togglePasswordVisibility()">
                  <mat-icon>{{ passwordVisible ? 'visibility' : 'visibility_off' }}</mat-icon>
                  </button>

                  <button 
                  class="renew-button" 
                  mat-icon-button matSuffix 
                  (click)="generatePassword()" 
                  type="button" 
                  matTooltip="{{ 'GenerateASecurePassword' | translate }}"
                  matTooltipPosition="above">
                    <mat-icon>autorenew</mat-icon>
                  </button>
                </mat-form-field>
  
                <mat-form-field appearance="outline"  class="tabColumn-1-fields">
                  <mat-label>{{ 'RepeatPassword' | translate}}</mat-label>
                  <input matInput formControlName="confirmPassword" type="password" [type]="passwordVisible ? 'text' : 'password'">
                </mat-form-field>
  
              </mat-dialog-content>
            </div>
          </div>
        </div>
        <mat-dialog-actions align="center" class="mat-dialog-content-wrapper">
          <button type="submit" mat-raised-button tabindex="1" class="save" (click)="onSubmit()">
            <mat-icon>save</mat-icon>
            <span>{{ 'Save' | translate }}</span>
          </button>
          <button mat-raised-button mat-dialog-close tabindex="-1" class="cancel">{{ 'Cancel' | translate }}</button>
        </mat-dialog-actions>
      </form>
  </div>