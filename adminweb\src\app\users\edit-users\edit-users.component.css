.contentPanel{
    padding: 2px;
    width: 100%;
    border-top: 8px solid var(--primary-color);
    justify-content: space-between;
    border-bottom: 1px solid #ccc;
  }
  
  .panelHead{
    margin: 20px;
    display: flex;
    align-items: center;
  }
  
  .h1{
    text-align: center;
    color: var(--color-main);
    width: 100%;
    border-bottom: 1px solid var(--color-gray-border) !important;
    margin: 0;
    font-family: 'Roboto', sans-serif;
    font-size: 20px;
    font-weight: 700;
    line-height: 16px;
    margin-left: 65px;
  }

  .close-modal {
    margin-left: 5px;
  }

  .profile-picture-container {
    margin: 20px 0;
    position: relative;
    text-align: center;
  }
  
  .profile-picture {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid #ccc;
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease-in-out;
  }
  
  .remove-button {
    position: absolute;
    top: -5px;
    right: -5px;
    background-color: rgba(255, 0, 0, 0.8);
    color: white;
    width: 40px;
    height: 40px;
    box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.2);
    transition: background-color 0.3s ease, transform 0.3s ease;
  }

  .profile-picture:hover {
    transform: scale(1.05);
  }

  .remove-button:hover {
    background-color: red;
    transform: scale(1.1);
  }

  .remove-photo-icon {
    margin-left: -3.7px;
    margin-top: -3.5px;
  }
  
  .placeholder {
    width: 120px;
    height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px dashed #ccc;
    border-radius: 50%;
  }
  
  .upload-icon {
    font-size: 25px;
    color: #ccc;
  }
  
  
  .close-icon{
      cursor: pointer;
      font-size: 35px;
      border: none;
      background-color: transparent;
  }
  
  .save{
    background-color: var(--secondary-color);
    color: white;
  }

  .save:disabled {
    background-color: #b0b0b0 !important; /* Greyed-out background */
    color: #ffffff !important; /* White text */
    cursor: not-allowed !important; /* Shows a "not-allowed" cursor */
    opacity: 0.6; /* Slightly faded */
    transition: 0.3s;
  }

  .remove-button:disabled {
    background-color: #b0b0b0 !important; /* Greyed-out background */
    color: #ffffff !important; /* White text */
    cursor: not-allowed !important; /* Shows a "not-allowed" cursor */
    opacity: 0.6; /* Slightly faded */
    transition: 0.3s;
  }
  
  .cancel {
    background-color: white;
    color: var(--secondary-color);
  }
  
  
  .tabColumn{
    width: 100%;
    margin-left: 2.5px;
  }
  
  .tabColumn-1-fields{
    width: 100%;
    /* margin: 0 5px; */
  }
  
  .tabColumn-2-fields{
    width: 49%;
    margin: 0 2.5px;
  }
  
  .tabColumn-3-fields{
    width: 32.5%;
    margin: 0 2.5px;
  }
  
  .tabColumn-4-fields{
    width: 24%;
    margin: 0 2.5px;
  }
  
  :ng-deep .mdc-dialog .mdc-dialog__content {
    padding: 20px !important;
  }
  
  ::ng-deep .mat-mdc-dialog-container-with-actions .mat-mdc-dialog-content {
    padding: 20px;
  }
  
  :host ::ng-deep .mat-mdc-text-field-wrapper {
    height: 44px;
  }
  
  .disabled-field input {
    pointer-events: stroke;
    color: #9e9e9e; /* Светъл цвят на текста за индикация */
  }
  
  .disabled-field input:hover {
    cursor: not-allowed; /* Забранителен знак при hover */
  }