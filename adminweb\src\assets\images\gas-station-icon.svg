<svg width="28" height="36" viewBox="0 0 28 36" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_f_740_3717)">
<ellipse cx="12" cy="29" rx="4" ry="2" fill="black" fill-opacity="0.12"/>
</g>
<mask id="path-2-outside-1_740_3717" maskUnits="userSpaceOnUse" x="1.33301" y="1.25" width="26" height="32" fill="black">
<rect fill="white" x="1.33301" y="1.25" width="26" height="32"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M20.9016 22.5711C23.791 20.5234 25.6663 17.2229 25.6663 13.5C25.6663 7.2868 20.443 2.25 13.9997 2.25C7.55635 2.25 2.33301 7.2868 2.33301 13.5C2.33301 17.2229 4.20832 20.5234 7.09774 22.5711C9.28849 24.1629 12.3745 26.672 13.0951 30.7071C13.1745 31.1518 13.5479 31.4966 13.9997 31.4966C14.4514 31.4966 14.8249 31.1518 14.9043 30.7071C15.6248 26.6719 18.7109 24.1629 20.9016 22.5711Z"/>
</mask>
<path fill-rule="evenodd" clip-rule="evenodd" d="M20.9016 22.5711C23.791 20.5234 25.6663 17.2229 25.6663 13.5C25.6663 7.2868 20.443 2.25 13.9997 2.25C7.55635 2.25 2.33301 7.2868 2.33301 13.5C2.33301 17.2229 4.20832 20.5234 7.09774 22.5711C9.28849 24.1629 12.3745 26.672 13.0951 30.7071C13.1745 31.1518 13.5479 31.4966 13.9997 31.4966C14.4514 31.4966 14.8249 31.1518 14.9043 30.7071C15.6248 26.6719 18.7109 24.1629 20.9016 22.5711Z" fill="#FFC0CB"/>
<path d="M20.9016 22.5711L20.3234 21.7551L20.3138 21.7621L20.9016 22.5711ZM7.09774 22.5711L7.6856 21.762L7.67595 21.7552L7.09774 22.5711ZM13.0951 30.7071L12.1106 30.8829L12.1106 30.8829L13.0951 30.7071ZM14.9043 30.7071L15.8887 30.8829L15.8887 30.8829L14.9043 30.7071ZM21.4799 23.3869C24.6146 21.1654 26.6663 17.57 26.6663 13.5H24.6663C24.6663 16.8757 22.9675 19.8814 20.3234 21.7552L21.4799 23.3869ZM26.6663 13.5C26.6663 6.70055 20.9607 1.25 13.9997 1.25V3.25C19.9253 3.25 24.6663 7.87305 24.6663 13.5H26.6663ZM13.9997 1.25C7.03866 1.25 1.33301 6.70055 1.33301 13.5H3.33301C3.33301 7.87305 8.07405 3.25 13.9997 3.25V1.25ZM1.33301 13.5C1.33301 17.57 3.38476 21.1654 6.51953 23.387L7.67595 21.7552C5.03189 19.8814 3.33301 16.8758 3.33301 13.5H1.33301ZM14.0795 30.5313C13.2833 26.0726 9.88416 23.3596 7.68556 21.7621L6.50992 23.3801C8.69282 24.9662 11.4657 27.2714 12.1106 30.8829L14.0795 30.5313ZM13.9997 30.4966C14.0152 30.4966 14.0312 30.4997 14.0451 30.5052C14.0584 30.5105 14.0669 30.5166 14.0715 30.5207C14.076 30.5246 14.0778 30.5275 14.0784 30.5287C14.0792 30.53 14.0794 30.5309 14.0795 30.5313L12.1106 30.8829C12.2663 31.7544 13.0143 32.4966 13.9997 32.4966V30.4966ZM13.9199 30.5313C13.9199 30.5309 13.9202 30.53 13.9209 30.5287C13.9216 30.5275 13.9234 30.5246 13.9278 30.5207C13.9325 30.5166 13.9409 30.5105 13.9542 30.5052C13.9682 30.4997 13.9842 30.4966 13.9997 30.4966V32.4966C14.9851 32.4966 15.7331 31.7544 15.8887 30.8829L13.9199 30.5313ZM20.3138 21.7621C18.1152 23.3596 14.716 26.0726 13.9199 30.5313L15.8887 30.8829C16.5336 27.2713 19.3066 24.9662 21.4895 23.38L20.3138 21.7621Z" fill="url(#paint0_linear_740_3717)" mask="url(#path-2-outside-1_740_3717)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M10.2105 6C9.81968 6 9.44484 6.16389 9.16847 6.45561C8.8921 6.74733 8.73684 7.143 8.73684 7.55556V18.4444C8.54142 18.4444 8.354 18.5264 8.21582 18.6722C8.07763 18.8181 8 19.0159 8 19.2222C8 19.4285 8.07763 19.6263 8.21582 19.7722C8.354 19.9181 8.54142 20 8.73684 20H17.5789C17.7744 20 17.9618 19.9181 18.1 19.7722C18.2382 19.6263 18.3158 19.4285 18.3158 19.2222C18.3158 19.0159 18.2382 18.8181 18.1 18.6722C17.9618 18.5264 17.7744 18.4444 17.5789 18.4444V14.5556H18.3158V16.5C18.3158 17.0157 18.5099 17.5103 18.8553 17.8749C19.2008 18.2396 19.6693 18.4444 20.1579 18.4444C20.6465 18.4444 21.115 18.2396 21.4605 17.8749C21.8059 17.5103 22 17.0157 22 16.5V11.7664C21.9999 11.3539 21.8446 10.9583 21.5682 10.6667L19.5736 8.56122C19.5056 8.48694 19.4243 8.42768 19.3344 8.38692C19.2445 8.34616 19.1478 8.3247 19.05 8.32381C18.9521 8.32291 18.8551 8.34259 18.7646 8.38169C18.674 8.4208 18.5917 8.47855 18.5226 8.55158C18.4534 8.62461 18.3987 8.71145 18.3616 8.80704C18.3246 8.90262 18.3059 9.00504 18.3068 9.10831C18.3076 9.21158 18.3279 9.31364 18.3666 9.40854C18.4052 9.50343 18.4613 9.58925 18.5317 9.661L19.3422 10.5173C19.1791 10.7488 19.0803 11.0235 19.0567 11.3108C19.033 11.5982 19.0856 11.8868 19.2085 12.1444C19.3313 12.402 19.5197 12.6184 19.7524 12.7695C19.9851 12.9206 20.2531 13.0004 20.5263 13L20.5271 16.5C20.5271 16.6031 20.4882 16.7021 20.4191 16.775C20.3501 16.8479 20.2563 16.8889 20.1586 16.8889C20.0609 16.8889 19.9672 16.8479 19.8981 16.775C19.829 16.7021 19.7902 16.6031 19.7902 16.5V14.5556C19.7902 14.143 19.6349 13.7473 19.3586 13.4556C19.0822 13.1639 18.7074 13 18.3165 13H17.5797V7.55556C17.5797 7.143 17.4244 6.74733 17.1481 6.45561C16.8717 6.16389 16.4968 6 16.106 6H10.2105ZM16.1053 11.4444V7.55556H10.2105V11.4444H16.1053Z" fill="white"/>
<defs>
<filter id="filter0_f_740_3717" x="6" y="25" width="12" height="8" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="1" result="effect1_foregroundBlur_740_3717"/>
</filter>
<linearGradient id="paint0_linear_740_3717" x1="13.9997" y1="2.25" x2="13.9997" y2="31.4966" gradientUnits="userSpaceOnUse">
<stop stop-color="#626262"/>
<stop offset="1" stop-color="#626262"/>
</linearGradient>
</defs>
</svg>
