import { CommonModule } from '@angular/common';
import { Component, ElementRef, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatOptionModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatDialogModule } from '@angular/material/dialog';
import { MatDividerModule } from '@angular/material/divider';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';  // Import MatInputModule
import { MatSelectModule } from '@angular/material/select';
import { Router } from '@angular/router';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { EditorComponent } from '../../shared/editor/editor.component';

@Component({
  selector: 'app-add-news',
  standalone: true,
  imports: [
  CommonModule, 
  TranslateModule,
  MatIconModule,
  MatDialogModule,
  ReactiveFormsModule,
  MatFormFieldModule,
  MatInputModule,
  MatButtonModule,
  MatDatepickerModule,
  MatSelectModule,
  MatOptionModule,
  MatDividerModule,
  MatCardModule,
  EditorComponent
  ],
  templateUrl: './add-news.component.html',
  styleUrl: './add-news.component.css'
})
export class AddNewsComponent {
  @ViewChild('fileInput') fileInput: ElementRef | undefined;


  addNewsForm!: FormGroup;
  protected content = '';
  protected imageSrc: string | ArrayBuffer | null = null; // To hold the before image source

  constructor(private translate: TranslateService, private fb: FormBuilder, private router: Router) {
    this.addNewsForm = this.fb.group({
      heading: ['', [Validators.maxLength(60)]],
      templateContent: ['']
    });
  }

  get form() {
    return this.addNewsForm.controls;
  }

  onSubmit() {
  console.log(this.content)
  }

  onChangeContent() {
    const templateContentControl = this.addNewsForm.get('templateContent');
    if (templateContentControl) {
      templateContentControl.setValue(this.content);
    }
  }

  openFileDialog() {
      if (this.fileInput) {
        this.fileInput.nativeElement.click();
      }
  }

  onFileSelected(event: Event) {
    const fileInput = event.target as HTMLInputElement;
    if (fileInput.files && fileInput.files[0]) {
      const file = fileInput.files[0];
      const reader = new FileReader();

      reader.onload = (e: any) => {
          this.imageSrc = e.target.result; // Set the image source for before image
      };

      reader.readAsDataURL(file); // Read the file as a data URL
    }
  }

  removePhoto() {
      this.imageSrc = null; // Clear the before image source
      if (this.fileInput) {
        this.fileInput.nativeElement.value = ''; // Clear the file input for before image
      }
  }

  onBack() {
    this.router.navigate(['/news']);
  }
}
