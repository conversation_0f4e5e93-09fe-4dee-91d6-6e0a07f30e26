.login-container {
    position: absolute;
    top: 47%;
    left: 40%;
    transform: translate(-50%, -50%);
    width: 60vw;
    border-radius: 10px;
  }
  
  .login-container h1{
    text-align: center;
    font-size: 2em;
    margin-top: 20px;
    margin-bottom: 50px;
    font-family: 'Roboto';
    font-size: 32px;
    font-weight: 700;
    line-height: 16px;
  }
  
  .login-container form{
    display: flex;
    height: 500px;
  }
  
  .error-snackbar {
    background-color: #f44336; /* Red color for errors */
    color: #ffffff;
  }
  
  .login-form {
    width: 50%;
    background-color: white;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    padding: 50px;
  }
  
  .login-image {
    width: 50%;
    background-image: url('../../assets/images/login/smart-city.png');
    background-size: cover;
    background-position: center;
  }
  
  .login-image-overlay {
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 50px 0  50px 50px;
    height: 100%;
    height: 100%;
    display: flex;
    align-items: center;
  
  }
  
  .login-image-texts {
    text-align: left;
    font-size: 1.2em;
  }
  
  .login-image-texts > div{
    display: flex;
  }
  
  .login-image-texts > div > img{
    width: auto;
    height: 18px;
    margin-right: 10px;
  }
  
  .mat-mdc-form-field{
    width: 100%;
  }
  
  .mat-mdc-raised-button:not(:disabled){
    background-color: var(--primary-color);
    width: 100%;
  }
  
  .login-checkbox-remember{
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 5px;
  }
  
  .login-checkbox-remember > mat-checkbox span {
    font-family: 'Roboto';
    font-size: 12px;
    font-weight: 700;
    line-height: 16px;
    color: var(--secondary-color);
  }
  
  .login-checkbox-remember > .forgot-password{
    font-family: Roboto;
    font-size: 12px;
    font-weight: 700;
    line-height: 16px;
    color: #0070E0;
    text-decoration: none;
  }
  
  .login-image-texts p{
    font-family: 'Roboto';
    font-size: 18px;
    font-weight: 400;
    line-height: 16px;
    margin-bottom: 25px
  }