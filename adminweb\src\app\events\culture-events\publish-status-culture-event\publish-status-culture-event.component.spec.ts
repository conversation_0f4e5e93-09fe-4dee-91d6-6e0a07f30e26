import { ComponentFixture, TestBed } from '@angular/core/testing';

import { PublishStatusCultureEventComponent } from './publish-status-culture-event.component';

describe('PublishStatusCultureEventComponent', () => {
  let component: PublishStatusCultureEventComponent;
  let fixture: ComponentFixture<PublishStatusCultureEventComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [PublishStatusCultureEventComponent]
    })
    .compileComponents();

    fixture = TestBed.createComponent(PublishStatusCultureEventComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
