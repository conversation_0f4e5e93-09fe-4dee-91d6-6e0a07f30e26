
.contentPanel{
    padding: 2px;
    width: 100%;
    border-top: 8px solid var(--primary-color);
    justify-content: space-between;
    border-bottom: 1px solid #ccc;
  }
  
  .panelHead{
    margin: 20px;
    display: flex;
    align-items: center;
  }

  .heading{
    position: absolute;
    font-family: Roboto;
    font-size: 14px;
    font-weight: 700;
    text-align: center;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;
    color: #626262;
   margin-bottom: 80px;
  }

  .divider{
    position: absolute;
    width: 100%;
    margin-bottom: 40px;
  }

  .content{
    white-space: pre-line; /* Preserves line breaks but collapses extra spaces */
    margin-top: 75%;
    width: 250px;
    height: 39%;
    flex: 1;
    overflow: auto;
    position: absolute;
    font-family: Roboto;
    font-size: 12px;
    font-weight: 300;
    text-align: justify;
    text-underline-position: from-font;
    text-decoration-skip-ink: none; 
    color: #626262;   
  }

  .contentImage{
   position: absolute;
   width: 275px;
   margin-bottom: 135%;
  }

  .dateImage{
    margin-bottom: 180px;
    margin-left: 63px;
    position: absolute;
  }

  .dateImageSrc{
  position: absolute;
  z-index: 1;
  width: 103px !important;
  height: 40px !important;
  }

  .dateText{
  position: absolute;
  z-index: 2;
  font-family: Roboto;
  font-size: 10px;
  font-weight: 700;
  line-height: 12px;
  text-align: left;
  text-underline-position: from-font;
  text-decoration-skip-ink: none;
  color: #626262;
  margin-top: 11px;
  margin-left: 20px;
  }

  .statusImage{
  position: absolute;
  margin-left: 50px;
  }

  .statusImageSrc{
    position: absolute;
    z-index: 1;
    width: 103px !important;
    height: 18px !important;
    }
  
    .statusText{
    position: absolute;
    z-index: 2;
    width: 95.58px;
    height: 12px;
    opacity: 0px;
    font-family: Roboto;
    font-size: 12px;
    font-weight: 300;
    line-height: 12px;
    text-align: left;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;
    color: #626262;
    margin-left: 4px;
    margin-top: 2px;
    }

  .rounder{
    clip-path: ellipse(50% 40% at 50% 0%);
  }

  .contentImageSrc{
    width: 276px !important; 
    height: 155px !important;
    margin-top: 55px;
    /*clip-path: polygon(17% 8%, 0 0, 0 49%, 0% 80%, 0 99%, 50% 100%, 100% 100%, 100% 80%, 100% 49%, 100% 0, 86% 8%, 49% 13%)*/
  }

  .headerImageSrc{
    width: 275px !important;
    height: 93px !important;
    margin-bottom: 100px !important;
    position: absolute;
  }

  .h1{
    text-align: center;
    color: var(--color-main);
    width: 100%;
    border-bottom: 1px solid var(--color-gray-border) !important;
    margin: 0;
    font-family: 'Roboto', sans-serif;
    font-size: 20px;
    font-weight: 700;
    line-height: 16px;
  }
  
  .close-icon{
      cursor: pointer;
      font-size: 35px;
      border: none;
      background-color: transparent;
  }

 body {
 background-color: #efeeee;
 min-height: 80vh;
 display: flex;
 justify-content: center;
 align-items: center;
 }

 .main {
  width: 285px;
  height: 600px;
  background-color: #333;
  border-radius: 30px;
  position: absolute;
 }

 .main .power-button {
  height: 50px;
  width: 30px;
  margin-left: 257px;
  border-radius: 10px;
  margin-top: 120px;
  background-color: #333;
 }

 .main .volume-button {
  height: 80px;
  width: 30px;
  margin-left: 257px;
  border-radius: 10px;
  margin-top: 10px;
  background-color: #333;
 }

 .main .display {
  width: 275px;
  margin: 12px 5px;
  height: 580px;
  background-color: #ffffff;
  border-radius: 20px;
  position: absolute;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
 }

 .main .camera {
 width: 20px;
 height: 20px;
 border-radius: 50%;
 position: absolute;
 background-color: #333;
 margin-left: 45%;
 margin-top: 20px;
 z-index: 99;
 display: flex;
 justify-content: center;
 align-items: center;
 }

 .main .camera .focus {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: linear-gradient(#9d9a9a, #0d0707);
 }

 .main .display img {
  width: 275px;
  height: 585px;
  }

  
  .star-icon {
    position: absolute;
    top: 80px;
    right: 6px;
    color: gray;
    font-size: 24px;
  }

 