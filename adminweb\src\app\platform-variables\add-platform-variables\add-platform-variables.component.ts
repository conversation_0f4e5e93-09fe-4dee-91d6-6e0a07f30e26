import { Component, Inject } from '@angular/core';
import { <PERSON><PERSON><PERSON><PERSON>, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { UsersService } from '../../core/services/user.service';
import { MessageService } from '../../core/services/message.service';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { Validators } from 'ngx-editor';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatSelectModule } from '@angular/material/select';
import { MatOptionModule } from '@angular/material/core';
import { AuthService } from '../../core/services/auth.service';
import { PlatformVariablesService } from '../../core/services/platform-variables.service';

@Component({
  selector: 'app-add-platform-variables',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    MatIconModule,
    MatDialogModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatDatepickerModule,
    MatSelectModule,
    MatOptionModule
  ],
  templateUrl: './add-platform-variables.component.html',
  styleUrl: './add-platform-variables.component.css'
})
export class AddPlatformVariablesComponent {
  platformVariablesCreateForm!: FormGroup;

  protected platformVariablesTypes: string[] = []; // Initialize as an empty array or as needed

  constructor(private translate: TranslateService, private fb: FormBuilder, private platformVariablesService: PlatformVariablesService,
    private messageService: MessageService,
    private dialogRef: MatDialogRef<AddPlatformVariablesComponent>, @Inject(MAT_DIALOG_DATA) public data: any,) {
    this.platformVariablesCreateForm = this.fb.group({
      name: ['', Validators.required],
      value: [''],
      type: ['', Validators.required],
    });
  }

  ngOnInit(): void {
    this.platformVariablesTypes = this.data.types
  }

  get form() {
    return this.platformVariablesCreateForm.controls;
  }

  onSubmit() {
    if (this.platformVariablesCreateForm.valid) {
      const platformVariableData = this.platformVariablesCreateForm.value;
      this.platformVariablesService.addPlatformVariable(platformVariableData).subscribe({
        next: (response) => {
          this.messageService.showMessage(["CreateSuccessfully"], 'success');
          
          // Handle successful response, e.g., show a success message or navigate to another page
           // Close the dialog after successful addition
           this.dialogRef.close(platformVariableData);
        },
        error: (error) => {
          if (error.error instanceof ErrorEvent) {
            // Client-side error
            console.error('Client-side error:', error.error.message);
          } else {
            // Server-side error
            console.error(`Server returned code: ${error.status}, body was: ${error.error}`);
          }
        },
        complete: () => {
          
          // Handle completion of the observable if needed
        }
      });
    } else {
      
    }
  }
}
