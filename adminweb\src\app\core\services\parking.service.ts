import { Inject, Injectable } from '@angular/core';
import { BehaviorSubject, Observable, of } from 'rxjs';
import { HttpClient, HttpParams } from '@angular/common/http';
import { ParkingGetModel } from '../../shared/interfaces/parking/parking-get.model';
import { environment } from '../../../environments/enviroment';
import { CreateParkingModel } from '../../shared/interfaces/parking/parking-create-model';
import { EditParkingModel } from '../../shared/interfaces/parking/parking-edit.model';

@Injectable({
  providedIn: 'root'
})
export class ParkingService {
  private parkingDataSubject = new BehaviorSubject<any>(null);
  parkingData$ = this.parkingDataSubject.asObservable();

  private apiUrl = 'https://localhost:44347/Manufacturers'; // Replace with your actual backend URL

  constructor(private http: HttpClient) {}

   getParkings(
    name:             string | null,
    sortColumn:       string | null,
    sortDirection:    string | null,
    pageNumber:       number,
    pageSize:         number,
  ): Observable<ParkingGetModel> {
    return this.http.get<ParkingGetModel>(`${environment.apiUrl}/ParkingLots/Admin`, {
      params: new HttpParams()
      .set('name', name ? name : '')
      .set('sortColumn', sortColumn ? sortColumn : '')
      .set('sortDirection', sortDirection ? sortDirection : '')
      .set('pageNumber', pageNumber.toString())
      .set('pageSize', pageSize.toString())
    });
  }

  addParking(parking: CreateParkingModel): Observable<CreateParkingModel>  {
    return this.http.post<CreateParkingModel>(`${environment.apiUrl}/ParkingLots`, parking);
  }

  updateParking(parking: EditParkingModel) {
    return this.http.put(`${environment.apiUrl}/ParkingLots/${parking.id}`, parking);
  }

  deleteParking(parkingId: number) {
    return this.http.delete<number>(`${environment.apiUrl}/ParkingLots/${parkingId}`)
  }


}