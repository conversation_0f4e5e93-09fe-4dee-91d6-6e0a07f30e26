import { Component, Inject } from '@angular/core';
import { <PERSON><PERSON><PERSON><PERSON>, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { UsersService } from '../../core/services/user.service';
import { MessageService } from '../../core/services/message.service';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { Validators } from 'ngx-editor';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatSelectModule } from '@angular/material/select';
import { MatOptionModule } from '@angular/material/core';
import { AuthService } from '../../core/services/auth.service';
import { PlatformVariablesService } from '../../core/services/platform-variables.service';

@Component({
  selector: 'app-edit-platform-variables',
  standalone: true,
  imports: [
  CommonModule,
  TranslateModule,
  MatIconModule,
  MatDialogModule,
  ReactiveFormsModule,
  MatFormFieldModule,
  MatInputModule,
  MatButtonModule,
  MatDatepickerModule,
  MatSelectModule,
  MatOptionModule
  ],
  templateUrl: './edit-platform-variables.component.html',
  styleUrl: './edit-platform-variables.component.css'
})
export class EditPlatformVariablesComponent {
  platformVariablesEditForm!: FormGroup;

  protected platformVariablesTypes: string[] = []; // Initialize as an empty array or as needed

  constructor(private translate: TranslateService, private fb: FormBuilder, private platformVariablesService: PlatformVariablesService,
    private messageService: MessageService,
    private dialogRef: MatDialogRef<EditPlatformVariablesComponent>, @Inject(MAT_DIALOG_DATA) public data: any,) {
    this.platformVariablesEditForm = this.fb.group({
      id: [this.data.id],
      name: [this.data.name, Validators.required],
      value: [this.data.value],
      type: [this.data.type, Validators.required],
    });
  }

  ngOnInit(): void {
    this.platformVariablesTypes = this.data.types
  }

  get form() {
    return this.platformVariablesEditForm.controls;
  }

  onSubmit() {
    if (this.platformVariablesEditForm.valid) {
      const platformVariableData = this.platformVariablesEditForm.value;
      this.platformVariablesService.editPlatformVariable(platformVariableData).subscribe({
        next: (response) => {
          this.messageService.showMessage(["CreateSuccessfully"], 'success');
          
          // Handle successful response, e.g., show a success message or navigate to another page
           // Close the dialog after successful addition
           this.dialogRef.close(platformVariableData);
        },
        error: (error) => {
          if (error.error instanceof ErrorEvent) {
            // Client-side error
            console.error('Client-side error:', error.error.message);
          } else {
            // Server-side error
            console.error(`Server returned code: ${error.status}, body was: ${error.error}`);
          }
        },
        complete: () => {
          
          // Handle completion of the observable if needed
        }
      });
    } else {
      
    }
  }
}
