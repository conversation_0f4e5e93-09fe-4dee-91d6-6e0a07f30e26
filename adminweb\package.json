{"name": "adminweb", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@4sellers/angular-material-components-datetime-picker": "^18.0.0", "@angular/animations": "^18.0.0", "@angular/cdk": "^18.2.13", "@angular/common": "^18.0.0", "@angular/compiler": "^18.0.0", "@angular/core": "^18.0.0", "@angular/forms": "^18.0.0", "@angular/google-maps": "^17.3.10", "@angular/material": "^18.2.13", "@angular/platform-browser": "^18.0.0", "@angular/platform-browser-dynamic": "^18.0.0", "@angular/router": "^18.0.0", "@auth0/angular-jwt": "^5.2.0", "@googlemaps/js-api-loader": "github:googlemaps/js-api-loader", "@ngx-translate/core": "^15.0.0", "@ngx-translate/http-loader": "^8.0.0", "bootstrap": "^5.3.3", "jwt-decode": "^4.0.0", "material-icons": "^1.13.12", "ngx-editor": "^15.3.0", "prosemirror-commands": "^1.5.0", "prosemirror-model": "^1.19.0", "prosemirror-schema-list": "^1.2.2", "prosemirror-state": "^1.4.2", "prosemirror-transform": "^1.7.3", "prosemirror-view": "^1.30.1", "rxjs": "~7.8.0", "tslib": "^2.8.1", "zone.js": "~0.14.3"}, "devDependencies": {"@angular-devkit/build-angular": "^18.0.4", "@angular/cli": "^18.0.4", "@angular/compiler-cli": "^18.0.0", "@types/jasmine": "~5.1.0", "jasmine-core": "~5.1.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.4.2"}}